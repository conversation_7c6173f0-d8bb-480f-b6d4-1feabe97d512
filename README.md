### Cấu trúc <PERSON>h<PERSON> mục:
1. **Modules**: Tổ chức ứng dụng thành các module tính năng.
  - Mỗi module tính năng nên chứa các thành phần như component, directive và service liên quan đến tính năng đó.
  - Tránh đặt tất cả mọi thứ trong `AppModule`. Thay vào đó, hãy tạo các module tính năng riêng biệt cho các phần khác nhau của ứng dụng.
2. **Shared**: Đặt các thành phần có thể tái sử dụng như component, directive, pipe và service ở đây.
3. **Core**: Lưu trữ các service singleton, các component chỉ sử dụng một lần trên toàn ứng dụng và các tài nguyên khác được chia sẻ trên toàn ứng dụng.
4. **Assets**: <PERSON><PERSON> gồm các tệp tĩnh như hình ảnh, font chữ và các kiểu dáng toàn cầu.
5. **Environment**: Lưu trữ các cấu hình cụ thể cho môi trường.

### Quy tắc Đặt tên:
1. **Files và Components**: Sử dụng kebab-case cho tên tệp và PascalCase cho tên lớp component.
  - Ví dụ: `my-component.component.ts` và `MyComponentComponent`.
2. **Services và Directives**: Sử dụng camelCase.
  - Ví dụ: `myService` và `myDirective`.

### Lazy Loading:
1. **Feature Modules**: Sử dụng lazy loading để tải các module chỉ khi cần thiết.
  - Sử dụng phương thức `RouterModule.forChild()` để xác định các route trong các module tính năng.
2. **Cấu hình Route**: Cấu hình route để tải các module tính năng theo cách lười biếng.
  - Ví dụ:
    ```typescript
    {
      path: 'lazy',
      loadChildren: () => import('./lazy/lazy.module').then(m => m.LazyModule)
    }
    ```

### Quản lý Trạng thái:
1. **NgRx/Redux**: Đối với quản lý trạng thái phức tạp, xem xét sử dụng NgRx.
  - NgRx cung cấp một mẫu container trạng thái dự đoán để quản lý trạng thái trong các ứng dụng Angular.

### Lưu trữ Trình Duyệt:

#### 1. LocalStorage và SessionStorage:
LocalStorage và SessionStorage là hai phương tiện lưu trữ dữ liệu cục bộ trên trình duyệt.

- **LocalStorage**: Lưu trữ dữ liệu trên trình duyệt với tính chất lưu trữ lâu dài, tồn tại ngay cả khi trình duyệt được đóng và mở lại.

- **SessionStorage**: Dữ liệu chỉ tồn tại trong phiên làm việc hiện tại của trình duyệt và bị xóa khi phiên làm việc kết thúc hoặc trình duyệt được đóng.

#### 2. Cookies:
Cookies là các tệp văn bản nhỏ được lưu trữ trên máy tính của người dùng thông qua trình duyệt. Chúng thường được sử dụng để lưu trữ thông tin nhất thời hoặc nhận dạng người dùng.

#### 3. IndexedDB:
IndexedDB là một cơ sở dữ liệu trên trình duyệt được sử dụng để lưu trữ dữ liệu có cấu trúc hoặc lớn.

- **IndexedDB** cung cấp một giao diện lập trình ứng dụng (API) mạnh mẽ cho việc thêm, truy vấn và xóa dữ liệu.

### Xử lý Lỗi:
1. **Bộ xử lý Lỗi Toàn cầu**: Thực hiện một bộ xử lý lỗi toàn cầu để bắt các ngoại lệ chưa được xử lý.
  - Cung cấp một nơi tập trung cho việc ghi nhật ký và hiển thị lỗi cho người dùng.
### Thực Hành Lập Trình:
1. **TypeScript**: Sử dụng các tính năng TypeScript để đảm bảo an toàn kiểu và dễ bảo trì hơn.
2. **RxJS**: Học và hiểu nguyên tắc lập trình phản ứng để xử lý các hoạt động bất đồng bộ.
3. **Phân Tách Mã**: Chia nhỏ các component lớn thành các component nhỏ hơn để cải thiện khả năng tái sử dụng và bảo trì.
4. **Sử dụng Angular CLI**: Tận dụng các lệnh Angular CLI để tạo ra các component, module, service, v.v.
  - Ví dụ: `ng generate component my-component`.

### Kiểm Thử:
1. **Kiểm Thử Đơn Vị**: Viết các kiểm thử đơn vị cho các component, service và directive bằng các công cụ như Jasmine và Karma.
2. **Kiểm Thử Tích Hợp**: Thực hiện các kiểm thử tích hợp với các công cụ như Protractor hoặc Cypress.

### Bảo Mật:
1. **Sanitize**: Lọc các đầu vào và đầu ra để ngăn chặn các cuộc tấn công XSS.
2. **Xác Thực và Phân Quyền**: Thực hiện các cơ chế xác thực và phân quyền an toàn.

### Tối Ưu Hiệu Năng:
1. **Lazy Loading**: Tối ưu hiệu năng bằng cách tải các module theo cách lười biếng.
2. **Tree Shaking**: Sử dụng Angular CLI cho các bản build sản xuất để loại bỏ mã không sử dụng.
3. **Biên Dịch AOT**: Biên dịch trước giúp giảm kích thước bundle và cải thiện hiệu suất khởi động.

### Tích hợp Microfrontend vào hai dự án Angular sử dụng @angular-architects/module-federation

## Dự án base

Cài đặt thư viện `@angular-architects/module-federation`
```javascript
const { ModuleFederationPlugin } = require('@angular-architects/module-federation/webpack');
const path = require('path');

module.exports = {
  entry: './src/main.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'main.js',
  },
  plugins: [
    new ModuleFederationPlugin({
      remotes: {
        'remote1': 'remote1@http://localhost:3000/remoteEntry.js',
        // Thêm các microfrontend khác nếu cần
      },
    }),
  ],
};
```
## Dự án microfrontend cần thực hiện để tích hợp vào dự án base
Cài đặt thư viện `@angular-architects/module-federation`
```javascript
const { ModuleFederationPlugin } = require('@angular-architects/module-federation/webpack');
const path = require('path');

module.exports = {
  entry: './src/main.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'remoteEntry.js',
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'remote1',
      filename: 'remoteEntry.js',
      exposes: {
        './Module': './src/app/remote1-feature1/remote1-feature1.module.ts',
      },
    }),
  ],
};
```


```
project-name/
│
├── projects/                   # Thư mục chứa các lib projects của ứng dụng
│   ├── my-library/             # Thư mục chứa lib project "my-library"
│   │   ├── src/                # Thư mục chứa mã nguồn của lib project
│   │   ├── README.md           # Tệp README của lib project "my-library"
│   │   └── ...                 # Các tệp khác của lib project "my-library" (tùy chọn)
│   └── another-library/        # Thư mục chứa lib project "another-library"
│       ├── src/                # Thư mục chứa mã nguồn của lib project
│       ├── README.md           # Tệp README của lib project "another-library"
│       └── ...                 # Các tệp khác của lib project "another-library" (tùy chọn)
├── src/
│   │
│   ├── app/
│   │   │
│   │   ├── core/               # Thư mục chứa các phần cốt lõi của ứng dụng
│   │   │   ├── services/       # Thư mục chứa các dịch vụ cốt lõi
│   │   │   │   ├── auth.service.ts   # Service xác thực người dùng
│   │   │   │   ├── data.service.ts   # Service tương tác với dữ liệu
│   │   │   │   ├── logger.service.ts # Service ghi log
│   │   │   │   └── common.service.ts # Service chứa các phương thức thông dụng
│   │   │   │   └── keycloak.service.ts # Service giao tiếp với Keycloak
│   │   │   ├── store/           # Thư mục chứa các tệp liên quan đến ngrx store
│   │   │   │   ├── actions/     # Thư mục chứa các actions
│   │   │   │   │   ├── auth.actions.ts   # Actions liên quan đến xác thực
│   │   │   │   │   └── ...      
│   │   │   │   ├── reducers/    # Thư mục chứa các reducers
│   │   │   │   │   ├── auth.reducer.ts  # Reducer liên quan đến xác thực
│   │   │   │   │   └── ...
│   │   │   │   ├── effects/     # Thư mục chứa các effects
│   │   │   │   │   ├── auth.effects.ts # Effects liên quan đến xác thực
│   │   │   │   │   └── ...
│   │   │   ├── guards/         # Thư mục chứa các guards
│   │   │   │   ├── auth.guard.ts    # Guard bảo vệ các route cần xác thực
│   │   │   │   └── admin.guard.ts  # Guard bảo vệ các route chỉ dành cho quản trị viên
│   │   │   │
│   │   │   └── interceptors/   # Thư mục chứa các interceptors
│   │   │       ├── auth.interceptor.ts # Interceptor thêm token xác thực vào các request
│   │   │       └── logging.interceptor.ts # Interceptor ghi log cho các request
│   │   │
│   │   ├── modules/            # Thư mục chứa các module tính năng
│   │   │   ├── feature1/       # Thư mục chứa module tính năng 1
│   │   │   └── feature2/       # Thư mục chứa module tính năng 2
│   │   │
│   │   ├── shared/             # Thư mục chứa các thành phần được chia sẻ
│   │   │   ├── pages/          # Thư mục chứa các trang chung của ứng dụng
│   │   │   │   ├── not-found/  # Thư mục chứa trang 404
│   │   │   │   │   ├── not-found.component.html   # Template HTML của trang 404
│   │   │   │   │   ├── not-found.component.ts     # File TypeScript của trang 404
│   │   │   │   │   └── not-found.component.scss   # File SCSS của trang 404
│   │   │   ├── components/     # Thư mục chứa các component được chia sẻ
│   │   │   │   ├── ...
│   │   │   │   └── ...
│   │   │   ├── directives/     # Thư mục chứa các directives được chia sẻ
│   │   │   │   ├── highlight.directive.ts # Directive thay đổi màu nền khi hover
│   │   │   │   ├── input.directive.ts     # Directive truyền dữ liệu vào phần tử DOM
│   │   │   │   ├── autocomplete.directive.ts # Directive thêm tính năng autocomplete vào input
│   │   │   │   ├── draggable.directive.ts    # Directive cho phép kéo thả phần tử
│   │   │   │   └── click-outside.directive.ts # Directive phát hiện khi click bên ngoài phần tử
│   │   │   ├── pipes/          # Thư mục chứa các pipes được chia sẻ
│   │   │   │   ├── date-format.pipe.ts # Pipe định dạng ngày tháng
│   │   │   │   ├── capitalize.pipe.ts  # Pipe chuyển đổi chữ cái đầu thành chữ hoa
│   │   │   │   ├── truncate.pipe.ts    # Pipe cắt ngắn chuỗi
│   │   │   │   ├── currency.pipe.ts    # Pipe định dạng tiền tệ
│   │   │   │   └── ...
│   │   │   ├── services/       # Thư mục chứa các services được chia sẻ
│   │   │   │   ├── api.service.ts   # Service giao tiếp với API
│   │   │   │   ├── storage.service.ts # Service quản lý lưu trữ dữ liệu cục bộ
│   │   │   │   └── notification.service.ts # Service hiển thị thông báo cho người dùng
│   │   │   └── utils/          # Thư mục chứa các util chung cho toàn dự án
│   │   │       ├── utility1.ts # Ví dụ về một util chung
│   │   │       ├── utility2.ts # Ví dụ về một util chung
│   │   │       └── ...
│   │   │
│   │   ├── app.component.html  # Template HTML của component gốc của ứng dụng
│   │   ├── app.component.ts    # File TypeScript của component gốc của ứng dụng
│   │   └── app.routes.ts       # File cấu hình routing của ứng dụng
│   │
│   ├── environments/           # Thư mục chứa cấu hình môi trường
│   │   ├── environment.ts      # File cấu hình môi trường mặc định
│   │   └── environment.prod.ts # File cấu hình môi trường sản phẩm
│   ├── assets/                 # Thư mục chứa các tài nguyên tĩnh như hình ảnh, fonts, v.v.
│   │   ├── images/             # Thư mục chứa hình ảnh
│   │   ├── fonts/              # Thư mục chứa fonts
│   │   └── styles/             # Thư mục chứa các tệp CSS hoặc SCSS
│   │       ├── theme.scss      # Tệp chứa CSS hoặc SCSS cho theme
│   │       └── ...
│   │
│
├── angular.json               # Tệp cấu hình Angular CLI
├── tsconfig.json              # Tệp cấu hình TypeScript
├── package.json               # Tệp cấu hình npm
├── .eslintrc.json             # Tệp cấu hình linting (ESLint)
├── .prettierrc.json           # Tệp cấu hình Prettier
├── jest.config.js             # Tệp cấu hình testing (Jest)
├── karma.conf.js              # Tệp cấu hình testing (Karma)
├── webpack.config.js         # Tệp cấu hình Webpack
└── README.md                  # Tệp README của dự án


```

## Thư mục `core`

- **Chức năng**: Thư mục `core` thường chứa các thành phần và dịch vụ cốt lõi của ứng dụng, như các dịch vụ giao tiếp với máy chủ, những hàm tiện ích chung, các interceptor cho HTTP request, và các guard cho việc xác thực.

- **Sử dụng**: Các thành phần trong thư mục `core` thường được sử dụng trên toàn bộ ứng dụng. Chúng được import vào AppModule, nơi mà chúng sẽ được cung cấp global cho toàn bộ ứng dụng.

- **Ví dụ**: Một số ví dụ về thành phần trong thư mục `core` có thể bao gồm AuthService (dịch vụ đăng nhập), AuthGuard (guard cho việc xác thực), LoggerService (dịch vụ ghi log), và ErrorInterceptor (interceptor cho việc xử lý lỗi HTTP).

## Thư mục `shared`

- **Chức năng**: Thư mục `shared` chứa các thành phần và dịch vụ có thể được chia sẻ giữa nhiều thành phần khác nhau trong ứng dụng. Điều này có thể bao gồm các thành phần UI như các thanh công cụ, các thành phần form, hoặc các pipe, directive.

- **Sử dụng**: Các thành phần trong thư mục `shared` thường được sử dụng lại ở nhiều nơi trong ứng dụng. Chúng có thể được import vào các Module cụ thể mà chúng cần sử dụng.

- **Ví dụ**: Các ví dụ cho các thành phần trong thư mục `shared` có thể bao gồm các thành phần UI như NavbarComponent, FooterComponent, các directive như HighlightDirective, các pipe như FormatCurrencyPipe.

## Thư mục `services`

- **Chức năng**: Thư mục `services` thường chứa các dịch vụ (services) được sử dụng trong toàn bộ ứng dụng. Các dịch vụ này thường chứa logic xử lý dữ liệu hoặc logic kinh doanh và được sử dụng để chia sẻ dữ liệu hoặc chức năng giữa các thành phần khác nhau trong ứng dụng.

- **Sử dụng**: Các dịch vụ trong thư mục `services` có thể được inject vào các thành phần khác nhau trong ứng dụng như Components, Directives, hoặc other Services. Bằng cách này, chúng ta có thể tái sử dụng logic và dữ liệu một cách hiệu quả và tránh việc lặp lại mã.

- **Ví dụ**: Một số ví dụ về các dịch vụ trong thư mục `services` bao gồm ApiService (dịch vụ để giao tiếp với các API), DataService (dịch vụ để quản lý dữ liệu trong ứng dụng), và UtilityService (dịch vụ chứa các hàm tiện ích chung). Các dịch vụ này thường chứa các phương thức để thực hiện các tác vụ như lấy dữ liệu từ máy chủ, xử lý logic kinh doanh, hoặc chia sẻ dữ liệu giữa các thành phần khác nhau.

