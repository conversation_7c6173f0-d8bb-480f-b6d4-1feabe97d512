{"root": true, "ignorePatterns": ["projects/lib/*", "webpack.config.js"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json", "e2e/tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/component-selector": ["warn", {"prefix": "app", "style": "kebab-case", "type": "element"}], "@angular-eslint/directive-selector": ["warn", {"prefix": "app", "style": "camelCase", "type": "attribute"}], "@angular-eslint/no-output-on-prefix": 0, "@angular-eslint/no-empty-lifecycle-method": 0, "@angular-eslint/no-input-rename": 0}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {"@angular-eslint/template/eqeqeq": 0}}]}