import { Injectable } from '@angular/core'
import { Store } from '@ngrx/store'
import { Observable } from 'rxjs'
import { filter, map, take } from 'rxjs/operators'
import { initConfig, removeConfig, setConfig, updateConfig } from './config.actions'
import { ComponentConfig, ContainerConfig } from './config.model'
import { selectAll, selectCanRedo, selectCanUndo, selectItem } from './config.selectors'
import { Configurable } from '@mb/ngx-ui-builder/configurable'
import { Mutable } from '@mb/ngx-ui-builder/utils'
import { AppService } from '@services/app.service'
import { HttpClient } from '@angular/common/http'
import _ from 'lodash'

@Injectable({ providedIn: 'root' })
export class ConfigService {
  constructor(
    public store: Store,
    private httpClient: HttpClient,
    private appService: AppService
  ) {}

  public getComponentId(): string {
    const componentId = localStorage.getItem('currentComponentBuilder')
    if (!componentId) {
      console.error('Component ID is not set in localStorage')
      return ''
    }
    return componentId
  }

  /**
   * Initialize configuration assuming the store is empty.
   * No object is removed and the state history is not modified.
   * @param config list of configuration items to manage
   */
  public init(config: ComponentConfig[]) {
    const componentId = this.getComponentId()
    this.store.dispatch(initConfig({ componentId, config }))
  }

  /**
   * Set the configuration assuming the store is not empty.
   * All previously existing objects are removed and the state
   * history is reset.
   * @param config
   */
  public set(config: ComponentConfig[]) {
    const componentId = this.getComponentId()
    this.store.dispatch({ type: 'CLEAR' }) // Clear the previous history, as the previous actions won't work on new objects
    this.store.dispatch(setConfig({ componentId, config }))
  }

  /**
   * Watch any change in the configuration objects for a specific component.
   */
  public watchAllConfig(): Observable<ComponentConfig[]> {
    const componentId = this.getComponentId()
    return this.store.select(selectAll(componentId))
  }

  /**
   * @returns all current configuration for a specific component
   */
  public getAllConfig(): ComponentConfig[] {
    let config: ComponentConfig[] = []
    this.watchAllConfig()
      .pipe(take(1))
      .subscribe((c) => (config = c))
    return config
  }

  /**
   * Watch changes of one specific configuration object for a specific component
   */
  public watchConfig(id: string): Observable<ComponentConfig> {
    const componentId = this.getComponentId()
    this.getConfig(id) // Ensure a value exists (if 'id' has no config)
    return this.store.select(selectItem(componentId, id)).pipe(
      filter((config) => !!config),
      map((config: ComponentConfig) => JSON.parse(JSON.stringify(config)))
    )
  }

  private _getConfig(id: string): ComponentConfig | undefined {
    const componentId = this.getComponentId()
    let config: ComponentConfig | undefined
    this.store
      .select(selectItem(componentId, id))
      .pipe(take(1))
      .subscribe((c) => (config = c))
    return config
  }

  /**
   * @returns the current configuration of a specific item with the given id for a specific component
   */
  public getConfig(id: string): ComponentConfig {
    let config = this._getConfig(id)
    if (!config) {
      const componentId = this.getComponentId()
      config = { id, type: id }
      this.store.dispatch(initConfig({ componentId, config: [config] })) // Use init instead of add, because add is undoable
    }
    return JSON.parse(JSON.stringify(config)) // Deep copy
  }

  /**
   * @returns the current configuration of a specific container item with the given id for a specific component
   */
  public getContainer(id: string): ContainerConfig {
    const config = this.getConfig(id)
    if (!this.isContainerConfig(config)) {
      throw `${id} is not a container`
    }
    return config
  }

  /**
   * @returns true if the configuration with the given id is a container
   */
  public isContainer(id: string): boolean {
    return this.isContainerConfig(this._getConfig(id))
  }

  /**
   * @returns true if the given configuration is a container
   */
  public isContainerConfig(conf: ComponentConfig | undefined): conf is ContainerConfig {
    return conf?.type === '_container'
  }

  /**
   * Test whether a given component id is used within the hierarchy of a container
   */
  public isUsed(id: string) {
    return !!this.findParent(id)
  }

  /**
   * @returns the configuration of a container that includes the given id as a child item for a specific component
   */
  public findParent(id: string): ContainerConfig | undefined {
    return this.getAllConfig().find((item) => this.isContainerConfig(item) && (item?.items || []).includes(id)) as ContainerConfig | undefined
  }

  /**
   * Update the configuration of a given component or list of components for a specific component
   */
  public updateConfig(config: ComponentConfig | ComponentConfig[]) {
    const componentId = this.getComponentId()
    this.store.dispatch(updateConfig({ componentId, config }))
  }

  /**
   * Remove the configuration of a component with the given id for a specific component
   */
  public removeConfig(id: string) {
    const componentId = this.getComponentId()
    this.store.dispatch(removeConfig({ componentId, id }))
  }

  /**
   * @returns a new unique component id for the given component type for a specific component
   */
  public generateId(type: string) {
    let idx = 1
    let root = type.startsWith('_') ? type.slice(1) : type
    const tokens = type.split('-')
    if (tokens[tokens.length - 1].match(/\d+/)) {
      idx = +tokens[tokens.length - 1]
      root = tokens.slice(0, tokens.length - 1).join('-')
    }
    let id = root
    do {
      id = `${root}-${idx++}`
    } while (this._getConfig(id) || id === type)
    return id
  }

  /**
   * @returns an observable state for the possibility of undoing the last action
   */
  public canUndo$(): Observable<boolean> {
    return this.store.select(selectCanUndo())
  }

  /**
   * @returns an observable state for the possibility of redoing the next action
   */
  public canRedo$(): Observable<boolean> {
    return this.store.select(selectCanRedo())
  }

  /**
   * Undo the last action if possible
   */
  public undo() {
    this.canUndo$()
      .pipe(take(1))
      .subscribe((can) => can && this.store.dispatch({ type: 'UNDO' }))
  }

  /**
   * Redo the next action if possible
   */
  public redo() {
    this.canRedo$()
      .pipe(take(1))
      .subscribe((can) => can && this.store.dispatch({ type: 'REDO' }))
  }

  public exportConfiguration() {
    const componentName = this.getComponentId()
    const config = JSON.stringify(this.getAllConfig(), null, 2)
    var element = document.createElement('a')
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(config))
    element.setAttribute('download', `${componentName}.json`)
    element.style.display = 'none'
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  duplicate(context: Configurable) {
    const config: Mutable<ComponentConfig> = this.getConfig(context.id)
    config.id = this.generateId(config.id) // Generate a new config id
    if (context.parentId) {
      const container = this.getContainer(context.parentId)
      const index = container.items.findIndex((item) => item === context.id)
      if (index !== -1) {
        container.items.splice(index + 1, 0, config.id)
        this.updateConfig([config, container])
      }
    }
  }

  getConfigVersions(): Promise<any> {
    const componentName = this.getComponentId()
    return this.getConfigComponentName(componentName)
  }

  getConfigComponentName(componentName: string, pageSize = 1000): Promise<any> {
    return new Promise((resolve, reject) => {
      this.appService.getListConfigBuilder(0, pageSize, { isAll: true, componentName: componentName }).subscribe((res) => {
        resolve(res)
      })
    })
  }

  saveConfig(componentName = undefined, body = undefined) {
    if (!componentName || !body || !body?.length) return

    // không lưu value của formControl
    let dataSave = body.map((x) => {
      if (x.type === 'formControl') {
        return { ...x, data: { ...x.data, value: '' } }
      } else {
        return x
      }
    })

    dataSave = _.orderBy(dataSave, ['type'], ['asc'])

    // if (isDevMode()) {
    //   this.httpClient.post<any>(environment.urlConfigBuilder + '/save-config-builder', {
    //     component: componentName,
    //     data: dataSave
    //   })
    // }
    dataSave = this.removeAllPrefixesFromIdsAndTypes(body)
    return this.appService.createConfigBuilder({
      component: componentName,
      data: { config: dataSave }
    })
  }

  saveTemplateConfig(componentName = undefined, body = undefined) {
    // không lưu value của formControl
    let dataSave = body.type === 'formControl' ? { ...body, data: { ...body.data, value: '' } } : body

    // if (isDevMode()) {
    //   this.httpClient.post<any>(environment.urlConfigBuilder + '/save-config-builder', {
    //     component: componentName,
    //     data: dataSave
    //   })
    // }

    return this.appService.createConfigBuilder({
      component: componentName,
      data: { config: dataSave }
    })
  }

  addPrefixToIds(items, prefix) {
    prefix = prefix + '__'
    function addPrefix(item) {
      // Create a shallow copy of the item to avoid modifying read-only properties
      const newItem = { ...item }

      // Add prefix to the id if it doesn't already have it
      if (newItem.id && !newItem.id.startsWith(prefix)) {
        newItem.id = prefix + newItem.id
      }

      // Recursively process items array if it exists
      if (newItem.items && Array.isArray(newItem.items)) {
        newItem.items = newItem.items.map((subItem) => {
          if (typeof subItem === 'string') {
            return subItem.startsWith(prefix) ? subItem : prefix + subItem
          }
          return addPrefix(subItem)
        })
      }

      return newItem
    }

    // Process the top-level items
    return items.map((item) => addPrefix(item))
  }

  removeAllPrefixesFromIds(items) {
    function removePrefix(item) {
      // Tạo một bản sao nông của item để tránh việc chỉnh sửa các thuộc tính chỉ đọc
      const newItem = { ...item }

      // Xóa toàn bộ tiền tố trước dấu '__' nếu nó tồn tại
      if (newItem.id && newItem.id.includes('__')) {
        newItem.id = newItem.id.split('__').pop()
      }

      // Xử lý đệ quy mảng items nếu nó tồn tại
      if (newItem.items && Array.isArray(newItem.items)) {
        newItem.items = newItem.items.map((subItem) => {
          if (typeof subItem === 'string') {
            return subItem.includes('__') ? subItem.split('__').pop() : subItem
          }
          return removePrefix(subItem)
        })
      }

      return newItem
    }

    // Xử lý các items ở mức cao nhất
    return items.map((item) => removePrefix(item))
  }

  removeAllPrefixesFromIdsAndTypes(items) {
    function removePrefix(item) {
      // Tạo một bản sao nông của item để tránh việc chỉnh sửa các thuộc tính chỉ đọc
      const newItem = { ...item }

      // Xóa toàn bộ tiền tố trước dấu '__' nếu nó tồn tại trong id
      if (newItem.id && newItem.id.includes('__')) {
        newItem.id = newItem.id.split('__').pop()
      }

      // Xóa toàn bộ tiền tố trước dấu '__' nếu nó tồn tại trong type
      if (newItem.type && newItem.type.includes('__')) {
        newItem.type = newItem.type.split('__').pop()
      }

      // Xử lý đệ quy mảng items nếu nó tồn tại
      if (newItem.items && Array.isArray(newItem.items)) {
        newItem.items = newItem.items.map((subItem) => {
          if (typeof subItem === 'string') {
            return subItem.includes('__') ? subItem.split('__').pop() : subItem
          }
          return removePrefix(subItem)
        })
      }

      return newItem
    }

    // Xử lý các items ở mức cao nhất
    return items.map((item) => removePrefix(item))
  }

  buildPrefix(prefix: string, id: string) {
    if (prefix) {
      return `${prefix}__${id}`
    }
    return id
  }
}
