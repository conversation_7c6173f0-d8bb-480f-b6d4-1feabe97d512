import { createSelector } from '@ngrx/store'
import { createHistorySelectors } from 'ngrx-wieder'
import { uibConfig } from './config.reducer'

const selectConfigState = uibConfig.selectUibConfigState

const selectComponentConfigs = (componentId: string) => createSelector(selectConfigState, (state) => state.configs[componentId] || {})

export const selectAll = (componentId: string) => createSelector(selectComponentConfigs(componentId), (configs) => Object.values(configs))

export const selectItem = (componentId: string, id: string) => createSelector(selectComponentConfigs(componentId), (configs) => configs[id])

export const { selectCanUndo, selectCanRedo } = createHistorySelectors(selectConfigState)
