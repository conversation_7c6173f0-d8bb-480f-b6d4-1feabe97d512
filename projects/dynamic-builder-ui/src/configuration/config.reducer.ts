import { createFeature, on } from '@ngrx/store'
import { initialUndoRedoState, undoRedo, UndoRedoState } from 'ngrx-wieder'
import { addConfig, initConfig, removeConfig, setConfig, updateConfig } from './config.actions'
import { ComponentConfig } from './config.model'

interface ConfigState extends UndoRedoState {
  configs: { [componentId: string]: { [id: string]: ComponentConfig } }
}

const { createUndoRedoReducer } = undoRedo({
  allowedActionTypes: [addConfig.type, removeConfig.type, updateConfig.type],
  maxBufferSize: Number.MAX_SAFE_INTEGER
})

function storeConfig(state: ConfigState, componentId: string, config: ComponentConfig[]) {
  state.configs[componentId] = state.configs[componentId] || {}
  for (let c of config) {
    state.configs[componentId][c.id] = c
  }
  return state
}

const reducer = createUndoRedoReducer<ConfigState>(
  { configs: {}, ...initialUndoRedoState },
  on(initConfig, (state, { componentId, config }) => {
    state.configs[componentId] = {}
    return storeConfig(state, componentId, config)
  }),
  on(setConfig, (state, { componentId, config }) => {
    state.configs[componentId] = {}
    return storeConfig(state, componentId, config)
  }),
  on(addConfig, (state, { componentId, config }) => {
    if (state.configs[componentId][config.id]) {
      throw new Error(`Config ${config.id} already exists.`)
    }
    return storeConfig(state, componentId, [config])
  }),
  on(removeConfig, (state, { componentId, id }) => {
    delete state.configs[componentId][id]
    return state
  }),
  on(updateConfig, (state, { componentId, config }) => {
    if (!Array.isArray(config)) config = [config]
    return storeConfig(state, componentId, config)
  })
)

export const uibConfig = createFeature({ name: 'uibConfig', reducer })
