import { AfterViewInit, ChangeDetectionStrategy, Component, Input, SimpleChanges } from '@angular/core'
import { ConfiguratorContext } from '../configurator.models'
import { FormsModule } from '@angular/forms'
import { Mutable, NgModelChangeDebouncedDirective } from '../../utils'
import { NgSelectModule } from '@ng-select/ng-select'
import _ from 'lodash'
import { ConfigService } from '@mb/ngx-ui-builder/configuration'

@Component({
  selector: 'uib-class-editor',
  standalone: true,
  imports: [FormsModule, NgSelectModule, NgModelChangeDebouncedDirective],
  template: `
    <label class="form-label" for="classes">CSS Classes</label>
    <textarea
      class="form-control"
      id="classes"
      type="text"
      name="classes"
      autocomplete="off"
      spellcheck="false"
      [(ngModel)]="context.config.classes"
      (ngModelChangeDebounced)="context.configChanged()"></textarea>
    <ng-select
      class="mt-2"
      [items]="items"
      [multiple]="true"
      [addTag]="true"
      [(ngModel)]="classes"
      (change)="dataChanged($event)"
      placeholder="Nhập tên class"></ng-select>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ClassEditorComponent implements AfterViewInit {
  @Input() context: ConfiguratorContext
  classes: string[]
  items = []
  constructor(public configService: ConfigService) {}

  ngOnChanges(changes: SimpleChanges) {
    this.classes = this.context.config.classes ? this.context.config.classes.split(' ') : []
  }

  ngAfterViewInit(): void {
    // load tự động gợi ý class
    if (localStorage.getItem('classes')) {
      const arrayClass = JSON.parse(localStorage.getItem('classes')) || []
      this.items = arrayClass
    }
  }

  dataChanged($event: any) {
    if ($event) {
      this.context.config.classes = $event.join(' ')
      this.context.configChanged()
      try {
        let arrayClass = []
        if (localStorage.getItem('classes')) {
          arrayClass = JSON.parse(localStorage.getItem('classes')) || []
          localStorage.setItem('classes', JSON.stringify(_.uniq([...$event, ...arrayClass])))
        } else {
          localStorage.setItem('classes', JSON.stringify($event))
        }
      } catch (e) {}
    }
  }
}
