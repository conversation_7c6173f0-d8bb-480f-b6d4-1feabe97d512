.offcanvas-body {
  padding: 0!important;
  padding-bottom: 90px!important; /* avoid toolbar hiding bottom of configurator */
}

.ltr > svg-icon {
  transform: rotate(180deg) translateY(-4px);
}
.offcanvas.offcanvas-end {
  width: 600px!important;
}
.mdc-list-item {
  button {
    font-size: 14px;
  }
}

:host ::ng-deep .mat-mdc-tab-header {
  top: 0;
  z-index: 1000;
  position: sticky;
  position: -webkit-sticky; /* macOS/iOS Safari */
  background-color: #fff;
}
:host ::ng-deep .mat-mdc-tab-body {
  padding: 10px;
}

input.select-diff {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.item-container {
  display: flex;
  align-items: center;

  .info {
    flex-grow: 1;
    text-align: left;
    display: flex;
  }
  button {
    color: #2b34e0;
    text-align: left;
    cursor: pointer;
  }
}
