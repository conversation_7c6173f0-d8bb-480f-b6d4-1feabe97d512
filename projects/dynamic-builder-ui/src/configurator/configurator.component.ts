import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChildren,
  ElementRef,
  HostListener,
  Input,
  isDevMode,
  OnDestroy,
  OnInit,
  QueryList,
  ViewChild
} from '@angular/core'
import { Offcanvas } from 'bootstrap'
import { map, switchMap, tap } from 'rxjs/operators'
import { Observable, Subscription } from 'rxjs'
import { Configurable, ConfigurableService } from '../configurable/configurable.service'
import { ComponentConfig, ConfigService, ContainerConfig } from '../configuration'
import { Mutable } from '../utils/typings'
import { defaultPaletteOptions, PaletteComponent } from './palette/palette.component'
import { ConfiguratorContext, ConfiguratorOptions } from './configurator.models'
import { TemplateNameDirective } from '@mb/ngx-ui-builder/utils'
import { CommonModule } from '@angular/common'
import { ModalComponent, NgModelChangeDebouncedDirective, ToastService, TooltipDirective } from '../utils'
import { ClassEditorComponent, ConditionEditorComponent, FlexEditorComponent, HtmlEditorComponent, SpacingEditorComponent } from './editors'
import { TreeComponent } from './public-api'
import { icons } from '@mb/ngx-ui-builder/svg/svg-icons'
import { AngularSvgIconModule, SvgIconRegistryService } from 'angular-svg-icon'
import { NgSelectModule } from '@ng-select/ng-select'
import Utils from '../../../../src/app/shared/utils/utils'

import { MatListModule } from '@angular/material/list'
import { MatTab, MatTabGroup } from '@angular/material/tabs'
import { MatIcon } from '@angular/material/icon'
import { MonacoEditorModule } from 'ngx-monaco-editor-v2'
import { FormsModule } from '@angular/forms'
import { isJsonString } from '@shared'
import { NavigationEnd, Router } from '@angular/router'
import { TruncatePipe } from '../../../../src/app/shared/pipe'
import { DialogDiffConfigComponent } from '@mb/ngx-ui-builder/configurator/dialog-diff-config/dialog-diff-config.component'
import { ButtonImportFileComponent } from '@shared/components/data-input/button-import-file/button-import-file.component'
import { ButtonImportComponent } from '@mb/ngx-ui-builder/configurator/button-import/button-import.component'

export const defaultConfiguratorOptions: ConfiguratorOptions = {
  paletteOptions: defaultPaletteOptions,
  showFlexEditor: true,
  showHtmlEditor: true,
  showCssClasses: true,
  showSpacingEditor: true,
  showConditionalDisplay: true,
  showRemove: true,
  showDuplicate: true
}

@Component({
  selector: 'uib-configurator',
  standalone: true,
  imports: [
    CommonModule,
    AngularSvgIconModule,
    TooltipDirective,
    TreeComponent,
    HtmlEditorComponent,
    FlexEditorComponent,
    PaletteComponent,
    ClassEditorComponent,
    SpacingEditorComponent,
    ConditionEditorComponent,
    NgSelectModule,
    MatListModule,
    MatTabGroup,
    MatTab,
    MatIcon,
    ModalComponent,
    MonacoEditorModule,
    FormsModule,
    NgModelChangeDebouncedDirective,
    TruncatePipe,
    DialogDiffConfigComponent,
    ButtonImportFileComponent,
    ButtonImportComponent
  ],
  templateUrl: './configurator.component.html',
  styleUrls: ['./configurator.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfiguratorComponent implements OnInit, OnDestroy {
  // Capture configurator templates
  @ContentChildren(TemplateNameDirective)
  children: QueryList<TemplateNameDirective>
  configurators: Record<string, TemplateNameDirective> = {}

  @ViewChild('offcanvas') offcanvasEl: ElementRef<HTMLElement>
  offcanvas: Offcanvas

  @ViewChild('offcanvasBody') offcanvasBodyEl: ElementRef<HTMLElement>
  @Input()
  options = defaultConfiguratorOptions
  @Input() zoneOptions: Record<string, ConfiguratorOptions> = {}
  @ViewChild('dialogImport') dialogImport: DialogDiffConfigComponent
  edited$: Observable<ConfiguratorContext>

  configuration: ComponentConfig[] = []

  isTree: boolean = true
  ltr = false
  parentId: string
  isJsonEditor = false
  version
  versions = []
  templates = []
  editorOptions = { language: 'json' }
  code: string = ''
  currentTabIndex = 0
  prefix = ''
  nameCopy
  showDialogCopy: boolean
  private routerSubscription: Subscription

  // Giá trị checkbox
  selectedItem = []
  // Dialog cho so sánh cấu hình
  historiesSelected = []
  showDialogShowDiff: boolean

  // Dialog cho import cấu hình
  diffLastConfigAndImport = []
  showDialogImportConfigDiff: boolean

  constructor(
    private cdr: ChangeDetectorRef,
    public configurableService: ConfigurableService,
    public configService: ConfigService,
    private iconReg: SvgIconRegistryService,
    private toastService: ToastService,
    private router: Router
  ) {

  }
  onRouteChange(event: NavigationEnd) {
    console.log('Navigation ended with URL:', event.urlAfterRedirects)
    //TODO: chuyển sang subcribe toàn store để ko phải set timeout
    setTimeout(() => {
      this.configuration = this.configService.getAllConfig()
      console.log('configuration', this.configuration)
      this.onLoadEditorConfig()
      this.cdr.markForCheck()
    }, 1000)
  }

  ngOnInit(): void {
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.onRouteChange(event)
      }
    })

    this.edited$ = this.configurableService.watchEdited().pipe(
      tap(() => this.offcanvas?.show()),
      tap(() => this.showTree(true)),
      tap((edited) => (this.parentId = edited.parentId)),
      switchMap((context) =>
        this.configService.watchConfig(context!.id).pipe(
          map((config) => ({
            context,
            config,
            options: this.resolveOptions(context.zone),
            configurators: this.configurators,
            configChanged: () => this.configService.updateConfig(config)
          }))
        )
      )
    )

    // subscribe to configuration events
    this.configService.watchAllConfig().subscribe((config) => {
      this.configuration = config!
      this.onLoadEditorConfig()
      this.cdr.markForCheck()
    })

    // when edition is disabled, close side panel
    this.configurableService.editorEnabled$.subscribe((value) => {
      if (value === false && this.offcanvas) {
        // this.offcanvas.hide();
      }
    })

    this.configurableService.editorJsonEnabled$.subscribe((value) => {
      isDevMode() && console.log('****** editorJsonEnabled$', value)
      this.isJsonEditor = value
      if (value) {
        // this.onLoadEditorConfig()
      }
    })

    icons.forEach((item) => {
      this.iconReg.addSvg(item.name, item.data)
    })
  }

  ngOnDestroy() {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe()
    }
  }

  getConfigVersions() {
    this.diffLastConfigAndImport = []

    // get history
    this.configService
      .getConfigVersions()
      .then((res) => {
        if (res?.data?.content) {
          this.historiesSelected = []
          this.versions = res?.data.content.map((x) => {
            return { key: x.id, value: `${x.dataVersion} - ${x.updatedAt}`, ...x, data: x?.data?.config, checked: false }
          })
          this.cdr.markForCheck()
        }
      })
      .finally(this.cdr.detectChanges)
  }

  getConfigTemplates() {
    this.diffLastConfigAndImport = []
    // get templates
    // TODO: api support get all
    this.configService
      .getConfigComponentName('templates', 1000)
      .then((res) => {
        if (res?.data?.content?.length > 0) {
          this.diffLastConfigAndImport.push(res?.data?.content[0])
        }
        if (res?.data?.content) {
          this.templates = res?.data.content.map((x) => {
            return { key: x.id, value: `${x?.data.config?.id || ''} - ${x?.data?.config?.type || ''}`, data: x?.data?.config }
          })
        }
      })
      .finally(this.cdr.detectChanges)
  }

  /**
   * Create Bootstrap OffCanvas component
   */
  ngAfterViewInit() {
    this.offcanvas = Offcanvas.getOrCreateInstance(this.offcanvasEl.nativeElement, {
      backdrop: false,
      scroll: true
    })
    this.offcanvasEl.nativeElement.addEventListener('hide.bs.offcanvas', (_) => {
      this.configurableService.stopEditing()
    })
  }

  /**
   * Extract list of configuration editors
   */
  ngAfterContentInit() {
    this.children.forEach((template) => (this.configurators[template.name] = template))
  }

  showTree(showTree = true) {
    this.isTree = showTree
    this.offcanvasBodyEl?.nativeElement?.scroll(0, 0)
  }

  showGlobalConfiguration() {
    const conf: Partial<Configurable> = {
      id: 'global',
      parentId: '', // parentId would be required to duplicate or remove the component, which is not applicable here
      zone: '',
      removeSelected: () => {}, // These callbacks do nothing because this is not a real click on a configurable component
      removeEdited: () => {}
    }

    this.configurableService.clickConfigurable(conf as Configurable)
  }

  resolveOptions(zone: string) {
    // First set defaults, then the configurator options, then zone-specific options
    const options = Object.assign({}, defaultConfiguratorOptions, this.options, this.zoneOptions[zone] || {})
    // Same thing for the nested palette options
    options.paletteOptions = Object.assign({}, defaultPaletteOptions, this.options.paletteOptions, this.zoneOptions[zone]?.paletteOptions || {})
    return options
  }

  /**
   * It removes the item from the parent container.
   * @param {Event} event - Event
   */
  remove(context: Configurable) {
    // only uib-zone cannot self remove
    if (context.parentId) {
      const container = this.configService.getContainer(context.parentId)
      const index = container.items.findIndex((item) => item === context.id)
      if (index !== -1) {
        container.items.splice(index, 1)
        this.configService.updateConfig([container])
        this.offcanvas.toggle()
      }
    }
  }

  duplicate(context: Configurable) {
    const config: Mutable<ComponentConfig> = this.configService.getConfig(context.id)
    config.id = this.configService.generateId(config.id) // Generate a new config id
    if (context.parentId) {
      const container = this.configService.getContainer(context.parentId)
      const index = container.items.findIndex((item) => item === context.id)
      if (index !== -1) {
        container.items.splice(index + 1, 0, config.id)
        this.configService.updateConfig([config, container])
      }
    }
    // Special case of a zone
    else if (context.zone === context.id) {
      // Create another copy
      const config2: Mutable<ComponentConfig> = this.configService.getConfig(context.id)
      config2.id = this.configService.generateId(config.id)

      const container: ContainerConfig = {
        id: context.id,
        type: '_container',
        items: [config.id, config2.id],
        classes: 'flex-column'
      }

      this.configService.updateConfig([config, config2, container])
    }
  }

  /**
   * lưu mẫu json để tái sử dụng
   * @param context
   */
  saveAsTemplate(context: Configurable) {
    const config: Mutable<ComponentConfig> = this.configService.getConfig(context.id)
    this.configService
      .saveTemplateConfig('templates', config)
      .pipe(
        tap((res) => {
          // reload templates
          this.toastService.show(`Templete '${context.id}' saved`, 'warning text-dark')
          this.getConfigTemplates()
        })
      )
      .subscribe()
  }

  async onContextChanged(context: ConfiguratorContext) {
    context.configChanged()
  }

  hidePanel() {
    this.offcanvas.hide()
  }

  onSelectVersion($event: any, edited: ConfiguratorContext) {
    const config = Utils.JSonTryParse($event.data)
    this.configService.init(config)
    // this.onContextChanged(edited)
  }

  onTemplateSelected(item: any, edited: ConfiguratorContext) {
    console.log('onTemplateSelected', item, edited)
    const context = edited.context
    let config = { ...item?.data, id: `${item?.data.type}-${new Date().getTime()}` }
    if (context.parentId) {
      const container = this.configService.getContainer(context.parentId)
      const index = container.items.findIndex((item) => item === context.id)
      if (index !== -1) {
        container.items.splice(index + 1, 0, config.id)
        this.configService.updateConfig([config, container])
      }
    }
    // Special case of a zone
    else if (context.zone === context.id) {
      const container: ContainerConfig = {
        id: context.id,
        type: '_container',
        items: [config.id],
        classes: 'flex-column'
      }

      this.configService.updateConfig([config, container])
    }
  }

  onClosedEditor() {
    this.configurableService.toggleJsonEditor()
  }

  onLoadEditorConfig() {
    this.code = JSON.stringify(this.configuration, null, 4)
  }

  goToParent() {
    const el = this.configurableService.configurableDirectiveMap.get(this.parentId)
    el?.click(new MouseEvent('click'))
    el?.nativeElement.scrollIntoView({ behavior: 'smooth', inline: 'nearest', block: 'center' })
  }

  tabIndexChange($event: number) {
    isDevMode() && console.log('tab', $event)
    this.currentTabIndex = $event
    if ($event === 2) {
      this.getConfigVersions()
    } else if ($event === 3) {
      this.getConfigTemplates()
    }
  }

  editorChanged(edited: ConfiguratorContext) {
    if (isJsonString(this.code)) {
      const json = JSON.parse(this.code)
      this.configService.updateConfig(json)
      // if (Array.isArray(json)) {
      //   json.forEach((c) => {
      //     debugger
      //     this.configService.updateConfig(c)
      //   })
      // }
    }
  }

  onModalDialogCopyClose($event: boolean) {
    this.showDialogCopy = false
    if (this.nameCopy && $event) {
      const config = this.configService.getAllConfig()
      this.configService
        .saveConfig(this.nameCopy, config)
        .pipe(
          tap((res) => {
            // reload templates
            alert(`Lưu ${this.nameCopy} thành công`)
          })
        )
        .subscribe()
    }
  }

  saveAsCopy() {
    this.nameCopy = this.configService.getComponentId()
    this.showDialogCopy = true
  }

  async pasteAsConfigClipboard(context: Configurable) {
    const item = JSON.parse(await navigator.clipboard.readText())
    let config = { ...item, id: `${item.type}-${new Date().getTime()}` }
    if (context.parentId) {
      const container = this.configService.getContainer(context.parentId)
      const index = container.items.findIndex((item) => item === context.id)
      if (index !== -1) {
        container.items.splice(index + 1, 0, config.id)
        this.configService.updateConfig([config, container])
      }
    }
    // Special case of a zone
    else if (context.zone === context.id) {
      const container: ContainerConfig = {
        id: context.id,
        type: '_container',
        items: [config.id],
        classes: 'flex-column'
      }

      this.configService.updateConfig([config, container])
    }
  }

  prefixChanged() {
    let config = this.configService.getAllConfig()
    // this.configService.init(this.addPrefixToIds(config, this.prefix + '-'))
  }
  showDiff() {
    this.historiesSelected = [...this.selectedItem]
    this.showDialogShowDiff = true
  }
  addToHistoriesCompare(item) {
    if (!item.checked) {
      this.selectedItem.push(item)
    } else {
      this.selectedItem = this.selectedItem.filter((i) => i.id !== item.id)
    }
  }
  onModalDialogDiffClose($event: boolean) {
    this.showDialogShowDiff = false
  }

  onModalDialogImportDiffClose($event: boolean) {
    this.showDialogImportConfigDiff = false
  }
  onFileSelected(event: any) {
    this.diffLastConfigAndImport[0] = this.configService.getAllConfig()
    const reader = new FileReader()
    reader.onload = () => {
      const content = reader.result as string
      const jsonData = JSON.parse(content)
      if (this.diffLastConfigAndImport.length === 1) {
        this.diffLastConfigAndImport.push(jsonData)
      } else if (this.diffLastConfigAndImport.length === 2) {
        this.diffLastConfigAndImport[1] = jsonData
      }
    }
    reader.readAsText(event)
    setTimeout(() => {
      this.showDialogImportConfigDiff = true
      this.cdr.markForCheck()
    }, 500)
  }

  applyConfig() {
    if(this.diffLastConfigAndImport.length === 2) {
      const config = this.diffLastConfigAndImport[1]
      this.configService.init(config)
      this.dialogImport.onModalClose(true)
    }
  }
  @HostListener('window:keydown', ['$event']) handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'ArrowLeft') {
      this.onArrowLeft();
    } else if (event.key === 'ArrowRight') {
      this.onArrowRight();
    }
  }

  onArrowLeft() {
    if(this.historiesSelected.length === 2) {
      // Xử lý sự kiện khi ấn nút mũi tên trái
      const prevVersion = Number(this.historiesSelected[0]?.dataVersion) - 1
      const prevItem = this.versions.find((e) => {
        return e?.dataVersion === prevVersion
      })

      if(prevItem) {
        const currItem = this.historiesSelected[0]
        this.historiesSelected = [prevItem, currItem]
      }
    }

  }

  onArrowRight() {
    // Xử lý sự kiện khi ấn nút mũi tên phải
    const nextVersion = Number(this.historiesSelected[1]?.dataVersion) + 1
    const nextItem = this.versions.find((e) => {
      return e?.dataVersion === nextVersion
    })
    if(nextItem) {
      const currItem = this.historiesSelected[1]
      this.historiesSelected = [currItem,nextItem]
    }
  }

  get previousTitleDiffHistory() {
    if(this.historiesSelected.length === 2 ) {
      if(Number(this.historiesSelected[0]?.dataVersion) > Number(this.historiesSelected[1]?.dataVersion)) {
        return String(this.historiesSelected[1]?.dataVersion)
      } else {
        return String(this.historiesSelected[0]?.dataVersion)
      }
    }
    return ''
  }
  get currentTitleDiffHistory() {
    if(this.historiesSelected.length === 2 ) {
      if(Number(this.historiesSelected[0]?.dataVersion) > Number(this.historiesSelected[1]?.dataVersion)) {
        return String(this.historiesSelected[0]?.dataVersion)
      } else {
        return String(this.historiesSelected[1]?.dataVersion)
      }
    }
    return ''
  }
}
