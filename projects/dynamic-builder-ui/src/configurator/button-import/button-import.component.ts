import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core'
import { MatIconModule } from '@angular/material/icon'

@Component({
  selector: 'app-button-import',
  templateUrl: './button-import.component.html',
  styleUrls: ['./button-import.component.scss'],
  standalone: true,
  imports: [MatIconModule]
})
export class ButtonImportComponent {
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>
  @Input() accept: string = '.json'
  @Input() multiple: boolean
  @Output() fileSelect = new EventEmitter()
  file: string
  @Input() name: string = 'import'

  // handleChange(files: FileList) {
  //   if (files && files.length) {
  //     this.file = files[0].name;
  //   }
  // }

  openFileSelectDialog() {
    this.fileInput.nativeElement.click()
  }

  onFileSelectionChanged(event: any): void {
    if (this.multiple) {
      this.fileSelect.emit(event.target.files)
    } else {
      this.fileSelect.emit(event.target.files[0])
    }
    this.fileInput.nativeElement.value = null
  }
}
