<div
  #offcanvas
  class="offcanvas"
  [ngClass]="{ 'offcanvas-start': ltr, 'offcanvas-end': !ltr }"
  tabindex="-1"
  id="offcanvas"
  *ngIf="{ edited: (edited$ | async) } as obs">
  <!-- HEADER -->
  <div class="offcanvas-header bg-light border-bottom">
    <h5 class="offcanvas-title" *ngIf="obs.edited as edited">
      <strong>{{ edited.config.id }}</strong>
      <h6>
        (Type:
        <strong>{{ edited.context.templates?.[edited.config.type]?.display || edited.config.type }}</strong>
        )
      </h6>
    </h5>
    <!--    <button-->
    <!--      id="right-to-left"-->
    <!--      type="button"-->
    <!--      [ngClass]="{ ltr: !ltr }"-->
    <!--      uib-tooltip="Slide to {{ ltr ? 'right' : 'left' }}"-->
    <!--      class="btn btn-sm btn-link ms-auto"-->
    <!--      (click)="ltr = !ltr">-->
    <!--      <svg-icon name="arrow_forward"></svg-icon>-->
    <!--    </button>-->
    <!--    <button class="btn btn-sm" uib-tooltip="Global configuration" (click)="showGlobalConfiguration()">-->
    <!--      <svg-icon name="gear"></svg-icon>-->
    <!--    </button>-->
    <!--    <button class="btn btn-sm" uib-tooltip="Tắt panel" (click)="hidePanel()">-->
    <!--      <svg-icon name="arrow_forward" class="rtl"></svg-icon>-->
    <!--    </button>-->
    <!--    <button *ngIf="!!parentId" class="btn btn-sm" uib-tooltip="Go to the parent container" (click)="goToParent()">-->
    <!--      <svg-icon name="folder" width="22px" height="25px"></svg-icon>-->
    <!--    </button>-->
    <div class="d-flex align-items-center">
      <ng-container *ngIf="obs.edited as edited">
        <app-button-import (fileSelect)="onFileSelected($event)" name="Import cấu hình" />
        <button class="btn btn-secondary btn-sm mrl-1" uib-tooltip="Xem trước" (click)="onContextChanged(edited)">Xem trước</button>
        <button class="btn btn-primary btn-sm mrl-1" uib-tooltip="Lưu thay đổi" (click)="saveAsCopy()">Lưu cấu hình</button>
      </ng-container>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close" aria-label></button>
    </div>
  </div>
  <!-- BODY -->
  <div #offcanvasBody class="offcanvas-body">
    <div class="d-flex flex-column">
      <mat-tab-group animationDuration="0ms" (selectedIndexChange)="tabIndexChange($event)">
        <mat-tab label="Tham số">
          <ng-container *ngIf="obs.edited as edited">
            <!-- CONFIGURATOR -->
            <div class="text-muted" *ngIf="edited.context.templates?.[edited.config.type]?.description as description">
              {{ description }}
              <hr />
            </div>
            <ng-container *ngTemplateOutlet="configurators[edited.config.type]?.templateRef; context: { $implicit: edited }"></ng-container>

            <ng-container *ngIf="edited.config.type === '_container'">
              <details class="mb-3" open *ngIf="edited.options.paletteOptions">
                <summary>Danh sách component</summary>
                <uib-palette
                  #paletteComponent
                  [context]="edited.context"
                  [config]="edited.config"
                  [configuratorContext]="edited"
                  [options]="edited.options.paletteOptions"
                  [configurators]="edited.configurators"></uib-palette>
              </details>
            </ng-container>
            <div class="d-flex justify-content-start mb-1 mt-2">
              <button
                *ngIf="edited.options.showRemove && edited.context.parentId"
                class="btn btn-outline-danger me-2"
                (click)="remove(edited.context)"
                uib-tooltip="Remove this component from its parent">
                Remove
              </button>
              <button
                *ngIf="edited.options.showDuplicate"
                class="btn btn-outline-secondary me-2"
                (click)="duplicate(edited.context)"
                uib-tooltip="Duplicate this component within its parent">
                Duplicate
              </button>
              <button class="btn btn-outline-primary me-2" (click)="saveAsTemplate(edited.context)" uib-tooltip="Save template reuse">
                Save as template
              </button>
              <button
                class="btn btn-outline-primary me-2"
                (click)="pasteAsConfigClipboard(edited.context)"
                uib-tooltip="Paste as config from clipboard">
                Paste as config from clipboard
              </button>
            </div>
          </ng-container>
        </mat-tab>
        <mat-tab label="Giao diện">
          <ng-container *ngIf="obs.edited as edited">
            <details class="mb-3 mt-3" open *ngIf="edited.options.showFlexEditor">
              <summary>Layout</summary>
              <uib-flex-editor [config]="edited.config"></uib-flex-editor>
            </details>

            <uib-html-editor
              *ngIf="edited.options.showHtmlEditor && edited.config.type === '_raw-html'"
              [context]="edited"
              class="d-block mb-3"></uib-html-editor>

            <!-- Separator to mark the difference between the type-specific configurator and the generic configuration options -->
            <!-- <ng-container > -->
            <hr *ngIf="configurators[edited.config.type]?.templateRef || edited.config.type.startsWith('_')" />
            <!-- </ng-container> -->

            <details
              *ngIf="edited.options.showConditionalDisplay && (edited.context.data || edited.context.conditionsData)"
              [attr.open]="edited.config.condition ? '' : undefined"
              class="mb-3">
              <summary uib-tooltip="Display this component only if the data validates a rule">Conditional display</summary>
              <uib-condition-editor [context]="edited"></uib-condition-editor>
            </details>
            <div class="mb-3" *ngIf="edited.options.showCssClasses">
              <uib-class-editor [context]="edited"></uib-class-editor>
            </div>
            <div class="mb-3" *ngIf="edited.options.showSpacingEditor">
              <uib-bs-spacing-editor [config]="edited.config"></uib-bs-spacing-editor>
            </div>
          </ng-container>
        </mat-tab>
        <mat-tab label="Lịch sử ({{ versions?.length || '' }})">
          <div *ngIf="obs.edited as edited" class="mb-4">
            <div class="d-flex flex-row justify-content-between align-items-center">
              <label class="form-label">Chọn phiên bản rồi lưu lại để khôi phục về phiên bản mong muốn</label>
              <span (click)="showDiff()" class="form-label cursor-pointer" style="color: #2b34e0">So sánh {{ historiesSelected.length }}/2</span>
            </div>
            <mat-nav-list>
              <mat-list-item *ngFor="let item of versions">
                <div class="item-container">
                  <input
                    class="select-diff"
                    type="checkbox"
                    [(ngModel)]="item.checked"
                    (click)="addToHistoriesCompare(item)"
                    [disabled]="!item.checked && selectedItem.length >= 2" />
                  <div class="info">{{ item.value }} - {{ item.updatedBy }}</div>
                  <button (click)="onSelectVersion(item, edited)">Áp dụng</button>
                </div>
              </mat-list-item>
            </mat-nav-list>
          </div>
        </mat-tab>
        <mat-tab label="Mẫu ({{ templates?.length || '' }})">
          <div *ngIf="obs.edited as edited" class="mb-4">
            <label class="form-label">Chọn component mẫu để thêm vào</label>
            <mat-nav-list>
              <mat-list-item *ngFor="let item of templates">
                <mat-icon matListItemIcon>view_compact</mat-icon>
                <button (click)="onTemplateSelected(item, edited)">
                  {{ item.value }}
                </button>
              </mat-list-item>
            </mat-nav-list>
          </div>
        </mat-tab>
      </mat-tab-group>
      <ng-container *ngIf="currentTabIndex <= 1">
        <div class="p-2">
          <details class="mb-3" open>
            <summary>Layer</summary>
            <div class="row">
              <div class="col-12">
                <div class="form-floating">
                  <input
                    class="form-control "
                    type="text"
                    value="{{ prefix }}"
                    [(ngModel)]="prefix"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChangeDebounced)="prefixChanged()" />
                  <label class="form-check-label">Prefix component</label>
                </div>
              </div>
            </div>
            <uib-tree [configuration]="configuration"></uib-tree>
          </details>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div *ngIf="{ edited: (edited$ | async) } as obs">
  <div *ngIf="this.configurableService.editorJsonEnabled$ | async">
    <div style="width: 100%; height: 600px">
      <ng-container *ngIf="obs.edited as edited">
        <ngx-monaco-editor style="width: 100%; height: 100%" [options]="editorOptions" [(ngModel)]="code"></ngx-monaco-editor>
        <div class="d-flex m-2 justify-content-end">
          <button class="btn btn-secondary btn-sm" uib-tooltip="Xem trước" (click)="editorChanged(edited)">Xem trước</button>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<uib-modal [title]="'Save all config as name...'" [show]="!!showDialogCopy" (close)="onModalDialogCopyClose($event)">
  <label for="config-display" class="form-label">Component name</label>
  <input type="text" class="form-control" id="config-display" autocomplete="off" spellcheck="off" [(ngModel)]="nameCopy" />
</uib-modal>
<app-dialog-diff-config [show]="showDialogShowDiff" (close)="onModalDialogDiffClose($event)" [data]="historiesSelected" [previousTitle]="previousTitleDiffHistory" [currentTitle]="currentTitleDiffHistory">
  <!-- buttons.component.html -->
  <div button-bottom class="d-flex flex-grow-1 align-items-center justify-content-center">
    <button (click)="onArrowLeft()">
      <svg-icon name="arrow-left" [svgStyle]="{'width.px': 36}"></svg-icon>
    </button>
    <button (click)="onArrowRight()">
      <svg-icon name="arrow-right" [svgStyle]="{'width.px': 36}"></svg-icon>
    </button>
  </div>
</app-dialog-diff-config>
<app-dialog-diff-config
  #dialogImport
  [show]="showDialogImportConfigDiff"
  (close)="onModalDialogImportDiffClose($event)"
  [data]="diffLastConfigAndImport"
  previousTitle="Config hiện tại"
  currentTitle="Config import">
  <div button-bottom class="d-flex flex-grow-1 align-items-center justify-content-center">
    <button class="btn btn-primary btn-sm mrb-2" (click)="applyConfig()">Áp dụng</button>
  </div>
</app-dialog-diff-config>
