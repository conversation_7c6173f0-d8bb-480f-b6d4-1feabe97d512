import { ChangeDetectionStrategy, Component, ContentChild, TemplateRef } from '@angular/core'

import { ConfigurableService } from '../../configurable'
import { ConfigService } from '../../configuration'
import { CommonModule } from '@angular/common'
import { TooltipDirective } from '../../utils'
import { AngularSvgIconModule, SvgIconRegistryService } from 'angular-svg-icon'

@Component({
  selector: 'uib-toolbar',
  standalone: true,
  imports: [CommonModule, TooltipDirective, AngularSvgIconModule],
  templateUrl: './toolbar.component.html',
  styleUrls: ['./toolbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ToolbarComponent {
  @ContentChild(TemplateRef, { static: false }) template: TemplateRef<any>

  constructor(
    public configService: ConfigService,
    public configurableService: ConfigurableService
  ) {}

  toggleEditor() {
    this.configurableService.toggleEditor()
  }

  toggleJsonEditor() {
    this.configurableService.toggleJsonEditor()
  }
}
