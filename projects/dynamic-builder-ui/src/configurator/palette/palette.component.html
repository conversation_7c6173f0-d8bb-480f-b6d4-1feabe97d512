
<div class="p-2">

  <details *ngIf="options.showStandardPalette" open>
    <summary>
      <h6 class="d-inline" >Kéo & thả component mới</h6>
    </summary>
    <small class="text-muted" >Drag & drop an item from this palette into the UI to create and configure a new component.</small>
    <div class="mt-1">
      <ng-container *ngFor="let item of standardPalette"
        [ngTemplateOutlet]="paletteItem"
        [ngTemplateOutletContext]="{$implicit: item}">
      </ng-container>
    </div>
  </details>
  <ng-select class="mt-2" [items]="config.items"
             [multiple]="true"
             [addTag]="true"
             [(ngModel)]="config.items"
             (change)="configuratorContext.configChanged()"
             placeholder="Items">
  </ng-select>
  <details *ngIf="options.showExistingPalette" class="mt-3">
    <summary>
      <h6 class="d-inline" >Kéo & thả vào component đã tồn tại</h6>
    </summary>
    <small class="text-muted mb-1" >Drag & drop an item from this palette into the UI to insert a component whose configuration already exists.
    </small>
    <span uib-tooltip="This palette may include unused configuration from components removed from the UI." >ⓘ</span>
    <div class="mt-1">
      <ng-container *ngFor="let item of existingPalette"
        [ngTemplateOutlet]="paletteItem"
        [ngTemplateOutletContext]="{$implicit: item}">
      </ng-container>
    </div>
  </details>
</div>

<ng-template #paletteItem let-item>
  <div
    [dndDraggable]="item.type"
    [dndType]="context.zone"
    (dndStart)="onDndStart(item)"
    (dndEnd)="onDndEnd()"
    [uib-tooltip]="item.title"
    container=".uib-bootstrap"
    placement="bottom"
    class="palette-item"
    [ngClass]="{'uib-unused': item.removable}">
    <svg-icon class="grip" key="grip" height="1em"></svg-icon>
    <span [ngClass]="item.iconClass"></span>
    {{item.display || item.type}}
    <button *ngIf="item.removable"
      type="button"
      class="btn-close"
      uib-tooltip="Remove this unused configuration"

      placement="bottom"
      container=".uib-bootstrap"
      (click)="removeItem(item)">
    </button>
  </div>
</ng-template>

<!-- Modal -->
<uib-modal [title]="modal?.title" [show]="!!modal" (close)="onModalClose($event)">
  <div *ngTemplateOutlet="modal?.configurator.templateRef; context: { $implicit: modal }"></div>
</uib-modal>
