import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core'
import { DiffEditorModel, MonacoEditorModule } from 'ngx-monaco-editor-v2'
import { MatIcon } from '@angular/material/icon'
import { Modal } from 'bootstrap'
import { NgIf } from '@angular/common'

@Component({
  selector: 'app-dialog-diff-config',
  standalone: true,
  imports: [MonacoEditorModule, MatIcon, NgIf],
  templateUrl: './dialog-diff-config.component.html',
  styleUrl: './dialog-diff-config.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DialogDiffConfigComponent implements AfterViewInit, OnChanges, OnDestroy {
  editorOptions = { language: 'javascript' }
  @ViewChild('modal', { static: true }) el: ElementRef
  @Input() show: boolean
  @Output() close = new EventEmitter<boolean>()
  @Input() data: any
  modal?: Modal
  @Input() previousTitle = 'trước'
  @Input() currentTitle = 'sau'
  @ViewChild('monacoEditor', { static: false }) monacoEditor: ElementRef

  historiesSelected: Array<any>
  dataSelect1?: DiffEditorModel
  dataSelect2?: DiffEditorModel

  ngOnChanges(changes: SimpleChanges) {
    if (this.show && !this.modal) {
      this.modal = Modal.getOrCreateInstance(this.el.nativeElement, { backdrop: false }) // Backdrop causes issues when the modal is embedded in a fixed container
      this.createCompare()
      this.modal.show()
    }
    if (changes['data']) {
      this.createCompare()
    }
    this.reRenderEditors()
  }

  ngOnDestroy() {
    this.modal?.dispose()
  }

  ngAfterViewInit() {
    this.el.nativeElement.addEventListener('hidden.bs.modal', () => this.onModalClose(false))
  }
  createCompare() {
    if (this.data) {
      this.historiesSelected = this.data.sort((a, b) => {
        if (a.dataVersion < b.dataVersion) {
          return -1
        } else {
          return 1
        }
      })
      this.dataSelect1 = {
        code: JSON.stringify(this.historiesSelected[0], null, 4),
        language: 'json'
      }
      this.dataSelect2 = {
        code: JSON.stringify(this.historiesSelected[1], null, 4),
        language: 'json'
      }
    }
  }
  onModalClose(success: boolean) {
    this.show = false
    this.close.next(success)
    this.modal?.hide()
    this.modal = undefined
  }
  reRenderEditors() {
    if (this.monacoEditor && this.monacoEditor.nativeElement) {
      const editor = this.monacoEditor.nativeElement.querySelector('.monaco-editor')
      if (editor) {
        editor.layout()
      }
    }
  }
}
