<ng-container
*ngTemplateOutlet="isArray ? array : zone; context: { $implicit: data, first: 'true' }"
></ng-container>

<!-- data is an array -->
<ng-template #array let-data>
  <ng-container *ngFor="let d of data; let index = index; let first = first"
    [ngTemplateOutlet]="zone"
    [ngTemplateOutletContext]="{ index, first }">
  </ng-container>
</ng-template>

<!-- data is a single object -->
<ng-template #zone let-index="index" let-first="first">
  <!-- edit mode -->
  <div *ngIf="first && (enabled$ | async)"
    class="uib-zone"
    [id]="fullId"
    [uib-item]="fullId"
    [configurable]="true"
    [dataIndex]="index"
    uib-configurable>
  </div>
  <!-- norma -->
  <div *ngIf="!first || (enabled$ | async) === false"
    [uib-item]="fullId"
    [configurable]="false"
    [dataIndex]="index"
    (click)="onItemClicked($event, data, index)">
  </div>
</ng-template>
