<div
  *ngIf="config.type === '_raw-html' && condition"
  [innerHTML]="config.rawHtml">
</div>

<ng-container
  *ngIf="!config.type.startsWith('_') && condition && !config.hidden"
  [ngTemplateOutlet]="zoneRef.templates[config.type || id]?.templateRef"
  [ngTemplateOutletContext]="{ $implicit: config, data: _data, id }"
></ng-container>
<ng-container *ngIf="config.type === '_container' && condition && !configurable && !config.hidden">
  <div *ngFor="let item of config.items; let index = index" [hidden]="config.hidden"
    [uib-item]="item"
    [configurable]="false"
    [dataIndex]="dataIndex"
    [style.z-index]="999 - index"
  >
  </div>
</ng-container>

<!-- editor mode -->
<ng-container *ngIf="config.type === '_container' && condition && configurable">
  <div
    [ngClass]="config.classes || ''"
    class="uib-dropzone"

    [dndDropzone]="[zone]"
    [dndHorizontal]="isHorizontal"
    (dndDrop)="onDndDrop($event)"
    >

    <div class="dragPlaceholder align-self-stretch" dndPlaceholderRef></div>
    <div *ngFor="let item of config.items; let index = index"
      [uib-item]="item"
      [configurable]="true"
      [dataIndex]="dataIndex"
      [uib-tooltip]="item"
      uib-configurable
      [id]="item"
      [parentId]="config.id"
      [uib-disable-if]="dataIndex"

      [dndDraggable]="{item, index, container: config.id}"
      [dndType]="zone"
      (dndCanceled)="onDndCanceled(item, index)"
      >
    </div>
  </div>

</ng-container>
