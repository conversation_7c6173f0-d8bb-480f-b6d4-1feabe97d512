import { SvgIconType } from '../utils/svg-icon/types'

export const icons: SvgIconType[] = [
  {
    name: 'align_x_center',
    data: `<svg viewBox="0 0 16 16"><rect y="1" width="1.5" height="14" rx=".75" opacity=".7"/><rect x="14.5" y="1" width="1.5" height="14" rx=".75" opacity=".7"/><rect x="4" y="4" width="4" height="8" rx="1"/><rect x="8.5" y="4" width="4" height="8" rx="1"/></svg>`
  },
  {
    name: 'align_x_end',
    data: `<svg id="sprite-align-x-end" viewBox="0 0 16 16"><rect opacity=".7" x="14.5" y="1" width="1.5" height="14" rx=".75"/><rect opacity=".7" y="1" width="1.5" height="14" rx=".75"/><rect x="5" y="4" width="4" height="8" rx="1"/><rect x="9.5" y="4" width="4" height="8" rx="1"/></svg>`
  },
  {
    name: 'align_x_space_around',
    data: `<svg id="sprite-align-x-space-around" viewBox="0 0 16 16"><rect opacity=".7" x="14.5" y="1" width="1.5" height="14" rx=".75"/><rect opacity=".7" y="1" width="1.5" height="14" rx=".75"/><rect x="3" y="4" width="4" height="8" rx="1"/><rect x="9" y="4" width="4" height="8" rx="1"/></svg>`
  },
  {
    name: 'align_x_space_between',
    data: `<svg id="sprite-align-x-space-between" viewBox="0 0 16 16"><rect opacity=".7" x="14.5" y="1" width="1.5" height="14" rx=".75"/><rect opacity=".7" y="1" width="1.5" height="14" rx=".75"/><rect x="2" y="4" width="4" height="8" rx="1"/><rect x="10" y="4" width="4" height="8" rx="1"/></svg>`
  },
  {
    name: 'align_x_start',
    data: `<svg viewBox="0 0 16 16"><rect x="2.5" y="4" width="4" height="8" rx="1"/><rect x="7" y="4" width="4" height="8" rx="1"/><rect opacity=".7" y="1" width="1.5" height="14" rx=".75"/><rect opacity=".7" x="14.5" y="1" width="1.5" height="14" rx=".75"/></svg>`
  },
  {
    name: 'align_x_stretch',
    data: `<svg id="sprite-align-x-stretch" viewBox="0 0 16 16"><rect x="3" y="12.5" width="4" height="10" rx="1" transform="rotate(-90 3 12.5)"/><rect x="3" y="7.5" width="4" height="10" rx="1" transform="rotate(-90 3 7.5)"/><rect opacity=".7" x=".5" y="15" width="14" height="1.5" rx=".75" transform="rotate(-90 .5 15)"/><rect opacity=".7" x="14" y="15" width="14" height="1.5" rx=".75" transform="rotate(-90 14 15)"/></svg>`
  },
  {
    name: 'align_y_baseline',
    data: `<svg id="sprite-align-y-baseline" viewBox="0 0 16 16"><rect opacity=".7" x="1" y="7" width="14" height="1.5" rx=".75"/><rect x="3.5" y="3" width="4" height="10" rx="1"/><rect x="8.5" y="3" width="4" height="8" rx="1"/></svg>`
  },
  {
    name: 'align_y_center',
    data: `<svg id="sprite-align-y-center" viewBox="0 0 16 16"><rect x="4" y="1" width="4" height="14" rx="1"/><rect x="9" y="4" width="4" height="8" rx="1"/><rect opacity=".7" x="1" y="7" width="14" height="1.5" rx=".75"/></svg>`
  },
  {
    name: 'align_y_end',
    data: `<svg id="sprite-align-y-end" viewBox="0 0 16 16"><rect x="4" y="1" width="4" height="12" rx="1"/><rect x="9" y="5" width="4" height="8" rx="1"/><rect opacity=".7" x="1" y="14" width="14" height="1.5" rx=".75"/></svg>`
  },
  {
    name: 'align_y_start',
    data: `<svg id="sprite-align-y-start" viewBox="0 0 16 16"><rect x="3" y="3" width="4" height="12" rx="1"/><rect x="8" y="3" width="4" height="8" rx="1"/><rect opacity=".7" x="1" y=".5" width="14" height="1.5" rx=".75"/></svg>`
  },
  {
    name: 'align_y_stretch',
    data: `<svg id="sprite-align-y-stretch" viewBox="0 0 16 16"><rect x="3.5" y="3" width="4" height="10" rx="1"/><rect x="8.5" y="3" width="4" height="10" rx="1"/><rect opacity=".7" x="1" y=".5" width="14" height="1.5" rx=".75"/><rect opacity=".7" x="1" y="14" width="14" height="1.5" rx=".75"/></svg>`
  },
  {
    name: 'arrow_down',
    data: `<svg viewBox="0 0 18 18"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.045 8.204a1.125 1.125 0 10-1.59 1.591l6.75 6.75c.439.44 1.151.44 1.59 0l6.75-6.75a1.125 1.125 0 00-1.59-1.59l-4.83 4.829V2.125a1 1 0 00-1-1h-.25a1 1 0 00-1 1v10.909l-4.83-4.83z"/></svg>`
  },
  {
    name: 'arrow_forward',
    data: `<svg viewBox="0 0 18 18"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.204 14.954a1.125 1.125 0 001.591 1.591l6.75-6.75c.44-.439.44-1.151 0-1.59l-6.75-6.75a1.125 1.125 0 00-1.59 1.59l4.829 4.83H2.125a1 1 0 00-1 1v.25a1 1 0 001 1h10.909l-4.83 4.83z"/></svg>`
  },
  {
    name: 'tree',
    data: `<svg viewBox="0 0 576 512"><path d="M544 32h-112l-32-32H320c-17.62 0-32 14.38-32 32v160c0 17.62 14.38 32 32 32h224c17.62 0 32-14.38 32-32V64C576 46.38 561.6 32 544 32zM544 320h-112l-32-32H320c-17.62 0-32 14.38-32 32v160c0 17.62 14.38 32 32 32h224c17.62 0 32-14.38 32-32v-128C576 334.4 561.6 320 544 320zM64 16C64 7.125 56.88 0 48 0h-32C7.125 0 0 7.125 0 16V416c0 17.62 14.38 32 32 32h224v-64H64V160h192V96H64V16z"/></svg>`
  },
  {
    name: 'undo',
    data: `<svg viewBox="0 0 512 512"><path d="M480 256c0 123.4-100.5 223.9-223.9 223.9c-48.84 0-95.17-15.58-134.2-44.86c-14.12-10.59-16.97-30.66-6.375-44.81c10.59-14.12 30.62-16.94 44.81-6.375c27.84 20.91 61 31.94 95.88 31.94C344.3 415.8 416 344.1 416 256s-71.69-159.8-159.8-159.8c-37.46 0-73.09 13.49-101.3 36.64l45.12 45.14c17.01 17.02 4.955 46.1-19.1 46.1H35.17C24.58 224.1 16 215.5 16 204.9V59.04c0-24.04 29.07-36.08 46.07-19.07l47.6 47.63C149.9 52.71 201.5 32.11 256.1 32.11C379.5 32.11 480 132.6 480 256z"/></svg>`
  },
  {
    name: 'redo',
    data: `<svg viewBox="0 0 512 512"><path d="M468.9 32.11c13.87 0 27.18 10.77 27.18 27.04v145.9c0 10.59-8.584 19.17-19.17 19.17h-145.7c-16.28 0-27.06-13.32-27.06-27.2c0-6.634 2.461-13.4 7.96-18.9l45.12-45.14c-28.22-23.14-63.85-36.64-101.3-36.64c-88.09 0-159.8 71.69-159.8 159.8S167.8 415.9 255.9 415.9c73.14 0 89.44-38.31 115.1-38.31c18.48 0 31.97 15.04 31.97 31.96c0 35.04-81.59 70.41-147 70.41c-123.4 0-223.9-100.5-223.9-223.9S132.6 32.44 256 32.44c54.6 0 106.2 20.39 146.4 55.26l47.6-47.63C455.5 34.57 462.3 32.11 468.9 32.11z"/></svg>`
  },
  {
    name: 'trash',
    data: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash3" viewBox="0 0 16 16">
  <path d="M6.5 1h3a.5.5 0 0 1 .5.5v1H6v-1a.5.5 0 0 1 .5-.5M11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3A1.5 1.5 0 0 0 5 1.5v1H1.5a.5.5 0 0 0 0 1h.538l.853 10.66A2 2 0 0 0 4.885 16h6.23a2 2 0 0 0 1.994-1.84l.853-10.66h.538a.5.5 0 0 0 0-1zm1.958 1-.846 10.58a1 1 0 0 1-.997.92h-6.23a1 1 0 0 1-.997-.92L3.042 3.5zm-7.487 1a.5.5 0 0 1 .528.47l.5 8.5a.5.5 0 0 1-.998.06L5 5.03a.5.5 0 0 1 .47-.53Zm5.058 0a.5.5 0 0 1 .47.53l-.5 8.5a.5.5 0 1 1-.998-.06l.5-8.5a.5.5 0 0 1 .528-.47M8 4.5a.5.5 0 0 1 .5.5v8.5a.5.5 0 0 1-1 0V5a.5.5 0 0 1 .5-.5"/>
</svg>`
  },
  {
    name: 'grip',
    data: `<svg viewBox="0 0 320 512"><path d="M88 352C110.1 352 128 369.9 128 392V440C128 462.1 110.1 480 88 480H40C17.91 480 0 462.1 0 440V392C0 369.9 17.91 352 40 352H88zM280 352C302.1 352 320 369.9 320 392V440C320 462.1 302.1 480 280 480H232C209.9 480 192 462.1 192 440V392C192 369.9 209.9 352 232 352H280zM40 320C17.91 320 0 302.1 0 280V232C0 209.9 17.91 192 40 192H88C110.1 192 128 209.9 128 232V280C128 302.1 110.1 320 88 320H40zM280 192C302.1 192 320 209.9 320 232V280C320 302.1 302.1 320 280 320H232C209.9 320 192 302.1 192 280V232C192 209.9 209.9 192 232 192H280zM40 160C17.91 160 0 142.1 0 120V72C0 49.91 17.91 32 40 32H88C110.1 32 128 49.91 128 72V120C128 142.1 110.1 160 88 160H40zM280 32C302.1 32 320 49.91 320 72V120C320 142.1 302.1 160 280 160H232C209.9 160 192 142.1 192 120V72C192 49.91 209.9 32 232 32H280z"/></svg>`
  },
  {
    name: 'eye',
    data: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><!--! Font Awesome Pro 6.1.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --><path d="M279.6 160.4C282.4 160.1 285.2 160 288 160C341 160 384 202.1 384 256C384 309 341 352 288 352C234.1 352 192 309 192 256C192 253.2 192.1 250.4 192.4 247.6C201.7 252.1 212.5 256 224 256C259.3 256 288 227.3 288 192C288 180.5 284.1 169.7 279.6 160.4zM480.6 112.6C527.4 156 558.7 207.1 573.5 243.7C576.8 251.6 576.8 260.4 573.5 268.3C558.7 304 527.4 355.1 480.6 399.4C433.5 443.2 368.8 480 288 480C207.2 480 142.5 443.2 95.42 399.4C48.62 355.1 17.34 304 2.461 268.3C-.8205 260.4-.8205 251.6 2.461 243.7C17.34 207.1 48.62 156 95.42 112.6C142.5 68.84 207.2 32 288 32C368.8 32 433.5 68.84 480.6 112.6V112.6zM288 112C208.5 112 144 176.5 144 256C144 335.5 208.5 400 288 400C367.5 400 432 335.5 432 256C432 176.5 367.5 112 288 112z"/></svg>`
  },
  {
    name: 'eye_slash',
    data: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--! Font Awesome Pro 6.1.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --><path d="M150.7 92.77C195 58.27 251.8 32 320 32C400.8 32 465.5 68.84 512.6 112.6C559.4 156 590.7 207.1 605.5 243.7C608.8 251.6 608.8 260.4 605.5 268.3C592.1 300.6 565.2 346.1 525.6 386.7L630.8 469.1C641.2 477.3 643.1 492.4 634.9 502.8C626.7 513.2 611.6 515.1 601.2 506.9L9.196 42.89C-1.236 34.71-3.065 19.63 5.112 9.196C13.29-1.236 28.37-3.065 38.81 5.112L150.7 92.77zM223.1 149.5L313.4 220.3C317.6 211.8 320 202.2 320 191.1C320 180.5 316.1 169.7 311.6 160.4C314.4 160.1 317.2 159.1 320 159.1C373 159.1 416 202.1 416 255.1C416 269.7 413.1 282.7 407.1 294.5L446.6 324.7C457.7 304.3 464 280.9 464 255.1C464 176.5 399.5 111.1 320 111.1C282.7 111.1 248.6 126.2 223.1 149.5zM320 480C239.2 480 174.5 443.2 127.4 399.4C80.62 355.1 49.34 304 34.46 268.3C31.18 260.4 31.18 251.6 34.46 243.7C44 220.8 60.29 191.2 83.09 161.5L177.4 235.8C176.5 242.4 176 249.1 176 255.1C176 335.5 240.5 400 320 400C338.7 400 356.6 396.4 373 389.9L446.2 447.5C409.9 467.1 367.8 480 320 480H320z"/></svg>`
  },
  {
    name: 'space-xs',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M20.22,19.1H9.78c-.71,0-1.28-.67-1.28-1.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.31,.17,.5,.28,.5h10.45c.11,0,.28-.19,.28-.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.83-.57,1.5-1.28,1.5Z"></path><path d="M14.78,16.9c-.16,0-.32-.08-.41-.22l-3.36-4.92c-.16-.23-.1-.54,.13-.7,.23-.15,.54-.1,.7,.13l3.36,4.92c.16,.23,.1,.54-.13,.7-.09,.06-.18,.09-.28,.09Z"></path><path d="M11.42,16.9c-.1,0-.2-.03-.28-.09-.23-.16-.29-.47-.13-.7l3.36-4.92c.16-.23,.47-.28,.7-.13,.23,.16,.29,.47,.13,.7l-3.36,4.92c-.1,.14-.25,.22-.41,.22Z"></path><g><path d="M17.35,14.44c-.95,0-1.73-.78-1.73-1.73s.78-1.73,1.73-1.73,1.73,.78,1.73,1.73c0,.28-.22,.5-.5,.5s-.5-.22-.5-.5c0-.4-.33-.73-.73-.73s-.73,.33-.73,.73,.33,.73,.73,.73c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5Z"></path><path d="M17.35,16.9c-.95,0-1.73-.78-1.73-1.73,0-.28,.22-.5,.5-.5s.5,.22,.5,.5c0,.4,.33,.73,.73,.73s.73-.33,.73-.73-.33-.73-.73-.73c-.28,0-.5-.22-.5-.5s.22-.5,.5-.5c.95,0,1.73,.78,1.73,1.73s-.78,1.73-1.73,1.73Z"></path></g></svg>`
  },
  {
    name: 'space-s',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M20.22,19.1H9.78c-.71,0-1.28-.67-1.28-1.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.31,.17,.5,.28,.5h10.45c.11,0,.28-.19,.28-.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.83-.57,1.5-1.28,1.5Z"></path><g><path d="M15,14.44c-.95,0-1.73-.78-1.73-1.73s.78-1.73,1.73-1.73,1.73,.78,1.73,1.73c0,.28-.22,.5-.5,.5s-.5-.22-.5-.5c0-.4-.33-.73-.73-.73s-.73,.33-.73,.73,.33,.73,.73,.73c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5Z"></path><path d="M15,16.9c-.95,0-1.73-.78-1.73-1.73,0-.28,.22-.5,.5-.5s.5,.22,.5,.5c0,.4,.33,.73,.73,.73s.73-.33,.73-.73-.33-.73-.73-.73c-.28,0-.5-.22-.5-.5s.22-.5,.5-.5c.95,0,1.73,.78,1.73,1.73s-.78,1.73-1.73,1.73Z"></path></g></svg>`
  },
  {
    name: 'space-m',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M20.22,19.1H9.78c-.71,0-1.28-.67-1.28-1.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.31,.17,.5,.28,.5h10.45c.11,0,.28-.19,.28-.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.83-.57,1.5-1.28,1.5Z"></path><g><path d="M13.44,16.9c-.28,0-.5-.22-.5-.5v-4.92c0-.22,.15-.42,.36-.48,.21-.06,.44,.02,.56,.21l1.56,2.46c.15,.23,.08,.54-.15,.69-.24,.15-.54,.08-.69-.15l-.64-1.01v3.2c0,.28-.22,.5-.5,.5Z"></path><path d="M16.56,16.9c-.28,0-.5-.22-.5-.5v-3.2l-.64,1.01c-.15,.23-.45,.3-.69,.15-.23-.15-.3-.46-.15-.69l1.56-2.46c.12-.19,.35-.27,.56-.21,.21,.06,.36,.26,.36,.48v4.92c0,.28-.22,.5-.5,.5Z"></path></g></svg>`
  },
  {
    name: 'space-l',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M16.07,16.9h-2.15c-.28,0-.5-.22-.5-.5v-4.92c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v4.42h1.65c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5Z"></path><path d="M20.22,19.1H9.78c-.71,0-1.28-.67-1.28-1.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.31,.17,.5,.28,.5h10.45c.11,0,.28-.19,.28-.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.83-.57,1.5-1.28,1.5Z"></path></svg>`
  },
  {
    name: 'space-xl',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M18.58,16.9h-2.15c-.28,0-.5-.22-.5-.5v-4.92c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v4.42h1.65c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5Z"></path><path d="M20.22,19.1H9.78c-.71,0-1.28-.67-1.28-1.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.31,.17,.5,.28,.5h10.45c.11,0,.28-.19,.28-.5v-1.46c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v1.46c0,.83-.57,1.5-1.28,1.5Z"></path><path d="M14.78,16.9c-.16,0-.32-.08-.41-.22l-3.36-4.92c-.16-.23-.1-.54,.13-.7,.23-.15,.54-.1,.7,.13l3.36,4.92c.16,.23,.1,.54-.13,.7-.09,.06-.18,.09-.28,.09Z"></path><path d="M11.42,16.9c-.1,0-.2-.03-.28-.09-.23-.16-.29-.47-.13-.7l3.36-4.92c.16-.23,.47-.28,.7-.13,.23,.16,.29,.47,.13,.7l-3.36,4.92c-.1,.14-.25,.22-.41,.22Z"></path></svg>`
  },
  {
    name: 'direction-bottom',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M15,23.26c-.13,0-.26-.05-.35-.15l-3-3c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l3,3c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,23.26c-.06,0-.13-.01-.19-.04-.19-.08-.31-.26-.31-.46V12.66c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v8.89l2.15-2.15c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-3,3c-.1,.1-.22,.15-.35,.15Z"></path><path d="M15,11.16c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-left',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M7.45,15.16c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l3-3c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-3,3c-.1,.1-.23,.15-.35,.15Z"></path><path d="M10.45,18.16c-.13,0-.26-.05-.35-.15l-3-3c-.14-.14-.19-.36-.11-.54s.26-.31,.46-.31h10.1c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5H8.66l2.15,2.15c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M21.05,16.66c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-right',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M19.55,18.16c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l3-3c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-3,3c-.1,.1-.23,.15-.35,.15Z"></path><path d="M22.55,15.16H12.45c-.28,0-.5-.22-.5-.5s.22-.5,.5-.5h8.89l-2.15-2.15c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l3,3c.14,.14,.19,.36,.11,.54s-.26,.31-.46,.31Z"></path><path d="M8.95,16.66c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-top',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M18,11.16c-.13,0-.26-.05-.35-.15l-3-3c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l3,3c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,18.26c-.28,0-.5-.22-.5-.5V8.87l-2.15,2.15c-.2,.2-.51,.2-.71,0s-.2-.51,0-.71l3-3c.14-.14,.36-.19,.54-.11,.19,.08,.31,.26,.31,.46v10.1c0,.28-.22,.5-.5,.5Z"></path><path d="M15,23.26c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-left-right',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M5.69,15.71c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l2.54-2.54c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.23,.15-.35,.15Z"></path><path d="M8.23,18.25c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.14-.14-.19-.36-.11-.54s.26-.31,.46-.31h4.94c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5h-3.73l1.68,1.68c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M21.77,18.25c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l2.54-2.54c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.23,.15-.35,.15Z"></path><path d="M24.31,15.71h-4.94c-.28,0-.5-.22-.5-.5s.22-.5,.5-.5h3.73l-1.68-1.68c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.14,.14,.19,.36,.11,.54s-.26,.31-.46,.31Z"></path><path d="M15,17.22c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-top-bottom',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M17.54,8.93c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,11.34c-.28,0-.5-.22-.5-.5v-3.73l-1.68,1.68c-.2,.2-.51,.2-.71,0s-.2-.51,0-.71l2.54-2.54c.14-.14,.36-.18,.54-.11,.19,.08,.31,.26,.31,.46v4.94c0,.28-.22,.5-.5,.5Z"></path><path d="M15,25.02c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,25.02c-.06,0-.13-.01-.19-.04-.19-.08-.31-.26-.31-.46v-4.94c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v3.73l1.68-1.68c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.22,.15-.35,.15Z"></path><path d="M14.99,17.21c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path></svg>`
  },
  {
    name: 'direction-left-right-top-bottom',
    data: `<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30"><path d="M5.69,15.71c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l2.54-2.54c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.23,.15-.35,.15Z"></path><path d="M8.23,18.25c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.14-.14-.19-.36-.11-.54s.26-.31,.46-.31h4.94c.28,0,.5,.22,.5,.5s-.22,.5-.5,.5h-3.73l1.68,1.68c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M21.77,18.25c-.13,0-.26-.05-.35-.15-.2-.2-.2-.51,0-.71l2.54-2.54c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.23,.15-.35,.15Z"></path><path d="M24.31,15.71h-4.94c-.28,0-.5-.22-.5-.5s.22-.5,.5-.5h3.73l-1.68-1.68c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.14,.14,.19,.36,.11,.54s-.26,.31-.46,.31Z"></path><path d="M15,17.22c-1.1,0-2-.9-2-2s.9-2,2-2,2,.9,2,2-.9,2-2,2Zm0-3c-.55,0-1,.45-1,1s.45,1,1,1,1-.45,1-1-.45-1-1-1Z"></path><path d="M17.54,8.93c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,11.34c-.28,0-.5-.22-.5-.5v-3.73l-1.68,1.68c-.2,.2-.51,.2-.71,0s-.2-.51,0-.71l2.54-2.54c.14-.14,.36-.18,.54-.11,.19,.08,.31,.26,.31,.46v4.94c0,.28-.22,.5-.5,.5Z"></path><path d="M15,25.02c-.13,0-.26-.05-.35-.15l-2.54-2.54c-.2-.2-.2-.51,0-.71s.51-.2,.71,0l2.54,2.54c.2,.2,.2,.51,0,.71-.1,.1-.23,.15-.35,.15Z"></path><path d="M15,25.02c-.06,0-.13-.01-.19-.04-.19-.08-.31-.26-.31-.46v-4.94c0-.28,.22-.5,.5-.5s.5,.22,.5,.5v3.73l1.68-1.68c.2-.2,.51-.2,.71,0s.2,.51,0,.71l-2.54,2.54c-.1,.1-.22,.15-.35,.15Z"></path></svg>`
  },
  {
    name: 'gear',
    data: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"/></svg>`
  },
  {
    name: 'right-left',
    data: `<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M32 96l320 0V32c0-12.9 7.8-24.6 19.8-29.6s25.7-2.2 34.9 6.9l96 96c6 6 9.4 14.1 9.4 22.6s-3.4 16.6-9.4 22.6l-96 96c-9.2 9.2-22.9 11.9-34.9 6.9s-19.8-16.6-19.8-29.6V160L32 160c-17.7 0-32-14.3-32-32s14.3-32 32-32zM480 352c17.7 0 32 14.3 32 32s-14.3 32-32 32H160v64c0 12.9-7.8 24.6-19.8 29.6s-25.7 2.2-34.9-6.9l-96-96c-6-6-9.4-14.1-9.4-22.6s3.4-16.6 9.4-22.6l96-96c9.2-9.2 22.9-11.9 34.9-6.9s19.8 16.6 19.8 29.6l0 64H480z"/></svg>`
  },
  {
    name: 'folder',
    data: `<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M0 96C0 60.7 28.7 32 64 32H196.1c19.1 0 37.4 7.6 50.9 21.1L289.9 96H448c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zM64 80c-8.8 0-16 7.2-16 16V416c0 8.8 7.2 16 16 16H448c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16H286.6c-10.6 0-20.8-4.2-28.3-11.7L213.1 87c-4.5-4.5-10.6-7-17-7H64z"/></svg>`
  },
  {
    name: 'pencil',
    data: `<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M410.3 231l11.3-11.3-33.9-33.9-62.1-62.1L291.7 89.8l-11.3 11.3-22.6 22.6L58.6 322.9c-10.4 10.4-18 23.3-22.2 37.4L1 480.7c-2.5 8.4-.2 17.5 6.1 23.7s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L387.7 253.7 410.3 231zM160 399.4l-9.1 22.7c-4 3.1-8.5 5.4-13.3 6.9L59.4 452l23-78.1c1.4-4.9 3.8-9.4 6.9-13.3l22.7-9.1v32c0 8.8 7.2 16 16 16h32zM362.7 18.7L348.3 33.2 325.7 55.8 314.3 67.1l33.9 33.9 62.1 62.1 33.9 33.9 11.3-11.3 22.6-22.6 14.5-14.5c25-25 25-65.5 0-90.5L453.3 18.7c-25-25-65.5-25-90.5 0zm-47.4 168l-144 144c-6.2 6.2-16.4 6.2-22.6 0s-6.2-16.4 0-22.6l144-144c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6z"/></svg>`
  },
  {
    name: 'save-config',
    data: `<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 512 512"><title>ionicons-v5-m</title><polyline points="160 368 32 256 160 144" style="fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:42px"/><polyline points="352 368 480 256 352 144" style="fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:42px"/><polyline points="192 288.1 256 352 320 288.1" style="fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:42px"/><line x1="256" y1="160" x2="256" y2="336.03" style="fill:none;stroke:#000;stroke-linecap:round;stroke-linejoin:round;stroke-width:42px"/></svg>`
  },
  {
    name: 'code',
    data: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">   <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 8-4 4 4 4m8 0 4-4-4-4m-2-3-4 14"/> </svg>`
  },
  {
    name: 'clipboard',
    data: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-clipboard" viewBox="0 0 16 16">
  <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1z"/>
  <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0z"/>
</svg>`
  },
  {
    name: 'arrow-left',
    data: `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" fill="currentColor">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path d="M14 7l-5 5 5 5V7z"/>
</svg>`
  },
  {
    name: 'arrow-right',
    data: `<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24" fill="currentColor">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path d="M10 17l5-5-5-5v10z"/>
</svg>`
  }
  // {
  //   name: "no-space",
  //   data: `<svg class="w-[20px] h-[20px] text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/></svg>`
  // }
]
