<div #toastRef class="alert alert-{{message?.style}} fade position-fixed toast-container translate-middle-x align-items-center border-0 m-3" role="alert" aria-live="assertive" aria-atomic="true">
  <div class="d-flex" *ngIf="message">
    <div class="toast-body">
      <i [ngClass]="message.icon" *ngIf="message.icon"></i>
      {{ message.message }}
    </div>
    <div class="ms-auto">
      <button type="button" class="btn badge btn-{{action.style || message.style}}" (click)="onAction(action)" *ngFor="let action of message.actions">
        <i [ngClass]="action.icon" *ngIf="action.icon"></i>
        {{action.text}}
      </button>
    </div>
    <button type="button" class="btn-close ms-2" (click)="toast.hide()" data-bs-dismiss="toast" aria-label="Close"></button>
  </div>
</div>
