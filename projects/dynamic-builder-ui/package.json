{"name": "dynamic-ui-builder", "version": "1.0.0", "author": "thanhdeva<PERSON>", "description": "An Angular library", "keywords": [], "license": "MIT", "scripts": {}, "ng-add": {"save": "dependencies"}, "peerDependencies": {"@angular/common": "^14.1.0", "@angular/core": "^14.1.0", "@ngrx/store": "^14.3.2", "@popperjs/core": "^2.11.5", "bootstrap": "^5.2.0", "immer": "^9.0.16", "ngrx-wieder": "^9.0.0", "ngx-drag-drop": "^2.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@types/bootstrap": "^5.1.6", "@angular/localize": "^14.1.0", "copyfiles": "file:../../node_modules/copyfiles", "typescript": "file:../../node_modules/typescript"}}