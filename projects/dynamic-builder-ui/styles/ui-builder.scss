:root {
  --padding: 0;
  --margin: 0;
  --menu-top: -18px;
  --menu-left: 0px;
}
$main-color: #141ed2;

/* bootstrap encapsulated inside "uib-bootstrap" namespace */
@import "_uib-bootstrap";

uib-toolbar {
  position: fixed;
  bottom: 0px;
  border-radius: 8px!important;
  right: 10px;
  z-index: 1046; // just above the configurator
}

/* zone's styles */
@import "_zone";

.uib-dropzone {
  display: flex;
  flex-grow: 1;
  min-width:32px;
  min-height:32px;
}

[uib-configurable] {
  min-height: 8px;
  min-width: 8px;
}

.dragPlaceholder {
  @include white-stripes;
  outline: 2px dashed orange;

	flex-grow: 1;

  min-height: 16px;
  min-width: 16px;

  z-index: 99;
}

svg-icon svg {
  min-width: 20px;
  fill: $main-color;
}
.form-control {
  height: 40px!important;
}
