.uib-bootstrap {
  // scss-docs-start import-stack
  // Configuration
  @import "bootstrap/scss/functions";
  @import "bootstrap/scss/variables";
  @import "bootstrap/scss/variables-dark";
  @import "bootstrap/scss/maps";
  @import "bootstrap/scss/mixins";
  @import "bootstrap/scss/utilities";
  // Note: Custom variable values only support SassScript inside `#{}`.

  // Colors
  //
  // Generate palettes for full colors, grays, and theme colors.

  @each $color, $value in $colors {
    --#{$prefix}#{""+$color}: #{$value};
  }

  @each $color, $value in $grays {
    --#{$prefix}gray-#{""+$color}: #{$value};
  }

  @each $color, $value in $theme-colors {
    --#{$prefix}#{""+$color}: #{$value};
  }

  @each $color, $value in $theme-colors-rgb {
    --#{$prefix}#{""+$color}-rgb: #{$value};
  }

  --#{$prefix}white-rgb: #{to-rgb($white)};
  --#{$prefix}black-rgb: #{to-rgb($black)};
  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};
  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};

  // Fonts

  // Note: Use `inspect` for lists so that quoted items keep the quotes.
  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172
  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};
  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};
  --#{$prefix}gradient: #{$gradient};

  // Root and body
  // stylelint-disable custom-property-empty-line-before
  // scss-docs-start root-body-variables
  @if $font-size-root != null {
    --#{$prefix}root-font-size: #{$font-size-root};
  }
  --#{$prefix}body-font-family: #{$font-family-base};
  @include rfs($font-size-base, --#{$prefix}body-font-size);
  --#{$prefix}body-font-weight: #{$font-weight-base};
  --#{$prefix}body-line-height: #{$line-height-base};
  --#{$prefix}body-color: #{$body-color};
  @if $body-text-align != null {
    --#{$prefix}body-text-align: #{$body-text-align};
  }
  --#{$prefix}body-bg: #{$body-bg};
  // scss-docs-end root-body-variables

  // scss-docs-start root-border-var
  --#{$prefix}border-width: #{$border-width};
  --#{$prefix}border-style: #{$border-style};
  --#{$prefix}border-color: #{$border-color};
  --#{$prefix}border-color-translucent: #{$border-color-translucent};

  --#{$prefix}border-radius: #{$border-radius};
  --#{$prefix}border-radius-sm: #{$border-radius-sm};
  --#{$prefix}border-radius-lg: #{$border-radius-lg};
  --#{$prefix}border-radius-xl: #{$border-radius-xl};
  --#{$prefix}border-radius-2xl: #{$border-radius-2xl};
  --#{$prefix}border-radius-pill: #{$border-radius-pill};
  // scss-docs-end root-border-var

  --#{$prefix}link-color: #{$link-color};
  --#{$prefix}link-hover-color: #{$link-hover-color};

  --#{$prefix}code-color: #{$code-color};

  --#{$prefix}highlight-bg: #{$mark-bg};
  --#{$prefix}btn-bg: #141ed2;

  $tooltip-bg: #141ed2;

  // Layout & components
  @import "bootstrap/scss/reboot";
  @import "bootstrap/scss/type";
  @import "bootstrap/scss/images";
  @import "bootstrap/scss/containers";
  @import "bootstrap/scss/grid";
  @import "bootstrap/scss/tables";
  @import "bootstrap/scss/forms";
  @import "bootstrap/scss/buttons";
  @import "bootstrap/scss/transitions";
  @import "bootstrap/scss/dropdown";
  @import "bootstrap/scss/button-group";
  @import "bootstrap/scss/nav";
  @import "bootstrap/scss/navbar";
  @import "bootstrap/scss/card";
  @import "bootstrap/scss/accordion";
  @import "bootstrap/scss/breadcrumb";
  @import "bootstrap/scss/pagination";
  @import "bootstrap/scss/badge";
  @import "bootstrap/scss/alert";
  @import "bootstrap/scss/progress";
  @import "bootstrap/scss/list-group";
  @import "bootstrap/scss/close";
  @import "bootstrap/scss/toasts";
  @import "bootstrap/scss/modal";
  @import "bootstrap/scss/tooltip";
  @import "bootstrap/scss/popover";
  @import "bootstrap/scss/carousel";
  @import "bootstrap/scss/spinners";
  @import "bootstrap/scss/offcanvas";
  @import "bootstrap/scss/placeholders";

  // Helpers
  @import "bootstrap/scss/helpers";

  // Utilities
  @import "bootstrap/scss/utilities/api";

  // Move reboot body rules in .uib-bootstrap:

  // Body
  //
  // 1. Remove the margin in all browsers.
  // 2. As a best practice, apply a default `background-color`.
  // 3. Prevent adjustments of font size after orientation changes in iOS.
  // 4. Change the default tap highlight to be completely transparent in iOS.

  // scss-docs-start reboot-body-rules
  margin: 0; // 1
  font-family: var(--#{$prefix}body-font-family);
  @include font-size(var(--#{$prefix}body-font-size));
  font-weight: var(--#{$prefix}body-font-weight);
  line-height: var(--#{$prefix}body-line-height);
  color: var(--#{$prefix}body-color);
  text-align: var(--#{$prefix}body-text-align);
  background-color: var(--#{$prefix}body-bg); // 2
  -webkit-text-size-adjust: 100%; // 3
  -webkit-tap-highlight-color: rgba($black, 0); // 4
  // scss-docs-end reboot-body-rules
  .offcanvas.offcanvas-end {
    top: 60px!important;
  }
  .offcanvas.offcanvas-start {
    top: 60px!important;
    left: 60px!important;
  }
  .modal {
    top: 60px!important;
  }
  * {
    border-radius: 0!important;
  }
}
