@mixin white-stripes {
  //--stripes-color: hsla(0, 0%, 100%, 0.4);
  //background-color: #f2f2f2;
  //background-image: linear-gradient(
  //  45deg,
  //  var(--stripes-color) 12.5%,
  //  transparent 12.5%,
  //  transparent 50%,
  //  var(--stripes-color) 50%,
  //  var(--stripes-color) 62.5%,
  //  transparent 62.5%,
  //  transparent 100%
  //);
  //background-size: 5.66px 5.66px;
}

/* when an element have 'edited' state */
@mixin edited {
  /* display current edited element */
  &.edited {
    @include white-stripes;

    background-color: aliceblue;
    outline: 1px dashed lightskyblue;

    /* inset shadow acts as a border */
    box-shadow: inset 0px 0px 0px 2px #fff;
  }
}
