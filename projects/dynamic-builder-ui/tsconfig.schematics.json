{"compilerOptions": {"baseUrl": ".", "lib": ["es2022", "dom"], "declaration": true, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitThis": true, "noUnusedParameters": true, "noUnusedLocals": true, "rootDir": "./schematics", "outDir": "../../dist/lib/schematics", "skipDefaultLibCheck": true, "skipLibCheck": true, "sourceMap": true, "strictNullChecks": true, "target": "ES2022", "types": ["jasmine", "node"]}, "include": ["schematics/**/*"], "exclude": ["schematics/*/files/**/*"]}