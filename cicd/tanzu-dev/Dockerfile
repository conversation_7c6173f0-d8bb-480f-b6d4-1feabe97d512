FROM k8sdev.mbbank.com.vn/common/node:18.20.1-alpine as builder

WORKDIR /build

COPY . .
RUN rm -rf package-lock.json
RUN rm -rf node_modules
RUN npm cache clean --force

RUN npm install
#
RUN npm run build-dev
# RUN ng build --configuration dev --aot --output-hashing=all

FROM k8sdev.mbbank.com.vn/support/nginx:1.21.4

COPY --from=builder /build/dist /usr/share/nginx/html
COPY cicd/tanzu-dev/L01/configmap-k8s/nginx-custom.conf /etc/nginx/conf.d/default.conf

# Add permissions for nginx user
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d
RUN touch /var/run/nginx.pid && chown -R nginx:nginx /var/run/nginx.pid

# Set the default user.
USER nginx


VOLUME /var/cache/nginx/
VOLUME /var/run/
VOLUME /var/log/nginx
