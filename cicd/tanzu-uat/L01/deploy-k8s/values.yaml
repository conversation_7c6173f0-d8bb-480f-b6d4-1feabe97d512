# So luong pod duoc tao
replicaCount: 1

# Khai bao user
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

# Khai bao gRPC user
gRPCEnable: false
gRPCService:
  nodePortgrpc: 10780
  portgrpc: 10780

# Khai bao hostnames
hostAliases:

# Khai bao su dung configmap
configMaps:
 - nginx-custom.conf

# Khai bao su dung PVC
pvc: false

# Khai bao volumeMount
volumeMountsMap:
  mountPathconfig: /deployment/config
  mountPathpodinfo: /deployment/podinfo

#Khai bao tai nguyen su dung cho pod
resources:
  limits:
    memory: 2Gi
  requests:
    memory: 1Gi

# Khai bao promethus
promethus: false
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8881"

# Khai bao label cho istio
labels: false
templateLabels:
  version: old
  sidecar.istio.io/inject: "true"

# <PERSON><PERSON> bao HealthCheck de gia tri healthCheck true,nguoc lai de false
healthCheck: false
startupProbe:
  failureThreshold: 5
  httpGet:
    path: /actuator/health
    port: 8881
    scheme: HTTP
  initialDelaySeconds: 10
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 2
livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /actuator/health
    port: 8881
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2
readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /actuator/health
    port: 8881
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2

# Khai bao bien moi truong
ENV_PROFILE: tanzu-dev
JVM_OPTS: -Xms128m -Xmx2048m -Dsun.net.http.retryPost=false -Dhttp.retryPost=false

Command:
  option: manual
  #binaryfile: MicroServiceFile.jar
  manual: "nginx -g 'daemon off;'"


# Chi dinh pod chay tren node co dinh(chi dung tren live: default hoac ecm)
NodeSelectorEnable: false
nodeName: default

# Khai bao hpa thi de gia tri hpa true,,nguoc lai de false
autoscaling:
  enabled: false
  minPodsHPA: 1
  maxPodsHPA: 4
  #cpuHPA: 80
  memoryHPA: 80

# So lan deploy Helm
numberHelm: H1

# Thong tin image
image:
  repository: image_version
  pullPolicy: Always
  tag: "image_tag"

# Khai bao secret-registry harbor
imagePullSecrets:
  - name: harbor-secret-registry

## Tao Service Account
serviceAccount:
  create: false
### Cau hinh dung vault tap chung
VaultInject: false


