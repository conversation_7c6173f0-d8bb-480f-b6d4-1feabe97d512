FROM k8sdev.mbbank.com.vn/common/node:18.20.1-alpine as builder

WORKDIR /build

COPY . .
RUN rm -rf node_modules
RUN npm cache clean --force

RUN npm install
#
RUN npm run build

FROM k8sdev.mbbank.com.vn/support/nginx:1.21.4

COPY --from=builder /build/dist /usr/share/nginx/html
COPY cicd/tanzu-uat/L01/configmap-k8s/nginx-custom.conf /etc/nginx/conf.d/default.conf

VOLUME /var/cache/nginx/
VOLUME /var/run/
VOLUME /var/log/nginx
