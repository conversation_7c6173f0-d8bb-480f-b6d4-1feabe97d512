# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/tmp
/out-tsc
/dist
# Only exists if <PERSON><PERSON> was run
/bazel-out


# profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
.angular/
/package-lock.json
/documentation/
/src/assets/layout/*.json.bak
/src/assets/layout/*.json
/.nx/cache/18.3.5-nx.win32-x64-msvc.node
/yarn.lock
node_modules
