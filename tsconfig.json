/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "useDefineForClassFields": false,
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "lib": ["ES2022", "dom"],
    "typeRoots": ["node_modules/@types", "src/types"],
    "skipLibCheck": true,
    "noImplicitOverride": true,
    "paths": {
      "@shared": ["src/app/shared"],
      "@shared/components/*": ["src/app/shared/components/*"],
      "@core/*": ["src/app/core/*"],
      "@pages/*": ["src/app/pages/*"],
      "@services": ["src/app/services"],
      "@services/*": ["src/app/services/*"],
      "@store/*": ["src/app/store/*"],
      "@lib/*": ["src/app/lib/*"],
      "@features/*": ["src/app/features/*"],
      "@env": ["src/environments"],
      "@env/*": ["src/environments/*"],
      "@mb/ngx-ui-builder": ["./projects/dynamic-builder-ui/src/public-api"],
      "@mb/ngx-ui-builder/*": ["./projects/dynamic-builder-ui/src/*"]
    }
  },
  "angularCompilerOptions": {
    "resolveJsonModule": true,
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
