const {withModuleFederationPlugin} = require("@angular-architects/module-federation/webpack");
module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'app_pbx',
    scriptType: 'text/javascript'
  },
  optimization: {
    // Only needed to bypass a temporary bug
    runtimeChunk: false
  },
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET,HEAD,OPTIONS,POST,PUT',
      'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
    }
  },
  plugins: [
    withModuleFederationPlugin({
      // For remotes (please adjust)
      name: 'app_pbx',
      library: {
        type: 'var',
        name: 'app_pbx'
      },
      filename: 'remoteEntry.js',
      exposes: {
        './web-components': './src/bootstrap.ts'
      },
      shared: {
        '@angular/core': {
          requiredVersion: '^14.1.0',
          singleton: true
        },
        '@angular/common': {
          requiredVersion: '^14.1.0',
          singleton: true
        },
        '@angular/router': {
          requiredVersion: '^14.1.0',
          singleton: true
        }
      }
    })
  ]
}
