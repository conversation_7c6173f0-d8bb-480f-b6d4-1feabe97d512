<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.1.0">
  <diagram name="Page-1" id="f76GAQ2J13BeVOgYoy0G">
    <mxGraphModel dx="5198" dy="1470" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-3" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="440" y="660" width="370" height="170" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="174" y="636" width="170" height="260" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-50" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="-20" y="600" width="986" height="650" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-28" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="420" y="636" width="500" height="584" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-54" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00FF00;entryPerimeter=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="1" source="pm5V1Q2rgflL0MZlzJiH-10" target="4VxmYRTt2rac8v3RHbJV-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="275" y="700" as="sourcePoint" />
            <mxPoint x="276" y="415" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-55" value="TCP/5432" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="ruG60msS0jveVvH4YD21-54" vertex="1" connectable="0">
          <mxGeometry x="0.2848" relative="1" as="geometry">
            <mxPoint y="-23" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-58" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#7F00FF;exitX=1;exitY=0.418;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="ruG60msS0jveVvH4YD21-28" target="4VxmYRTt2rac8v3RHbJV-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="930" y="880" as="sourcePoint" />
            <mxPoint x="1189" y="729.5" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1117" y="880" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-99" value="&lt;font style=&quot;font-size: 10px;&quot;&gt;Push message log&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 10px;&quot;&gt;Push message call request&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="ruG60msS0jveVvH4YD21-58" vertex="1" connectable="0">
          <mxGeometry x="-0.2684" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-71" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;" parent="1" vertex="1">
          <mxGeometry x="795.6" y="259" width="284" height="180" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-79" value="&lt;b&gt;DCMS SYSTEM - K8S TANZU&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="833.8" y="409" width="209.6" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-93" value="PBX-CMS Service" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#97D077;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="849.5999999999999" y="290.5" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-94" value="PBX-CMS Web" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#97D077;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="976.8499999999999" y="288.5" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-6" value="&lt;b style=&quot;font-size: 10px;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;Egress&amp;nbsp;&lt;/font&gt;&lt;/b&gt;&lt;div&gt;&lt;b style=&quot;font-size: 10px;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;&lt;span style=&quot;font-family: Arial;&quot;&gt;10.215.253.5&lt;/span&gt;&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#97D077;" parent="1" vertex="1">
          <mxGeometry x="665" y="327.5" width="105.6" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-9" value="&lt;b&gt;Kafka&amp;nbsp;&lt;/b&gt;&lt;div&gt;&lt;span style=&quot;font-family: Calibri;&quot;&gt;&lt;b&gt;LOG:&lt;/b&gt;&amp;nbsp;10.215.109.201/205/206/207/208/209: 9093&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;&lt;font face=&quot;Calibri&quot;&gt;&lt;b&gt;DATA:&lt;/b&gt;&amp;nbsp;&lt;/font&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;10.215.109.201/205/206/207/208/209:9092&lt;/span&gt;&lt;/font&gt;&lt;span style=&quot;font-family: Calibri; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&amp;nbsp;&lt;/span&gt;&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#CCCCCC;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="1093" y="695.07" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-11" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#CCCCCC;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="501" y="466.5" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.212;entryY=0.005;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00FF00;" parent="1" source="4VxmYRTt2rac8v3RHbJV-11" target="ruG60msS0jveVvH4YD21-28" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="610" as="sourcePoint" />
            <mxPoint x="528" y="695.0699999999997" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-13" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;Read source XML&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4VxmYRTt2rac8v3RHbJV-12" vertex="1" connectable="0">
          <mxGeometry x="0.1378" y="-4" relative="1" as="geometry">
            <mxPoint x="5" y="-58" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-18" value="&lt;b style=&quot;font-size: 10px;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;Ingress&lt;/font&gt;&lt;/b&gt;&lt;div&gt;&lt;b style=&quot;font-size: 10px;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;&lt;span style=&quot;font-family: Arial;&quot;&gt;10.215.254.168&lt;/span&gt;&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#97D077;" parent="1" vertex="1">
          <mxGeometry x="884.8" y="194" width="105.6" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#00FF00,#FFFFFF);" parent="1" source="ruG60msS0jveVvH4YD21-71" target="4VxmYRTt2rac8v3RHbJV-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="670" y="636" as="sourcePoint" />
            <mxPoint x="720" y="586" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="4VxmYRTt2rac8v3RHbJV-18" target="ruG60msS0jveVvH4YD21-71" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="670" y="636" as="sourcePoint" />
            <mxPoint x="951" y="246" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-22" value="" style="edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00FF00;" parent="1" source="4VxmYRTt2rac8v3RHbJV-6" target="4VxmYRTt2rac8v3RHbJV-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="636" as="sourcePoint" />
            <mxPoint x="600" y="586" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-23" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00FF00;" parent="1" target="4VxmYRTt2rac8v3RHbJV-11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="525" y="348" as="sourcePoint" />
            <mxPoint x="525" y="486" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#7F00FF;" parent="1" source="4VxmYRTt2rac8v3RHbJV-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="820" y="530" as="sourcePoint" />
            <mxPoint x="720" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-25" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;TCP/8080 Websocket&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4VxmYRTt2rac8v3RHbJV-24" vertex="1" connectable="0">
          <mxGeometry x="-0.3262" y="3" relative="1" as="geometry">
            <mxPoint y="89" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-36" value="" style="edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;endSize=8;startSize=8;strokeColor=#7F00FF;" parent="1" target="4VxmYRTt2rac8v3RHbJV-9" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="720" y="520" as="sourcePoint" />
            <mxPoint x="1150" y="640" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1117" y="520" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-37" value="TCP/9093" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4VxmYRTt2rac8v3RHbJV-36" vertex="1" connectable="0">
          <mxGeometry x="-0.2463" relative="1" as="geometry">
            <mxPoint x="182" y="100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-38" value="&lt;font color=&quot;#ffffff&quot; style=&quot;font-size: 18px;&quot;&gt;MB INTERNAL&lt;/font&gt;" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fillColor=#3333FF;" parent="1" vertex="1">
          <mxGeometry x="-600" width="2080" height="1280" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-40" value="CHANNEL" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;rotation=0;startSize=50;" parent="4VxmYRTt2rac8v3RHbJV-38" vertex="1">
          <mxGeometry y="40" width="2080" height="140" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-28" value="&#xa;" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;fillColor=#29FF29;" parent="4VxmYRTt2rac8v3RHbJV-40" vertex="1">
          <mxGeometry x="131" y="17.24000000000001" width="58.6" height="107.76" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-45" value="Softphone&amp;nbsp;&lt;div style=&quot;padding: 0px; margin: 0px;&quot;&gt;&lt;br style=&quot;padding: 0px; margin: 0px; text-wrap: nowrap; background-color: rgb(255, 255, 255);&quot;&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="4VxmYRTt2rac8v3RHbJV-40" vertex="1">
          <mxGeometry x="69.6" y="60" width="60" height="39" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-1" value="  Web: https://pbx-dev.mbbank.com.vn" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAGEAAABUCAIAAAAd087qAAAFQElEQVR4Ae2c0U/bVhTG+39aSOlDXlIh8ZIXuolNyguatEmjW4WsJA1xhlMnLglZU6YOxIqGYKDAMlsJS1IzRLoCZR2YIKcWHuhMt54d7GubdCQ9lh987OPj65/P9zkh2HcMnNwI3HFLuO3bNU3L5sRMf6ZsTtQ0beAZqaqa5vMMK/VjTvN5VVWRkRNcZOREB7oSGSGjm3Coj72P8psH9nu2xfiRETJy01qspOQ3Dywz9pG7SdMy2qj8Wny6MCjzXKnMfTc7lS64zp8mF80IAvURL4jR5MpofG2Y5mhyhU0LZkaBPJsXxHC8Yi43BMvheAUZuXiNnRHNhb/23v+R9NGNMRrjd8403f5ZayDWnGn6GL8DLPrYR7GSIrdVGuS3MEduq7GSgoycbAgZOdGB3kFGyMjt2xaN/WEfYR9hH9EoJXgOag21hloLriOaCqg11BpqjUYpwXNQa6g11FpwHdFUQK2h1lBrNEoJnoNaQ62h1oLriKYCag21hlqjUUrwHNQaag21FlxHNBVQa6g11BqNUoLnoNZQa6i14DqiqYBaQ62h1miUEjznA2ltoP/3uKtfRIUGsO7j/x4Hv5i3pAIycvdvZPTBGeWeFBLZYjxbutmZ5eddHzGbShemZ+e9Hvdhpuha+Zv03HQ651X41z5To6rqn32YxELpfnLJ+Zm4aHKFf5zzdHBFUTg+51wWtvp44OxaRn16Xqb4dGE0vuZ8JcPxCi+IngYwVO/SQEbu1x4ZISN3Au4Z2EfIyDCarZeZAZk+e/Sj892TfquHe//x8fGMMD/CVumr/4+Zk6nyJ6mfbmQAtIw0TePFoo8PYAFHOVFoxUpKrKSEU7VQQoZleD42wtUhHBeb9qOMsNWpdOFefN2+yesaWkZ2r+XXX8ttVW6riZU2w0rFrUMIp5f3GVb6ufE3hHA+23unEEaFRoSrw7LcViNcPSo0INzeO2VY6cHiHriacnTOsFK5+gbCDeWEYaXl+l8QFrcOQwl5e+8UQjio5eSvvsFy+bvslmW915CK0crqL5/PLJtLkzM5OOmGU7XJ8i6MtatfRLj6uNgkYThVI6FhGBGuPsbvwFbDMMb4nQhXJ+G42LzM7+oXsGZcbF5WI+FkeTecqh2cdGHrg8W9y+ZSjs4h7IlpNL72kJsL6A/ujJqtl9/yz8yAGFYiEoBH58fFprntx/gdCCcKLYaViCigp8ySCSVkhpWIgiJc3aE4/KnMUjycqpHdLYOE8H5y6YuZZzRf5a7L4fic03v97D5tPuFwqmY+JfAF86AhwULQXsGS4FABoLsmWGB9lVngs499v4Nn4fmSruu9331o9+lQQia9DW9FiJUUopT06iuzg3T1CzBa8o4J8JTp5X2yy5c//MGwErGVM02HviMJ5eobhpXSq6/ImolCyzyM3193Qgl5otAiksxvHlgYjbDVR8L3h4eHpIiPhd6M7D69oZxAdXhBRSghE3d423l3GZrtAzzYDBFO2MwITIQYs2EY0IxyW4UDgcGFEvLbzjtYoxydhxJyVGgQKIB+srxL1tgx3WW3ZvPFTqfjgw7s0oOR3actF2fgwnvx9SelBV33+eIUK6OePu0DyoZyQm7zARd63ra8Dmli5sXSi1V/rfQfRnaf9joUyDff4/0Ny7wXaNnfSMx7fc0//02umStTLr9nZPdp8wE8LUeFhuV9eQFD8jOZp2FYkn3793tGdp+2HGMIQn/+/S+j4fPp666oD/++YrS/v5/JZLz+YjO4+dxsdrOyTWlGhmFcMdJ13dPPNUOQrGkaPaN/AMoJEJHubjakAAAAAElFTkSuQmCC;" parent="4VxmYRTt2rac8v3RHbJV-40" vertex="1">
          <mxGeometry x="1499.65" y="5" width="73.9" height="64" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-4" value="&lt;div style=&quot;padding: 0px; margin: 0px;&quot;&gt;Web/App&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="241" y="52.62" width="60" height="39" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-8" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="510" y="25" width="739" height="90" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-11" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;AI Platform&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="540" y="47.57000000000005" width="110" height="47.43" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-13" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;T24&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="680" y="48.75" width="110" height="46.25" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-14" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Way4&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="820" y="47.57000000000005" width="110" height="47.43" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-16" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;DCMS&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="961.5" y="47.57000000000005" width="110" height="47.43" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-17" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;...&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="1107" y="47.57000000000005" width="110" height="47.43" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-30" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://png.pngtree.com/png-vector/20220520/ourmid/pngtree-blue-browser-icon-internet-and-network-vectors-sign-buttons-hand-vector-png-image_46366036.jpg;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-40">
          <mxGeometry x="309" y="29.96" width="89.04" height="89.04" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-41" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="4VxmYRTt2rac8v3RHbJV-1" target="4VxmYRTt2rac8v3RHbJV-18" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="938" y="40" as="sourcePoint" />
            <mxPoint x="630" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-46" value="CONTACT CENTER" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;startSize=50;" parent="1" vertex="1">
          <mxGeometry x="-600" y="182" width="2080" height="1098" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-56" value="&lt;b&gt;&lt;font style=&quot;font-size: 24px;&quot;&gt;PBX SYSTEM - DC&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="4VxmYRTt2rac8v3RHbJV-46" vertex="1">
          <mxGeometry x="600" y="1028" width="214.6" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ruG60msS0jveVvH4YD21-31" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;MEDIA SERVER&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="4VxmYRTt2rac8v3RHbJV-46" vertex="1">
          <mxGeometry x="1000" y="1008" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-15" value="&lt;div&gt;&lt;b style=&quot;text-wrap: nowrap;&quot;&gt;&lt;br&gt;&lt;/b&gt;&lt;/div&gt;&lt;b style=&quot;text-wrap: nowrap;&quot;&gt;Postgres DB&lt;/b&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;10.215.107.44:5432&lt;/div&gt;" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.database;whiteSpace=wrap;fillColor=#E6E6E6;shadow=1;" parent="4VxmYRTt2rac8v3RHbJV-46" vertex="1">
          <mxGeometry x="669" y="238" width="113.9" height="70" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-98" value="&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;&lt;b&gt;NAS&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;&lt;font style=&quot;font-size: 10px;&quot;&gt;&lt;b&gt;&amp;nbsp;&lt;/b&gt;&lt;span style=&quot;background-color: initial; text-wrap: wrap;&quot;&gt;dev-pbx-cms-volume&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;text-wrap: nowrap;&quot;&gt;&lt;span style=&quot;font-size: 10px; background-color: light-dark(rgb(255, 255, 255), rgb(18, 18, 18)); color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Port: 111, 2049, 20048&lt;/span&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="4VxmYRTt2rac8v3RHbJV-46" vertex="1">
          <mxGeometry x="950" y="278" width="160" height="82" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-111" value="Kibana" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://marketplace-assets.digitalocean.com/logos/sharklabs-kibana.svg;" parent="4VxmYRTt2rac8v3RHbJV-46" vertex="1">
          <mxGeometry x="1930" y="511.07000000000005" width="79" height="79" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-5" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;entryX=0.006;entryY=0.133;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="4VxmYRTt2rac8v3RHbJV-46" target="pm5V1Q2rgflL0MZlzJiH-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="-22" as="sourcePoint" />
            <mxPoint x="821" y="741" as="targetPoint" />
            <Array as="points">
              <mxPoint x="353" y="608" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-7" value="WebRTC / WSS" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="eTBQ91r6Va6702sbMowr-5">
          <mxGeometry x="-0.4918" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-29" value="&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;&lt;b&gt;Redis:&lt;/b&gt;&amp;nbsp;10.1.39.108/109/110: 6379&amp;nbsp;&lt;/span&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#CCCCCC;gradientColor=none;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-46">
          <mxGeometry x="1700" y="881" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-48" value="&lt;font style=&quot;font-size: 18px;&quot; color=&quot;#ffffff&quot;&gt;DMZ ZONE&lt;/font&gt;" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fillColor=#3333FF;" parent="1" vertex="1">
          <mxGeometry x="1480" y="2" width="393" height="1278" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-115" value="" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="4VxmYRTt2rac8v3RHbJV-48" vertex="1">
          <mxGeometry x="60" y="777" width="190" height="283" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-119" value="&lt;div&gt;&lt;b style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;SBC&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;&lt;div&gt;&lt;b style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;(Kamailio)&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="4VxmYRTt2rac8v3RHbJV-48" vertex="1">
          <mxGeometry x="60" y="787" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-120" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#000000,#FFFFFF);align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;edgeStyle=orthogonalEdgeStyle;fillColor=#FF0000;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="4VxmYRTt2rac8v3RHbJV-48" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="160.0000000000009" y="941" as="sourcePoint" />
            <mxPoint x="160" y="890.0000000000005" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-121" value="Active-Standby" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;" parent="pm5V1Q2rgflL0MZlzJiH-120" vertex="1" connectable="0">
          <mxGeometry x="-0.0114" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-122" value="&lt;b&gt;&lt;br style=&quot;forced-color-adjust: none; padding: 0px; margin: 0px;&quot;&gt;10.1.10.101&lt;br style=&quot;forced-color-adjust: none; padding: 0px; margin: 0px;&quot;&gt;NAT:&amp;nbsp;103.12.105.250&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;align=center;" parent="4VxmYRTt2rac8v3RHbJV-48" vertex="1">
          <mxGeometry x="100" y="1008" width="120" height="39" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-21" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;rotation=-90;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-48">
          <mxGeometry x="131.75" y="794.25" width="50" height="140.5" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-22" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;rotation=-90;" vertex="1" parent="4VxmYRTt2rac8v3RHbJV-48">
          <mxGeometry x="130" y="898" width="50" height="140.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-57" value="&lt;font style=&quot;font-size: 18px;&quot; color=&quot;#ffffff&quot;&gt;INTERNET&lt;/font&gt;" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fillColor=#3333FF;" parent="1" vertex="1">
          <mxGeometry x="1800" y="2" width="320" height="1278" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-59" value="&lt;div&gt;&lt;b&gt;Telco CMC/FPT&lt;/b&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;&amp;nbsp;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: initial; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;118.69.114.182&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Đầu số:&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;***********&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="4VxmYRTt2rac8v3RHbJV-57" vertex="1">
          <mxGeometry x="94.72" y="811" width="122.28" height="58" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-60" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAADgAAABnCAIAAADaPBZrAAAI0klEQVR4Ae1baXPbxhnmp/6HfunH/o9Ov/YfWJJrJbWV2GNZY7tJJqlbx9OM09RO2jRNZkTdEmXrsOTQdnSLouTYOiiSOHjfN0ESJAESN5adBUiKom2O6GFlOwPMM5zF7rvvPu+D3QUG4KurvCOH7h3hWfklEgWgUmaYIkUVKSqbzcYbjlg87vIGbJjTijoQhzsYCjc0xhOJBJHJqB3pUkmS5de4jMcUBQBwHJ/PF9IEEY8nPP4g6vCgDo/djthsdpvN9vQAMe2hpj30oRkZWrYfYcn+LyPy93nk1hxyewH57+OGpmX72CqyuI2pHc376KEVurKjOOJwYy5vIBhOJpOZTKZULoui+KoYjohSdCkQDB/YHWu7jtktfGwV/e4x8o8FiI+nkWtTEANT6JVJiEvj6IXRY/jTCNY7jJ0bhr/nR7DG1r4x9NI4pnYcmERVV3+bhSF9tYh8/wSZXEfnzdgzqwPBXaFo4qWSQ6IAVApF6tAZGlzGPr2PXDOglyfQi2PohVHsvWGIbj1+ZrDD+OMQ/t4w9v4I1jcKw+6fQD++h96cRQwbuCcQFkUJHNdWB0BFFCVvIDy4jF0cx7o6TaitCHv0+NUp9MdtJEnkBEkCDWQh0WKR2j50fTaDvFmWakg9evyfi3Yr7iuznNxENJ0mFnccV6fQtqL/Pxl36/GPDMj2PlqkGUk+0lQng0oimbq3ifVPIG2N/cGE6/aT0A+meAt8txG78TDQ1c4U79bj1w3I1h5CUmVBBnWqOkkGscTrEL0+61s4zFgjdAs88xeHdxI9Q46TS6ASNe0imUKJF+UjoqIEovHkayh68rHbslSJbu4iRKHEiXL95qATJTkafx1F2xr+5MaNRFlRlmp3MZVocrr9OXrysduyrBNN50uMIIm1a69TL/07RrR3xPntevSZv7AXKJ4+nqDZ/mnPiRTtn/bMHhAmd/7b9dgpY+p5asdb+GopfCKiA/e9swfE3AFxfsx1yvjcGFx1kN+sRU9KdGY/bdhNtTX9O2L8ybx/CctpRDv3qKUp2jkt1SmuKaopqu2jHZoD2mLqkJD126+mqKaotj11aA5oi6lDQmrbU6eF1BTVFNW2p07PAU3RU1YUyMde5A7c975d7554QWQioLBdSY3qJMKYCO7c2zjon0DeHqLm3WfZmIlPGCT/pwD9A3j2G52Mn0m7hu+vP32riD59vl5w3Zasvwc//xps/QqYdDqRNEejPvXV+NujKPwqQoS49GPJdw3Yfgd2f3vsHf5bRfToY4NAVvKmd4Go8plZI9rmRqvdmdoUrP6A/KqCpqimqPampENzQFtMHRKyvltpimqKattTh+aAtpg6JKS2PXVaSE1RTVFte+r0HNAU1RT9xT09zVuIx0j2oznfKeOb1eiWO3/Sv2VenHIbdlMHIeoxkj1lbDjJHW/+ljF4okt/btjx2YL/+83YG8GdlciHk+4TEa0/IrzBQoeJdg3i7485Lxs8vaPOelTnx12Xpz3nhqsJFz1Djr4J12XDUU3dskWhw0T7Jlx3VyIPDomvVyNnlVSQs0OOQXN84ZD43BjsHYHsr9zzDJrjcxbi1qNgC2ZNTS2JKkktVybbyL65ZQzaozQAFT/B9k24uwbxDybdZAlmzK07yeuz3jOD+H82YjGSYwTZ7Mk3sWlx2opoPEUsbMPUrBb9m5puLAae+goFRrRF6Avjrq5BvG/CFcqyFCsabZmB+5Do3ZWIK1kmKH4JyzV1b3EK85mmYQqi8lVEbkzBkAmyYLY4/zLThqLdevzqjPfOSqR/2lMf9ZMH/rsr4Q8n3WpN74jzxmLgzvJRTd2yRaFHj3/5wLqHejNFhhXkeu6VTpRBiRMRl//fRvv5EayFi6amLj3erXc0JlVVaxoeA16saXLSdNo1iF8YxdZ2LIEkmSvxKlE1SRASLfNSIkc/R3xjy7abM/ZL4+jZoQ7nLDYRevG0dxj7swG5/cBmNNsQXyxOMgVG4ERJruXdwQwxVpDJshAhKKsntrHveLRtNZoOHm7uz65bJlasw0u2Lx9Yv5i3/nXGDlNDJ9GBSZiJ+eJgr6rp0eN9o7CXipuz9i/mbV8/tA0tWQ1r1vkNi3Fz/9GW5aef0S2rBwulQ5lSmuIpTlQyxKpZlzoZAF6UaU7K0HwkW/LESWck4wwTjlDa7o9b3JE9Z9hk9ZosnqXnzh+30QWzfcFsn9+0zqwdNGJq5XBoyar/yTq6bJ1etTQ2zawdzG0eqh0XzPbVfdemxbNj9+05QlZPFA0kneG0I5x2RrOeRD6ULSeLHFkWyrwkSKAmaEUHKhVV1CIrEjQfL3ARkg3n2FCWCRAlX5rypihPnIQBRLN4OI2FIPBg0hFI4IG4ggQeSNi8kX1naM8RsrjCiC+GVZuqBlgwWe0YTruiWXcs50nkvcmiL00HMuVglgllmTDJxvJsSmFJcxIvAphxV8u4hJm2MgCiMgEoViTLAkHzqSKfLELS8QIXy3NRko2SrBpAOKeEASNhQzk2mGWCWfjrT9O+ZBEiRfmJUiDDBDKM0sqEasawL8lGctCV6jOWh0MkClyyyKdpIVsS8gzUklOS2OosK5Va2rrKlRdlhpdoTiqyYoER82XIO1cSsrSQoWEABMWnKT5FwUhgMAU+oQQTz3OxPNSjEfEjElyyyKWKXFrpTlB8huazNPScKwtkWciXhQIjUpxU4iVWkAVJ2ZVqWlZXfXWuKrpKMhBlmZdkTlQBGEEu83KZl0ocDIDmJIoViwoKjFgoV4MhS8JLoDDIMzDmAgN70WzVSYmTyrzE8DIjyKwgcwIcjherFOEFbxRToXiUDa5mWwMAc0ZlJXNUBhVJmRWiLIsSECRZkODKq4UBC6wyEivIcNTjUJsUY1DjoTqBfkQJ3nUkGUigIkPAcatHXbyGwjGi9XoAKqDSACVCxYviFHqHAyhQyjJckS8BNFMtqyujzkVx2TCEkpReJ/Bi4eVEX7RrqoGRqGiM51XlmnGTk7ZOX5NoW2N0xPh/grYp04H+y64AAAAASUVORK5CYII=;" parent="4VxmYRTt2rac8v3RHbJV-57" vertex="1">
          <mxGeometry x="129" y="619" width="53.03" height="97.52" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-61" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" parent="4VxmYRTt2rac8v3RHbJV-57" vertex="1">
          <mxGeometry x="207" y="627.76" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="iZH3BQpPbOzT9sAwNXRf-2" value="&lt;div&gt;&lt;font style=&quot;font-size: 12px;&quot;&gt;TCP/UDP: 5060&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 12px;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;UDP: 10000-40000&amp;nbsp;&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="4VxmYRTt2rac8v3RHbJV-57" vertex="1" connectable="0">
          <mxGeometry x="139.99795038539878" y="987.9960754846338" as="geometry">
            <mxPoint x="22" y="-26" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-96" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" parent="4VxmYRTt2rac8v3RHbJV-57" source="4VxmYRTt2rac8v3RHbJV-59" target="4VxmYRTt2rac8v3RHbJV-60" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-373" y="728" as="sourcePoint" />
            <mxPoint x="30" y="728" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-73" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#97D077;" parent="1" vertex="1">
          <mxGeometry x="-920" y="22" width="70" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-74" value="&lt;div style=&quot;padding: 0px; margin: 0px;&quot;&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;Cấu phần của ứng dụng&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-850" y="35.25" width="171" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-75" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#7EA6E0;" parent="1" vertex="1">
          <mxGeometry x="-920" y="92" width="70" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-76" value="&lt;div style=&quot;padding: 0px; margin: 0px;&quot;&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;Core tổng đài&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-844" y="100.25" width="101" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-77" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#CCCCCC;" parent="1" vertex="1">
          <mxGeometry x="-921" y="162" width="70" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-78" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Các hệ thống khác&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-848" y="170.25" width="135" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-80" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Đường kết nối cuộc gọi&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-848" y="308.5" width="158" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-83" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#7F00FF;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-921" y="375" as="sourcePoint" />
            <mxPoint x="-851" y="374" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-84" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Đường kết nối các hệ thống khác&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-849" y="356.5" width="179" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-85" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#00FF00;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-921" y="422" as="sourcePoint" />
            <mxPoint x="-851" y="421" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-86" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Đường đọc ghi dữ liệu&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-848" y="404.5" width="158" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4VxmYRTt2rac8v3RHbJV-88" value="" style="swimlane;startSize=0;" parent="1" vertex="1">
          <mxGeometry x="-940" y="2" width="290" height="488" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-10" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="28" y="742" width="200" height="372" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-11" value="&lt;b&gt;&amp;nbsp;&lt;/b&gt;&lt;b style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Kamailio&lt;/b&gt;&lt;div&gt;&lt;div&gt;10.1. 30.28&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="96.5" y="779.9999999999999" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-12" value="&lt;b&gt;&amp;nbsp;&lt;/b&gt;&lt;b style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Kamailio&lt;/b&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="98.5" y="958.9999999999999" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-18" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="pm5V1Q2rgflL0MZlzJiH-12" target="pm5V1Q2rgflL0MZlzJiH-11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="173" y="912" as="sourcePoint" />
            <mxPoint x="223" y="862" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-19" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;Active-Standby&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-18" vertex="1" connectable="0">
          <mxGeometry x="-0.3764" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-24" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="480" y="680" width="380" height="200" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-27" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;fillColor=#FF6666;strokeColor=#7F00FF;" parent="1" target="pm5V1Q2rgflL0MZlzJiH-31" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="613" y="751.9300000000001" as="sourcePoint" />
            <mxPoint x="781.0000744490276" y="738.2488892070471" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-28" value="SIP" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-27" vertex="1" connectable="0">
          <mxGeometry x="-0.0308" y="-2" relative="1" as="geometry">
            <mxPoint y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-29" value="TCP/8021" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-27" vertex="1" connectable="0">
          <mxGeometry x="-0.012" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-30" value="&lt;b&gt;FreeSwitch&lt;/b&gt;&lt;div&gt;TCP/UDP: 5060, 5080, 8021&lt;/div&gt;&lt;div&gt;UDP: 10000-40000&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="564" y="717" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-31" value="&lt;b&gt;Websocket Server&lt;/b&gt;&lt;div&gt;TCP: 8080&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="756" y="714.9300000000001" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-38" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;&lt;font style=&quot;&quot;&gt;&amp;nbsp;&lt;/font&gt;10.1.30.120&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="616.25" y="848" width="107.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-40" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="480" y="951.5" width="380" height="200" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-41" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;fillColor=#FF6666;strokeColor=#7F00FF;" parent="1" target="pm5V1Q2rgflL0MZlzJiH-45" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="608" y="1023.4300000000001" as="sourcePoint" />
            <mxPoint x="776.0000744490276" y="1009.7488892070471" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-42" value="SIP" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-41" vertex="1" connectable="0">
          <mxGeometry x="-0.0308" y="-2" relative="1" as="geometry">
            <mxPoint y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-43" value="TCP/8021" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-41" vertex="1" connectable="0">
          <mxGeometry x="-0.012" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-44" value="&lt;b&gt;FreeSwitch&lt;/b&gt;&lt;div&gt;TCP/UDP: 5060, 5080, 8021&lt;/div&gt;&lt;div&gt;UDP: 10000-40000&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="559" y="988.5" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-45" value="&lt;b&gt;Websocket Server&lt;/b&gt;&lt;div&gt;TCP: 8080&lt;/div&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.traditional_server;fillColor=#7EA6E0;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="751" y="986.4300000000001" width="50" height="73" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-46" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;&lt;font style=&quot;&quot;&gt;&amp;nbsp;&lt;/font&gt;10.1.30.29&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="614.75" y="1125.5" width="107.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-48" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="pm5V1Q2rgflL0MZlzJiH-40" target="pm5V1Q2rgflL0MZlzJiH-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="665" y="1280" as="sourcePoint" />
            <mxPoint x="675" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-49" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;Active - Active&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-48" vertex="1" connectable="0">
          <mxGeometry x="0.0746" y="-4" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-66" value="&lt;b&gt;&lt;font style=&quot;font-size: 14px;&quot;&gt;PROXY SERVER&lt;/font&gt;&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="48" y="1084" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-67" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;" parent="1" source="pm5V1Q2rgflL0MZlzJiH-10" target="ruG60msS0jveVvH4YD21-28" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="720" as="sourcePoint" />
            <mxPoint x="860" y="670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-68" value="Routing SIP/RTP: 8081" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-67" vertex="1" connectable="0">
          <mxGeometry x="-0.4175" y="-3" relative="1" as="geometry">
            <mxPoint x="36" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-79" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1" source="4VxmYRTt2rac8v3RHbJV-28">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-187" y="160" as="sourcePoint" />
            <mxPoint x="31" y="928" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-437" y="928" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-80" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;RTP 10000-40000 UDP (Kết nối âm thanh cuộc gọi)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 12px;&quot;&gt;SIP 5060 TCP/UDP (Kết nối tín hiệu )&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-79" vertex="1" connectable="0">
          <mxGeometry x="-0.4175" y="-3" relative="1" as="geometry">
            <mxPoint x="36" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-81" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;entryX=-0.006;entryY=0.524;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="ruG60msS0jveVvH4YD21-28" target="pm5V1Q2rgflL0MZlzJiH-115" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1110" y="1061.5" as="sourcePoint" />
            <mxPoint x="1559" y="928.*************" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-82" value="Routing SIP: 5060" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-81" vertex="1" connectable="0">
          <mxGeometry x="-0.4175" y="-3" relative="1" as="geometry">
            <mxPoint x="83" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-91" value="" style="edgeStyle=segmentEdgeStyle;endArrow=classic;html=1;curved=0;rounded=0;strokeColor=#FF0000;shape=flexArrow;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-914" y="324.5" as="sourcePoint" />
            <mxPoint x="-854" y="324.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-94" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#FF0000,#FFFFFF);shape=flexArrow;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.982;exitY=0.526;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="pm5V1Q2rgflL0MZlzJiH-115" target="4VxmYRTt2rac8v3RHbJV-59" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1740" y="928.5" as="sourcePoint" />
            <mxPoint x="1889" y="1130" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1956" y="928" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-95" value="SIP TRUNK" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="pm5V1Q2rgflL0MZlzJiH-94" vertex="1" connectable="0">
          <mxGeometry x="-0.4175" y="-3" relative="1" as="geometry">
            <mxPoint x="83" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-112" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=light-dark(#7F00FF,#FFFFFF);align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;edgeStyle=orthogonalEdgeStyle;fillColor=#FF0000;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="4VxmYRTt2rac8v3RHbJV-9" target="pm5V1Q2rgflL0MZlzJiH-111" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1190" y="650" as="sourcePoint" />
            <mxPoint x="1240" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pm5V1Q2rgflL0MZlzJiH-127" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=light-dark(#7F00FF,#FFFFFF);align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;edgeStyle=orthogonalEdgeStyle;fillColor=#FF0000;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.998;exitY=0.795;exitDx=0;exitDy=0;exitPerimeter=0;entryPerimeter=0;" parent="1" source="ruG60msS0jveVvH4YD21-28" target="eTBQ91r6Va6702sbMowr-29" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="980" y="1060" as="sourcePoint" />
            <mxPoint x="1093.6599999999999" y="1100.4999999999995" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-9" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFF00;" vertex="1" parent="1">
          <mxGeometry x="-918" y="238" width="70" height="42.5" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-10" value="&lt;span style=&quot;font-size: 14px;&quot;&gt;Các hệ thống nội bộ MB&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="-845" y="246.25" width="135" height="30" as="geometry" />
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-18" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=-0.003;exitY=0.214;exitDx=0;exitDy=0;exitPerimeter=0;strokeColor=light-dark(#7F00FF,#FFFFFF);" edge="1" parent="1" source="ruG60msS0jveVvH4YD21-28" target="eTBQ91r6Va6702sbMowr-8">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="30" y="680" as="sourcePoint" />
            <mxPoint x="80" y="630" as="targetPoint" />
            <Array as="points">
              <mxPoint x="280" y="761" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="eTBQ91r6Va6702sbMowr-19" value="HTTP" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="eTBQ91r6Va6702sbMowr-18">
          <mxGeometry x="0.6198" y="2" relative="1" as="geometry">
            <mxPoint x="2" as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
