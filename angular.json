{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"mb-ng-started": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets", "src/assets/images/icons", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor/", "output": "/assets/monaco/"}], "styles": ["src/styles.scss", {"input": "src/styles.scss", "bundleName": "dcm-style", "inject": false}], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js", "node_modules/webrtc-adapter/out/adapter.js", "src/assets/html/demos/janus.js", "src/assets/pbx_3cx_ggg_v1.js"], "customWebpackConfig": {"path": "webpack.config.js"}, "allowedCommonJsDependencies": ["lodash", "moment", "j<PERSON>y", "js-sha256", "file-saver"], "stylePreprocessorOptions": {"includePaths": ["src", "src/assets/scss", "src/app/theme/style", "src/assets/scss/mb", "./node_modules"]}}, "configurations": {"development": {"optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}}}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"development": {"buildTarget": "mb-ng-started:build:development"}, "production": {"buildTarget": "mb-ng-started:build:production"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "mb-ng-started:build"}}, "test": {"builder": "@angular-builders/custom-webpack:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": [], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "mb-ng-started:serve"}, "configurations": {"production": {"devServerTarget": "mb-ng-started:serve:production"}}}}}, "dynamic-builder-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/dynamic-builder-ui", "sourceRoot": "projects/dynamic-builder-ui/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser-esbuild", "options": {"outputPath": "dist/dynamic-builder-ui", "index": "projects/dynamic-builder-ui/src/index.html", "main": "projects/dynamic-builder-ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/dynamic-builder-ui/tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["./node_modules", "src"]}, "assets": ["projects/dynamic-builder-ui/src/favicon.ico", "projects/dynamic-builder-ui/src/assets"], "styles": ["projects/dynamic-builder-ui/src/styles.scss"], "scripts": []}, "configurations": {"production": {"tsConfig": "projects/dynamic-builder-ui/tsconfig.lib.prod.json", "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "outputHashing": "all"}, "development": {"tsConfig": "projects/dynamic-builder-ui/tsconfig.lib.json", "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "dynamic-builder-ui:build:production"}, "development": {"buildTarget": "dynamic-builder-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "dynamic-builder-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/dynamic-builder-ui/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/dynamic-builder-ui/src/favicon.ico", "projects/dynamic-builder-ui/src/assets"], "styles": ["projects/dynamic-builder-ui/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/dynamic-builder-ui/**/*.ts", "projects/dynamic-builder-ui/**/*.html"]}}}}}}