// @import '~@angular/material/theming';



// Typography
$font-size-base: 14;

// All Map
$utilities: () !default;

// COLOR
$custom-collection: (
  white: white,
  black: #333333,
  gray: #959595,
  silver: #f7f7f7,
  primary: #141ed2,
  secondary: #2b34e0,
  error: #ed1d24,
  info: #a0d2ff,
  success: #7ed600,
  warning: #fdd131,
  danger: #f0383e94,
  green: #45D39D,
  contrast: (
    white: #303030,
    black: #ffffff,
    gray: #ffffff,
    silver: #ffffff,
    primary: #ffffff,
    secondary: #ffffff,
    error: #ffffff,
    info: #ffffff,
    success: #ffffff,
    warning: #ffffff,
    danger: #ffffff,
    green: #BA2C62
  )
);
$color-gray-200: #fafafa;
$color-gray-500: #bdbdbd;
$color-light-error: #ffdce1;
$color-light-success: #d7f2db;
$color-sematic-success-100: #28d97e;
$color-light-primary: #141ed25e;
$color-light-blue-100: #b3d3f5;
$sematic-warning-100: #957a4c;

$color-blue-primary: #141ed2;
$color-blue-dark-blue: #24264e;
$color-secondary-lotus-lavender: #b4a5fa;
$color-secondary-mint-green: #9be6c8;
$color-secondary-sky-blue: #a0d2ff;
$color-neutral-black: #353644;
$color-gray-900: #2e2e2e;
$color-gray-800: #303030;
$color-gray-700: #646464;
$color-gray-600: #a1a1a1;
$color-gray-400: #cccccc;
$color-gray-300: #d8d8d8;
$color-gray-100: #f7f7f7;
$color-gray-50: #fcfcfc;
$color-neutral-white: #ffffff;
$color-sematic-success: #1fb266;
$color-sematic-error: #eb2d4b;
$color-sematic-disabled: #b2b8ed;
$sematic-warning: #f1b627;
$color-gray-custom: #9b9b9b;
$color-light-blue: #ebf3ff;
$body-color: #041557;
$color-green: #45D39D;
$place-holder: #b2b8cc;

:root {
  --color-gray-200: #{$color-gray-200};
  --color-gray-500: #{$color-gray-500};
  --color-light-error: #{$color-light-error};
  --color-light-success: #{$color-light-success};
  --color-sematic-success-100: #{$color-sematic-success-100};
  --color-light-primary: #{$color-light-primary};
  --color-light-blue-100: #{$color-light-blue-100};
  --sematic-warning-100: #{$sematic-warning-100};
  --color-blue-primary: #{$color-blue-primary};
  --color-blue-dark-blue: #{$color-blue-dark-blue};
  --color-secondary-lotus-lavender: #{$color-secondary-lotus-lavender};
  --color-secondary-mint-green: #{$color-secondary-mint-green};
  --color-secondary-sky-blue: #{$color-secondary-sky-blue};
  --color-neutral-black: #{$color-neutral-black};
  --color-gray-900: #{$color-gray-900};
  --color-gray-800: #{$color-gray-800};
  --color-gray-700: #{$color-gray-700};
  --color-gray-600: #{$color-gray-600};
  --color-gray-400: #{$color-gray-400};
  --color-gray-300: #{$color-gray-300};
  --color-gray-100: #{$color-gray-100};
  --color-gray-50: #{$color-gray-50};
  --color-neutral-white: #{$color-neutral-white};
  --color-sematic-success: #{$color-sematic-success};
  --color-sematic-error: #{$color-sematic-error};
  --color-sematic-disabled: #{$color-sematic-disabled};
  --sematic-warning: #{$sematic-warning};
  --color-gray-custom: #{$color-gray-custom};
  --color-light-blue: #{$color-light-blue};
  --body-color: #{$body-color};
  --color-green: #{$color-green};
  --place-holder: #{$place-holder};
}

$colors: (
  white: $color-neutral-white,
  gray-50: $color-gray-50,
  gray-100: $color-gray-100,
  gray-200: $color-gray-200,
  gray-300: $color-gray-300,
  gray-400: $color-gray-400,
  gray-500: $color-gray-500,
  gray-600: $color-gray-600,
  gray-700: $color-gray-700,
  gray-900: $color-gray-900,
  error: $color-sematic-error,
  success: $color-sematic-success,
  light-success: $color-light-success,
  success-100: $color-sematic-success-100,
  warning: $sematic-warning,
  warning-100: $sematic-warning-100,
  disabled: $color-sematic-disabled,
  primary: $color-blue-primary,
  dark-blue: $color-blue-dark-blue,
  light-blue: $color-light-blue,
  light-error: $color-light-error,
  white-error: $color-neutral-white,
  neutral-black: $color-neutral-black,
  body: $body-color,
  green: $color-green,
  place-holder: $place-holder
);

$colors-background: (
  white: $color-gray-200,
  gray-50: $color-gray-900,
  gray-100: $color-neutral-white,
  gray-200: $color-neutral-white,
  gray-300: $color-neutral-white,
  gray-400: $color-neutral-white,
  gray-500: $color-neutral-white,
  gray-600: $color-neutral-white,
  gray-700: $color-neutral-white,
  gray-900: $color-neutral-white,
  error: $color-neutral-white,
  success: $color-neutral-white,
  light-success: $color-blue-primary,
  success-100: $color-neutral-white,
  warning: $color-neutral-white,
  warning-100: $color-neutral-white,
  disabled: $color-neutral-white,
  primary: $color-neutral-white,
  dark-blue: $color-neutral-white,
  light-blue: $color-blue-primary,
  light-error: $color-sematic-error,
  white-error: $color-sematic-error,
  green: $color-neutral-white
);

$colors-border: (
  primary: #B2B8CC,
  white: $color-blue-primary,
  light-blue: $color-blue-primary,
  light-error: $color-sematic-error
);

// DEVICE WIDTH DEFINES
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 750px,
  lg: 1025px,
  xl: 1366px
);
$breakpoints: (sm, md, lg, xl);
$grid-media-queries: (sm, gt-sm, lt-md, md, gt-md, lt-lg, lg);

// Flex
$flex-values: (row, column, row-reverse, column-reverse);
// $flex-justify-content: (flex-start, flex-end, center, space-between, space-around, initial, inherit);
// Spacing
$spacings: (
  mr: (
    margin
  ),
  mrl: (
    margin-left
  ),
  mrr: (
    margin-right
  ),
  mrt: (
    margin-top
  ),
  mrb: (
    margin-bottom
  ),
  mrs: (
    margin-left,
    margin-right
  ),
  mrx: (
    margin-left,
    margin-right
  ),
  mru: (
    margin-top,
    margin-bottom
  ),
  mry: (
    margin-top,
    margin-bottom
  ),
  pd: (
    padding
  ),
  pdt: (
    padding-top
  ),
  pdb: (
    padding-bottom
  ),
  pdl: (
    padding-left
  ),
  pdr: (
    padding-right
  ),
  pds: (
    padding-left,
    padding-right
  ),
  pdu: (
    padding-top,
    padding-bottom
  )
);
// Text Aligns
$text-align: (center, left, right, justify);
// Row margin-left,right
$grid-gutter-width: 2rem;
// Font Weight
$font-weights: (
  normal: 400,
  medium: 600,
  bold: bold
);
// Font Sizes
$font-sizes: (
  12: 10px,
  14: 0.875rem,
  16: 1rem,
  18: 1.125rem,
  20: 1.25rem,
  25: 1.5rem,
  50: 3.5rem
);
// Text upper case
$text-cases: (uppercase, lowercase, capitalize);

$flex-justify-content: (
  start: (
    flex-start
  ),
  end: (
    flex-end
  ),
  center: (
    center
  ),
  between: (
    space-between
  ),
  around: (
    space-around
  ),
  initial: (
    initial
  ),
  inherit: (
    inherit
  )
);
