export const SVGIcons = {
  emailActive: `<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="4" fill="#F6F8FF"/>
  <path d="M24.167 27.708h-8.333a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h8.333c2.383 0 3.542-1.158 3.542-3.541v-5.834c0-2.383-1.159-3.541-3.542-3.541h-8.333c-2.384 0-3.542 1.158-3.542 3.541a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625c0-3.041 1.75-4.791 4.792-4.791h8.333c3.042 0 4.792 1.75 4.792 4.791v5.834c0 3.041-1.75 4.791-4.792 4.791" fill="#809AF9"/>
  <path d="M20 20.725c-.7 0-1.408-.217-1.95-.658l-2.608-2.084a.623.623 0 0 1 .775-.975l2.608 2.084c.633.508 1.708.508 2.342 0l2.608-2.084a.615.615 0 0 1 .875.1.615.615 0 0 1-.1.875l-2.608 2.084c-.534.441-1.242.658-1.942.658m-3.333 3.65h-5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625m-2.5-3.333h-2.5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625" fill="#809AF9"/>
</svg>`,
  emailDisable: `<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="4" fill="#F7F8FA"/>
  <path d="M24.167 27.708h-8.333a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h8.333c2.383 0 3.542-1.158 3.542-3.541v-5.834c0-2.383-1.159-3.541-3.542-3.541h-8.333c-2.384 0-3.542 1.158-3.542 3.541a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625c0-3.041 1.75-4.791 4.792-4.791h8.333c3.042 0 4.792 1.75 4.792 4.791v5.834c0 3.041-1.75 4.791-4.792 4.791" fill="#B2B8CC"/>
  <path d="M20 20.725c-.7 0-1.408-.217-1.95-.658l-2.608-2.084a.623.623 0 0 1 .775-.975l2.608 2.084c.633.508 1.708.508 2.342 0l2.608-2.084a.615.615 0 0 1 .875.1.615.615 0 0 1-.1.875l-2.608 2.084c-.534.441-1.242.658-1.942.658m-3.333 3.65h-5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625m-2.5-3.333h-2.5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625" fill="#B2B8CC"/>
</svg>`,
  phoneActive: `<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="4" fill="#F6F8FF"/>
  <path d="M24.542 28.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.008-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.033-.65-2.033-.65-2.975 0-.65.117-1.267.342-1.842a4.45 4.45 0 0 1 1.116-1.616c.642-.634 1.375-.95 2.159-.95.325 0 .658.075.941.208.325.15.6.375.8.675l1.934 2.725c.175.242.308.475.4.708.108.25.166.5.166.742q-.002.477-.266.917a3.3 3.3 0 0 1-.559.708l-.566.592a.4.4 0 0 0 .025.058c.1.175.3.475.683.925.408.467.792.892 1.175 1.283.492.484.9.867 1.283 1.184.475.4.784.6.967.691l-.017.042.609-.6a3.1 3.1 0 0 1 .75-.575c.458-.283 1.041-.333 1.625-.092.216.092.45.217.7.392l2.766 1.967c.309.208.534.475.667.791.125.317.183.609.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142a4.9 4.9 0 0 1-1.858.358m-9.883-16.666c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.175a3.7 3.7 0 0 0-.258 1.383c0 .775.183 1.617.55 2.483.375.884.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.267 13.6 13.6 0 0 0 2.742 1.575c1.425.608 2.758.75 3.858.291.425-.175.8-.441 1.142-.825a2.7 2.7 0 0 0 .467-.7c.1-.208.15-.425.15-.641a1 1 0 0 0-.092-.417.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.183-.742-.467-1.183-.842A26 26 0 0 1 18.5 21.45a29 29 0 0 1-1.241-1.35q-.588-.687-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65c.166-.166.291-.325.375-.466a.54.54 0 0 0 .091-.284.8.8 0 0 0-.066-.266 2.4 2.4 0 0 0-.267-.442l-1.933-2.733a.77.77 0 0 0-.309-.259 1 1 0 0 0-.416-.091m6.966 10.216-.133.567.225-.583c-.042-.009-.075 0-.092.016" fill="#809AF9"/>
</svg>`,
  phoneDisable: `<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="40" height="40" rx="4" fill="#F7F8FA"/>
  <path d="M24.542 28.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.008-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.033-.65-2.033-.65-2.975 0-.65.117-1.267.342-1.842a4.45 4.45 0 0 1 1.116-1.616c.642-.634 1.375-.95 2.159-.95.325 0 .658.075.941.208.325.15.6.375.8.675l1.934 2.725c.175.242.308.475.4.708.108.25.166.5.166.742q-.002.477-.266.917a3.3 3.3 0 0 1-.559.708l-.566.592a.4.4 0 0 0 .025.058c.1.175.3.475.683.925.408.467.792.892 1.175 1.283.492.484.9.867 1.283 1.184.475.4.784.6.967.691l-.017.042.609-.6a3.1 3.1 0 0 1 .75-.575c.458-.283 1.041-.333 1.625-.092.216.092.45.217.7.392l2.766 1.967c.309.208.534.475.667.791.125.317.183.609.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142a4.9 4.9 0 0 1-1.858.358m-9.883-16.666c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.175a3.7 3.7 0 0 0-.258 1.383c0 .775.183 1.617.55 2.483.375.884.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.267 13.6 13.6 0 0 0 2.742 1.575c1.425.608 2.758.75 3.858.291.425-.175.8-.441 1.142-.825a2.7 2.7 0 0 0 .467-.7c.1-.208.15-.425.15-.641a1 1 0 0 0-.092-.417.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.183-.742-.467-1.183-.842A26 26 0 0 1 18.5 21.45a29 29 0 0 1-1.241-1.35q-.588-.687-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65c.166-.166.291-.325.375-.466a.54.54 0 0 0 .091-.284.8.8 0 0 0-.066-.266 2.4 2.4 0 0 0-.267-.442l-1.933-2.733a.77.77 0 0 0-.309-.259 1 1 0 0 0-.416-.091m6.966 10.216-.133.567.225-.583c-.042-.009-.075 0-.092.016" fill="#B2B8CC"/>
</svg>`,
  callOut: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M14.542 18.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.009-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.034-.65-2.034-.65-2.975 0-.65.117-1.267.342-1.842A4.45 4.45 0 0 1 2.5 1.992c.642-.634 1.375-.95 2.159-.95.325 0 .658.075.941.208.325.15.6.375.8.675L8.334 4.65q.262.36.4.708c.108.25.166.5.166.742q-.002.477-.266.917a3.3 3.3 0 0 1-.559.708l-.566.591q.012.036.025.059c.1.175.3.475.683.925.408.466.792.891 1.175 1.283a21 21 0 0 0 1.283 1.184c.475.4.784.6.967.691l-.017.042.609-.6q.387-.387.75-.575c.458-.284 1.041-.333 1.625-.092.216.092.45.217.7.392l2.766 1.967q.465.314.667.791c.125.317.183.609.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142-.575.233-1.2.358-1.858.358M4.659 2.292c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.174a3.7 3.7 0 0 0-.258 1.384c0 .775.183 1.616.55 2.483.375.883.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.266 13.5 13.5 0 0 0 2.742 1.576c1.425.608 2.758.75 3.858.291.425-.175.8-.442 1.142-.825.192-.208.342-.433.467-.7.1-.208.15-.425.15-.641a1 1 0 0 0-.092-.417.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.184-.742-.467-1.183-.842A26 26 0 0 1 8.5 11.45a29 29 0 0 1-1.24-1.35q-.588-.687-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65c.166-.167.291-.325.375-.466a.53.53 0 0 0 .091-.284.8.8 0 0 0-.066-.267 2.4 2.4 0 0 0-.267-.441L5.384 2.642a.77.77 0 0 0-.309-.259 1 1 0 0 0-.416-.091m6.966 10.216-.133.567.225-.583c-.042-.009-.075 0-.092.016m5.042-4.549a.63.63 0 0 1-.625-.625V3.959h-3.375a.63.63 0 0 1-.625-.626.63.63 0 0 1 .625-.625h4a.63.63 0 0 1 .625.625v4a.63.63 0 0 1-.625.625" fill="#292D32"/>
</svg>`,
  callIn: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M14.542 18.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.009-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.034-.65-2.034-.65-2.975 0-.65.117-1.267.342-1.842A4.45 4.45 0 0 1 2.5 1.992c.642-.634 1.375-.95 2.159-.95.325 0 .658.075.941.208.325.15.6.375.8.675L8.334 4.65q.262.36.4.708c.108.25.166.5.166.742q-.002.477-.266.917a3.3 3.3 0 0 1-.559.708l-.566.591q.012.036.025.059c.1.175.3.475.683.925.408.466.792.891 1.175 1.283a21 21 0 0 0 1.283 1.184c.475.4.784.6.967.691l-.017.042.609-.6q.387-.387.75-.575c.458-.284 1.041-.333 1.625-.092.216.092.45.217.7.392l2.766 1.967q.465.314.667.791c.125.317.183.609.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142-.575.233-1.2.358-1.858.358M4.659 2.292c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.174a3.7 3.7 0 0 0-.258 1.384c0 .775.183 1.616.55 2.483.375.883.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.266 13.5 13.5 0 0 0 2.742 1.576c1.425.608 2.758.75 3.858.291.425-.175.8-.442 1.142-.825.192-.208.342-.433.467-.7.1-.208.15-.425.15-.641a1 1 0 0 0-.092-.417.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.184-.742-.467-1.183-.842A26 26 0 0 1 8.5 11.45a29 29 0 0 1-1.24-1.35q-.588-.687-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65c.166-.167.291-.325.375-.466a.53.53 0 0 0 .091-.284.8.8 0 0 0-.066-.267 2.4 2.4 0 0 0-.267-.441L5.384 2.642a.77.77 0 0 0-.309-.259 1 1 0 0 0-.416-.091m6.966 10.216-.133.567.225-.583c-.042-.009-.075 0-.092.016M17.5 7.125h-4a.63.63 0 0 1-.625-.625v-4a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.375H17.5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625" fill="#292D32"/>
</svg>`,
  historyChange: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8 5.75c-.41 0-.75-.34-.75-.75V2c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75m8 0c-.41 0-.75-.34-.75-.75V2c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75" fill="currentColor"/>
  <path d="M16 22.75H8c-3.65 0-5.75-2.1-5.75-5.75V8.5c0-3.65 2.1-5.75 5.75-5.75h8c3.65 0 5.75 2.1 5.75 5.75V17c0 3.65-2.1 5.75-5.75 5.75M8 4.25c-2.86 0-4.25 1.39-4.25 4.25V17c0 2.86 1.39 4.25 4.25 4.25h8c2.86 0 4.25-1.39 4.25-4.25V8.5c0-2.86-1.39-4.25-4.25-4.25z" fill="currentColor"/>
  <path d="M16 11.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75m-4 5H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75" fill="currentColor"/>
</svg>`,
  phone: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M14.542 18.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.009-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.034-.65-2.034-.65-2.975 0-.65.117-1.267.342-1.842A4.45 4.45 0 0 1 2.5 1.992c.642-.634 1.375-.95 2.159-.95.325 0 .658.075.941.208.325.15.6.375.8.675L8.334 4.65q.262.36.4.708c.108.25.166.5.166.742q-.002.477-.266.917a3.3 3.3 0 0 1-.559.708l-.566.591q.012.036.025.059c.1.175.3.475.683.925.408.466.792.891 1.175 1.283a21 21 0 0 0 1.283 1.184c.475.4.784.6.967.691l-.017.042.609-.6q.387-.387.75-.575c.458-.284 1.041-.333 1.625-.092.216.092.45.217.7.392l2.766 1.967q.465.314.667.791c.125.317.183.609.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142-.575.233-1.2.358-1.858.358M4.659 2.292c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.174a3.7 3.7 0 0 0-.258 1.384c0 .775.183 1.616.55 2.483.375.883.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.266 13.5 13.5 0 0 0 2.742 1.576c1.425.608 2.758.75 3.858.291.425-.175.8-.442 1.142-.825.192-.208.342-.433.467-.7.1-.208.15-.425.15-.641a1 1 0 0 0-.092-.417.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.184-.742-.467-1.183-.842A26 26 0 0 1 8.5 11.45a29 29 0 0 1-1.24-1.35q-.588-.687-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65c.166-.167.291-.325.375-.466a.53.53 0 0 0 .091-.284.8.8 0 0 0-.066-.267 2.4 2.4 0 0 0-.267-.441L5.384 2.642a.77.77 0 0 0-.309-.259" fill="currentColor"/>
</svg>`,
  subtractCircle: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="22" height="22">
    <path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75m0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75" fill="currentColor"/>
    <path d="M16 12.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75" fill="currentColor"/>
  </mask>
  <g mask="url(#a)">
    <path fill="currentColor" d="M-.471 0h24v24h-24z"/>
  </g>
</svg>`,
  addCircle: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="22" height="22">
    <path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75m0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75" fill="currentColor"/>
    <path d="M16 12.75H8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75" fill="currentColor"/>
    <path d="M12 16.75c-.41 0-.75-.34-.75-.75V8c0-.41.34-.75.75-.75s.75.34.75.75v8c0 .41-.34.75-.75.75" fill="currentColor"/>
  </mask>
  <g mask="url(#a)">
    <path fill="#currentColor" d="M-.471 0h24v24h-24z"/>
  </g>
</svg>`,
  callOutCircle: `<svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x=".5" y=".5" width="35" height="35" rx="17.5" stroke="currentColor"/>
  <path d="M22.542 26.958c-.942 0-1.933-.225-2.958-.658-1-.425-2.009-1.009-2.992-1.717a26 26 0 0 1-2.808-2.391 25 25 0 0 1-2.392-2.8c-.717-1-1.292-2-1.7-2.967-.433-1.033-.65-2.033-.65-2.975 0-.65.117-1.267.342-1.842A4.45 4.45 0 0 1 10.5 9.991c.642-.633 1.375-.95 2.159-.95.325 0 .658.075.941.209.325.15.6.375.8.675l1.934 2.725c.175.242.308.475.4.708q.164.377.166.742c0 .316-.091.625-.266.917a3.3 3.3 0 0 1-.559.708l-.566.592a.4.4 0 0 0 .025.058c.1.175.3.475.683.925.408.466.792.892 1.175 1.283.492.484.9.867 1.283 1.184.475.4.784.6.967.691l-.017.042.609-.6q.387-.387.75-.575c.458-.284 1.041-.334 1.625-.092.216.092.45.217.7.392l2.766 1.967c.309.208.534.475.667.791.125.317.183.608.183.9 0 .4-.091.8-.266 1.175s-.392.7-.667 1c-.475.525-.992.9-1.592 1.142-.575.233-1.2.358-1.858.358m-9.883-16.666c-.459 0-.884.2-1.292.6-.383.358-.65.75-.817 1.175a3.7 3.7 0 0 0-.258 1.383c0 .775.183 1.617.55 2.483.375.884.9 1.8 1.567 2.717a23 23 0 0 0 2.258 2.65 23.5 23.5 0 0 0 2.658 2.267 13.5 13.5 0 0 0 2.742 1.575c1.425.608 2.758.75 3.858.291.425-.175.8-.442 1.142-.825.192-.208.342-.433.467-.7.1-.208.15-.425.15-.642a1 1 0 0 0-.092-.416.63.63 0 0 0-.233-.25l-2.767-1.967a2.4 2.4 0 0 0-.458-.258c-.184-.075-.259-.15-.542.025a1.9 1.9 0 0 0-.483.375l-.634.625a1.19 1.19 0 0 1-1.208.25l-.225-.1c-.342-.184-.742-.467-1.183-.842-.4-.341-.834-.741-1.359-1.258a29 29 0 0 1-1.241-1.35q-.588-.688-.85-1.175l-.1-.25a1.6 1.6 0 0 1-.067-.417c0-.3.108-.566.317-.775l.625-.65q.249-.252.375-.466a.54.54 0 0 0 .091-.284.8.8 0 0 0-.066-.267 2.4 2.4 0 0 0-.267-.441l-1.933-2.733a.77.77 0 0 0-.309-.259 1 1 0 0 0-.416-.091m6.966 10.216-.133.567.225-.584q-.065-.01-.092.017m5.042-4.549a.63.63 0 0 1-.625-.625v-3.375h-3.375a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h4a.63.63 0 0 1 .625.625v4a.63.63 0 0 1-.625.625" fill="currentColor"/>
</svg>
`,
  sortDefault: `<svg width="10" height="26" viewBox="0 0 10 26" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="mask0_34128_638107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="10" height="6">
  <path d="M9.45322 4.31999L7.31322 2.17999L6.00655 0.86666C5.45322 0.313327 4.55322 0.313327 3.99988 0.86666L0.546552 4.31999C0.0932182 4.77333 0.419885 5.54666 1.05322 5.54666H4.79322H8.94655C9.58655 5.54666 9.90655 4.77333 9.45322 4.31999Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask0_34128_638107)">
  <rect x="-3" y="-5" width="16" height="16" fill="#9AA1BC"/>
  </g>
  <mask id="mask1_34128_638107" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="10" width="10" height="6">
  <path d="M9.45322 11.68L7.31322 13.82L6.00655 15.1333C5.45322 15.6867 4.55322 15.6867 3.99988 15.1333L0.546552 11.68C0.0932182 11.2267 0.419885 10.4533 1.05322 10.4533H4.79322H8.94655C9.58655 10.4533 9.90655 11.2267 9.45322 11.68Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask1_34128_638107)">
  <rect width="16" height="16" transform="matrix(1 0 0 -1 -3 21)" fill="#9AA1BC"/>
  </g>
  </svg>
`,
  sortDown: `<svg width="10" height="26" viewBox="0 0 10 26" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="mask0_34128_638094" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="10" height="6">
  <path d="M9.45322 4.31999L7.31322 2.17999L6.00655 0.86666C5.45322 0.313327 4.55322 0.313327 3.99988 0.86666L0.546552 4.31999C0.0932182 4.77333 0.419885 5.54666 1.05322 5.54666H4.79322H8.94655C9.58655 5.54666 9.90655 4.77333 9.45322 4.31999Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask0_34128_638094)">
  <rect x="-3" y="-5" width="16" height="16" fill="#9AA1BC"/>
  </g>
  <mask id="mask1_34128_638094" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="10" width="10" height="6">
  <path d="M9.45322 11.68L7.31322 13.82L6.00655 15.1333C5.45322 15.6867 4.55322 15.6867 3.99988 15.1333L0.546552 11.68C0.0932182 11.2267 0.419885 10.4533 1.05322 10.4533H4.79322H8.94655C9.58655 10.4533 9.90655 11.2267 9.45322 11.68Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask1_34128_638094)">
  <rect width="16" height="16" transform="matrix(1 0 0 -1 -3 21)" fill="#041557"/>
  </g>
  </svg>
`,
  sortUp: `<svg width="10" height="26" viewBox="0 0 10 26" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="mask0_34128_638081" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="10" height="6">
  <path d="M9.45322 4.31999L7.31322 2.17999L6.00655 0.86666C5.45322 0.313327 4.55322 0.313327 3.99988 0.86666L0.546552 4.31999C0.0932182 4.77333 0.419885 5.54666 1.05322 5.54666H4.79322H8.94655C9.58655 5.54666 9.90655 4.77333 9.45322 4.31999Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask0_34128_638081)">
  <rect x="-3" y="-5" width="16" height="16" fill="#041557"/>
  </g>
  <mask id="mask1_34128_638081" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="10" width="10" height="6">
  <path d="M9.45322 11.68L7.31322 13.82L6.00655 15.1333C5.45322 15.6867 4.55322 15.6867 3.99988 15.1333L0.546552 11.68C0.0932182 11.2267 0.419885 10.4533 1.05322 10.4533H4.79322H8.94655C9.58655 10.4533 9.90655 11.2267 9.45322 11.68Z" fill="#292D32"/>
  </mask>
  <g mask="url(#mask1_34128_638081)">
  <rect width="16" height="16" transform="matrix(1 0 0 -1 -3 21)" fill="#9AA1BC"/>
  </g>
  </svg>
`,
  sortHeaderTable: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M22 6.5h-6m-10 0H2m8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m12 7.5h-4m-10 0H2M14 21a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  changeProfile: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="18" height="18"><path d="M7.633 9.683h-.067a.5.5 0 0 0-.15 0C5 9.608 3.175 7.708 3.175 5.367A4.33 4.33 0 0 1 7.5 1.042a4.33 4.33 0 0 1 4.325 4.325 4.31 4.31 0 0 1-4.167 4.316zM7.5 2.292a3.084 3.084 0 0 0-3.075 3.075c0 1.666 1.3 3.008 2.958 3.066.05-.008.158-.008.267 0a3.07 3.07 0 0 0 2.925-3.066A3.084 3.084 0 0 0 7.5 2.292" fill="#141ED2"/><path d="M13.783 9.792c-.025 0-.05 0-.075-.009-.342.034-.692-.208-.725-.55-.033-.341.175-.65.517-.691.1-.009.208-.009.3-.009a2.29 2.29 0 0 0 2.166-2.291 2.29 2.29 0 0 0-2.291-2.292.617.617 0 0 1-.625-.617.63.63 0 0 1 .625-.625 3.55 3.55 0 0 1 3.541 3.542c0 1.917-1.5 3.467-3.408 3.542zm-6.142 9c-1.633 0-3.275-.417-4.516-1.25-1.159-.767-1.792-1.817-1.792-2.959 0-1.141.633-2.2 1.792-2.975 2.5-1.658 6.55-1.658 9.033 0 1.15.767 1.792 1.817 1.792 2.959 0 1.141-.634 2.2-1.792 2.975-1.25.833-2.883 1.25-4.517 1.25m-3.825-6.134c-.8.534-1.233 1.217-1.233 1.934 0 .708.442 1.391 1.233 1.916 2.075 1.392 5.575 1.392 7.65 0 .8-.533 1.234-1.216 1.234-1.933 0-.708-.442-1.392-1.234-1.917-2.075-1.383-5.575-1.383-7.65 0m11.467 4.634c-.292 0-.55-.2-.608-.5a.634.634 0 0 1 .483-.742c.525-.108 1.008-.317 1.383-.608.475-.359.734-.809.734-1.284s-.259-.925-.725-1.275q-.552-.423-1.367-.608a.63.63 0 0 1-.475-.75.63.63 0 0 1 .75-.475c.717.158 1.342.442 1.85.833.775.584 1.217 1.409 1.217 2.275 0 .867-.45 1.692-1.225 2.284a4.5 4.5 0 0 1-1.884.833.4.4 0 0 1-.133.017" fill="#292D32"/></mask><g mask="url(#a)"><path fill="currentColor" d="M-2.471-2h24v24h-24z"/></g></svg>`,
  revokeProfile: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12.5 18.958h-5c-4.525 0-6.458-1.933-6.458-6.458v-5c0-4.525 1.933-6.458 6.458-6.458h5c4.525 0 6.458 1.933 6.458 6.458v5c0 4.525-1.933 6.458-6.458 6.458m-5-16.666c-3.842 0-5.208 1.366-5.208 5.208v5c0 3.842 1.366 5.208 5.208 5.208h5c3.842 0 5.208-1.366 5.208-5.208v-5c0-3.842-1.366-5.208-5.208-5.208z" fill="currentColor"/>
  <path d="M11.6 13.442H7.5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h4.1a1.944 1.944 0 0 0 1.942-1.942A1.94 1.94 0 0 0 11.6 8.308H5.958a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625H11.6a3.197 3.197 0 0 1 3.192 3.192 3.197 3.197 0 0 1-3.192 3.192" fill="currentColor"/>
  <path d="M7.142 9.6a.62.62 0 0 1-.442-.183L5.392 8.108a.63.63 0 0 1 0-.883L6.7 5.917a.63.63 0 0 1 .883 0 .63.63 0 0 1 0 .883l-.866.867.866.866A.624.624 0 0 1 7.141 9.6" fill="currentColor"/>
</svg>`,
  link: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 493 493" xml:space="preserve" fill="currentColor">
  <path d="M215.6 314.05c6.8 6.6 14.7 12.4 23.5 16.4 2.1 1.2 4.4 1.9 6.7 2.8 2.2.9 4.5 1.7 6.9 2.2 2.4.6 4.5 1.3 7.1 1.7s5.2.8 7.8 1.1c2 .2 3.3.2 4.9.3l2.4.1h3.2l9.5-.1 19-.3 38.1-.9 38.1-1.1 9.5-.3 4.8-.1c1.6 0 3.7-.2 5.5-.4 7.5-.4 14.8-2.2 21.9-4.4 26.3-5.2 50.8-24.7 61.6-51.4l2-5 1.5-5.2c1.1-3.3 1.8-7.3 2.4-11.2l.4-2.9.2-2.2.3-4.3.1-1.1v-2.3l-.1-2.7-.2-5.4c-1.2-14.5-6-28.7-13.6-41.1s-18.2-23-30.6-30.6l-4.7-2.8c-1.6-.8-3.3-1.6-4.9-2.4-3.2-1.7-6.7-2.9-10.1-4.1-3.4-1.3-7-2-10.5-2.9-3.7-.8-8-1.3-11.7-1.7l-4.3-.2-2.2-.1-2.1-.1h-27.1c-11.5-.1-23.1-.2-34.6-.5-4.1-.1-10.5 3.4-12.6 5.8-4 4.9 1.2 9 9 12.1 12.9 5.2 27.1 8.9 42.1 11.4 7.5 1.3 15.2 2.3 23 3h.2-.6.1l.4.1.7.1 1.5.2 2.9.4c2.2.2 3.4.7 4.8 1 2.7.5 6 1.7 9 2.7 14.7 5.4 26.8 16.6 33.3 30.2 3.3 6.8 5.2 14.1 5.8 21.4.6 7.1-.4 15.8-2.4 21.8-4.1 13.9-13.6 26-25.8 33.5-6.1 3.8-12.8 6.5-19.9 7.9-1.8.2-3.5.8-5.4.8l-2.7.3c-.9.1-1.7.2-3 .2l-14.4.5c-9.5-.4-19-.7-28.5-1.1-15.1-.4-30.2-.8-45.2-1l-22.6-.2h-8.9l-1.4-.1-5.5-.3-3.3-.6c-5-1-10.6-2.5-15.5-5s-9.6-5.6-13.8-9.3c-4.1-3.8-7.8-8.2-10.9-12.9-3-4.9-5.3-10.1-7-15.7-.4-1.6-.8-3.2-1.3-4.7s-.7-3.1-1.2-4.5c-1-2.9-1.7-5.7-2.8-8.1-2-4.8-4.6-8.4-9.2-8.7-4.1-.3-8.5 2.3-11.6 8.1-1.6 2.9-2.8 6.5-3.3 10.7-.8 4.2-.6 9 .1 14.1 2.7 18.1 11.4 35.8 25.2 49.1"/>
  <path d="M65.7 198.45c6.1-3.8 12.8-6.5 19.9-7.9 1.8-.2 3.5-.8 5.4-.8l2.7-.2c.9-.1 1.7-.2 3-.2l14.4-.5c9.5.4 19 .7 28.5 1.1 15.1.4 30.2.8 45.2 1l22.6.2h8.9l1.4.1 5.5.3 3.3.6c5 1 10.6 2.5 15.5 5s9.6 5.6 13.8 9.3c4.1 3.8 7.8 8.2 10.9 12.9 3 4.9 5.3 10.1 7 15.7.4 1.6.8 3.2 1.3 4.7s.7 3.1 1.2 4.5c1 2.9 1.7 5.7 2.8 8.1 2 4.8 4.6 8.4 9.2 8.7 4.1.3 8.5-2.3 11.6-8.1 1.6-2.9 2.8-6.5 3.3-10.7.8-4.2.6-9-.1-14.1-3-18.2-11.8-36-25.6-49.2-6.8-6.6-14.7-12.4-23.5-16.4-2.1-1.2-4.4-1.9-6.7-2.8-2.2-.9-4.5-1.7-6.9-2.2-2.4-.6-4.5-1.3-7.1-1.7s-5.2-.8-7.8-1.1c-2-.2-3.3-.2-5-.3l-2.4-.1h-3.2l-9.5.1-19 .3-38.1.9-38.1 1.1-9.5.3-4.8.1c-1.6 0-3.7.2-5.4.4-7.5.4-14.8 2.2-21.9 4.4-26.3 5.2-50.8 24.7-61.6 51.4l-2 5-1.5 5.2c-1.1 3.3-1.8 7.3-2.4 11.2l-.4 2.9-.2 2.2-.3 4.3-.1 1.1v2.3l.1 2.7.2 5.4c1.2 14.5 6 28.7 13.6 41.1s18.2 23 30.6 30.6l4.7 2.8c1.6.8 3.3 1.6 4.9 2.4 3.2 1.7 6.7 2.9 10.1 4.1 3.4 1.3 7 2 10.5 2.9 3.7.8 8 1.3 11.7 1.7l4.3.2 2.2.1 2.1.1h27.1c11.5.1 23.1.2 34.6.5 4.1.1 10.5-3.4 12.6-5.8 4-4.9-1.2-9-9-12.1-12.9-5.2-27.1-8.9-42.1-11.4-7.5-1.3-15.2-2.3-23-3H95h.6-.1l-.4-.1-.7-.1-1.5-.2-2.9-.4c-2.2-.2-3.4-.7-4.8-1-2.7-.5-6-1.7-9-2.7-14.7-5.4-26.8-16.6-33.3-30.2-3.3-6.8-5.2-14.1-5.8-21.4-.6-7.1.4-15.8 2.4-21.8 4.6-13.9 14-25.9 26.2-33.5"/>
</svg>
`
}
