*,
:after,
:before {
  box-sizing: border-box;
}

body {
  font-family: $font-family;
  color: $bodyColor;
  font-size: 14px;
  line-height: normal;
  overflow-x: hidden;
}

html .mat-drawer-container {
  background-color: $white;
}

.mainWrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
  flex: 1;
  height: 100%;
}

.body-wrapper {
  border-radius: 20px;
  background: $body-bg;
  margin: 0 15px 30px 0;
  min-height: calc(100% - 102px);
}

@media (max-width: 1023px) {
  .body-wrapper {
    margin-right: 0;
  }
  .sidebarNav-horizontal {
    .body-wrapper {
      margin-top: 30px;
    }
  }
}

.sidebarNav-horizontal {
  .body-wrapper {
    margin-left: 15px;
    margin-right: 15px;
  }
}

.pageWrapper {
  padding: 30px;
  min-height: calc(100vh - 100px);

  margin: 0 auto;
  &.maxWidth {
    max-width: $boxedWidth;
  }
}

.container {
  max-width: 1200px;
  padding-left: 24px;
  padding-right: 24px;
  margin: 0 auto;
  &.full-width {
    display: flex;
    align-items: center;
    width: 100%;
  }
}

.hstack {
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.filter-sidebar {
  width: 290px !important;
}
