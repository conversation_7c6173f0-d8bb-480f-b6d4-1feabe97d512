// Define base values for border-radius
//.radius-sm: <PERSON> gó<PERSON> nhỏ (2px).
//.radius-lg: <PERSON> góc lớn (8px).
//.radius-full: <PERSON> góc tròn hoàn toàn.
//.radius-top-md: <PERSON> gó<PERSON> chỉ ở phía trên với kích thước trung bình (4px).
$radius-values: (
  none: 0,
  sm: 0.125rem,  // 2px
  md: 0.25rem,   // 4px
  lg: 0.5rem,    // 8px
  xl: 1rem,      // 16px
  full: 9999px
);

// Generate utility classes for border-radius
@each $name, $value in $radius-values {
  .radius-#{$name} {
    border-radius: $value;
  }
}

// Additional classes for specific sides
@each $name, $value in $radius-values {
  .radius-top-#{$name} {
    border-top-left-radius: $value;
    border-top-right-radius: $value;
  }

  .radius-right-#{$name} {
    border-top-right-radius: $value;
    border-bottom-right-radius: $value;
  }

  .radius-bottom-#{$name} {
    border-bottom-right-radius: $value;
    border-bottom-left-radius: $value;
  }

  .radius-left-#{$name} {
    border-top-left-radius: $value;
    border-bottom-left-radius: $value;
  }
}
