/* You can add global styles to this file, and also import other style files */
@import 'styles-variables';
@import '_reboot.scss';
@import 'functions.scss';
@import 'mixins.scss';
@import 'common.scss';
@import 'utilities.scss';
@import 'icon.scss';
@import 'button.scss';
@import 'form.scss';
@import 'tab.scss';
@import 'table.scss';
@import 'stepper.scss';
@import 'barge.scss';
@import 'typography.scss';
@import 'panel.scss';
@import 'swiper-slide.scss';
@import 'section.scss';
@import 'print.scss';
@import 'radio.scss';
@import 'checkbox.scss';
@import 'spacing.scss';
@import 'dialog.scss';
@import 'perfect-scrollbar.scss';
@import 'scrollbar.scss';
@import 'key-frames.scss';
@import 'ngx-toastr.scss';
@import 'material.scss';
@import 'slide-toggle.scss';
@import 'search.scss';
@import 'radius.scss';
@import "../grid/grid";
@import "../helpers/index";
