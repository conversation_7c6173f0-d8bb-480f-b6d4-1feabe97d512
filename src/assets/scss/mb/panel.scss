.panel {
  display: flex;
  flex-direction: column!important;
  min-height: 3rem;
  background-color: white;
  // overflow: hidden;
  position: relative;
  @include border-radius(0.5rem);
  // @include shadow(0 0 1rem 0 opacity-color($color-gray-900, 0.2));
  &.border {
    border: 1px solid #d1d4e0;
  }
  &.panel-over {
    overflow: auto;
  }
  .btn {
    span {
      font-size: 14px;
      font-family: var(--body-fonts) !important;
    }
  }
  .panel-heading {
    padding: 15px;
    border-bottom: 1px solid #e2e2e2;
    min-height: 80px;
    .panel-heading-label {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
      text-align: left;
    }
  }

  .panel-body {
    z-index: 1;
    // overflow: auto;
    &::-webkit-scrollbar {
      width: 5px;
      background: white;
    }
    > div {
      width: 100%;
    }
    /* Track */
    &::-webkit-scrollbar-track {
      border-radius: 10px;
    }
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: transparent;
      box-shadow: inset 0 0 5px grey;
      border-radius: 10px;
    }
    &.panel-padding {
      padding: 16px 17px;
      // max-height: 70vh;
    }
    > .form-group {
      width: 100%;
    }
    &.panel-fix-heigth {
      min-height: 55vh;
    }
    .view-guardian {
      padding-top: 3.5rem;
      padding-bottom: 3.8125rem;
      padding-left: 10.8125rem;
      .txt-top {
        font-size: 1rem;
        font-weight: bold;
      }
      .txt-bot {
        font-size: 1.5rem;
        font-weight: bold;
        text-transform: uppercase;
        margin-top: 0;
      }
    }
  }
  .panel-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
    width: 100%;
    > button {
      margin: 0.75rem 0;
      color: white;
    }
  }
  &.panel-bottom {
    margin-bottom: 6rem;
  }
  &.panel-fix-heigth {
    min-height: 30vh;
  }
}
