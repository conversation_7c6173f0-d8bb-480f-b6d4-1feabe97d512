.dialog-title {
  margin-top: 24px;
  font-family: var(--body-fonts);
  font-weight: bold;
  font-size: 20px;
  text-align: center;
  color: var(--body-color);
}

.mat-mdc-dialog-actions {
  padding: 12px !important;
}

.header-dialog-dform {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  align-items: center;
  padding: 10.5px 24px;
  box-shadow: inset 0 -1px 0 0 #dedede;
  background-color: $color-neutral-white;
  .header-dialog-title {
    font-family: var(--body-fonts);
    font-weight: bold;
    font-size: 18px;
  }
  .modal-content {
    padding: 15px;
    max-height: 100%;
    overflow: auto;
    padding-bottom: 100px!important;
  }
}

.modal-dialog-scrollable {
  height: 100%;
  .modal-heading {
    padding: 5px 15px;
    border-bottom: 1px solid #e2e2e2;
    .label {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
      text-align: left;
    }
  }
  .modal-content {
    padding: 15px;
    max-height: 100%;
    overflow: auto;
    padding-bottom: 100px!important;
  }
}

.mat-mdc-dialog-content {
  padding: 15px!important;
}

.mat-mdc-dialog-container .mdc-dialog__surface {
  border-radius: 20px!important;
}
