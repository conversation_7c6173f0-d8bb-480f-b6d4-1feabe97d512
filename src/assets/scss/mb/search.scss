.div-search {
  padding: 0.75rem;
  line-height: 1.25rem;
  border: 1px solid $color-gray-500;
  max-height: 2.75rem;
  width: 100%;
  border-radius: 0.5rem;
  position: relative;
  mat-icon {
    margin-top: -0.1rem;
    font-size: 17px;
    color: #bebebe;
  }
  input {
    font-family: var(--body-fonts);
    font-size: 14px;
  }
}

.error-search {
  .div-search {
    border-color: $color-sematic-error;
  }
  .form-remark {
    margin-top: 0.5rem;
    color: $color-sematic-error;
  }
}

.search-button {
  width: 100%;
  height: 2.8rem;
  padding: 11px 49px;
  font-family: var(--body-fonts);
  font-size: 14px;
  .mat-icon {
    font-size: 18px;
    margin-right: 4px;
  }
}
