// GL
button[disabled] {
  opacity: 0.3;
  pointer-events: none;
}

button {
  font-family: var(--body-fonts);
}

.btn {
  display: flex !important;
  padding: 0.5rem;
  align-items: center;
  justify-content: center;
  line-height: initial !important;
  min-height: 1.5rem;
  border-radius: 8px;
  font-size: 14px;
  font-family: var(--body-fonts);
  @include ie-only {
    line-height: normal !important;
  }

  &.disabled {
    opacity: 0.3;
    pointer-events: none;
  }

  white-space: normal !important;
  //margin: 0.5rem 0rem;
  position: relative;
  cursor: pointer;
  // @include border-radius(0.25rem);

  @each $color,
  $value in $colors {
    &.btn-#{ "" + $color} {
      background-color: $value;
      border: solid 1px $value;
    }

    &.btn-#{ "" + $color}:hover {
      color: $value;

      ::before {
        color: $value;
      }
    }
  }

  @each $color,
  $value in $colors-background {
    &.btn-#{ "" + $color} {
      color: $value;

      ::before {
        color: $value;
      }
    }

    &.btn-#{ "" + $color}:hover {
      background-color: $value;
    }
  }

  @each $color,
  $value in $colors-border {
    &.btn-border-#{ "" + $color} {
      border-color: $value !important;
    }
  }


  @each $value in $flex-values {
    @each $breakpoint in $breakpoints {
      @include create-responsives-style($breakpoint) {
        &.btn-flex-#{ "" + $breakpoint}-#{ "" + $value} {
          flex-direction: $value !important;

          &.mat-button {
            > span.mat-button-wrapper {
              flex-direction: $value !important;
            }
          }
        }
      }
    }

    &.btn-flex-#{ "" + $value} {
      flex-direction: $value !important;

      &.mat-button {
        > span.mat-button-wrapper {
          flex-direction: $value !important;
        }
      }
    }
  }

  &.mat-button {
    > span.mat-button-wrapper {
      display: flex !important;
      flex-direction: row !important;
      align-items: center;
      justify-content: center;
    }
  }

  @each $breakpoint in $breakpoints {
    @include create-responsives-style($breakpoint) {
      &.btn-#{ "" + $breakpoint}-border {
        border: solid 2px !important;
      }

      &.#{ "" + $breakpoint}-rounded {
        @include border-radius(500px !important);
      }
    }
  }

  &.btn-border {
    border: solid 1px !important;
  }

  @each $name,
  $color in $colors {
    &.#{ "" + $name} {
      border-color: #{ "" + $color};
    }
  }

  @each $name,
  $color in $colors {
    &.btn-border-#{ "" + $name} {
      border-color: #{ "" + $color};
    }
  }

  &.rounded {
    @include border-radius(500px !important);
  }

  /* Button Size */
  &.btn-block {
    width: 100%;
    height: 100%;
  }

  &.btn-xs {
    padding-left: 15px!important;
    padding-right: 15px!important;
    height: 40px;
  }

  &.btn-md {
    padding-left: 15px!important;
    padding-right: 15px!important;
    height: 40px;
    min-width: 128px;
  }

  &.btn-lg {
    padding-left: 15px!important;
    padding-right: 15px!important;
    font-size: 14px;
    height: 40px;
  }

  @each $breakpoint in $breakpoints {
    @include create-responsives-style($breakpoint) {
      &.btn-#{ "" + $breakpoint}-block {
        width: 100%;
        height: 100%;
      }

      &.btn-#{ "" + $breakpoint}-md {
        min-width: 10rem;
      }

      &.btn-#{ "" + $breakpoint}-lg {
        padding: 0.75rem !important;
        font-size: 1.25rem;
      }
    }
  }

  /* Button Icons */
  &.btn-icon-right {
    .mat-icon {
      order: 2;
    }
  }

  &.btn-icon-left {
    .mat-icon {
      order: 1;
    }
  }

  .loading {
    &.active {
      display: block;
    }

    animation-name: spin;
    animation-duration: 2000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    display: none;
    background: url('/assets/images/loading.png');
    @include bg-thumb();
    content: '';
    position: absolute;
    right: 1rem;
    width: 29px;
    height: 29px;
  }

  .spinning {
    animation-name: spin;
    animation-duration: 2000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    display: none;
    position: absolute;
    right: 1rem;
    top: 0;
    bottom: 0;
    margin: auto;
  }

  &.btn-shadow {
    @include shadow(0rem 0rem 1rem 0rem opacity-color($color-gray-900, 0.2));
  }
}

.btn-link {
  color: #2b34e0;
  font-weight: bold;
  &:hover {
    opacity: 0.8;
  }
}

.mat-mdc-icon-button {
  height: 40px!important;
  width: 40px!important;
  padding: 7px!important;
  display: flex!important;
  align-content: center;
  justify-content: center;
  align-items: center;
}

.mat-mdc-icon-button .mat-mdc-button-touch-target {
  height: 40px!important;
  width: 40px!important;
}
