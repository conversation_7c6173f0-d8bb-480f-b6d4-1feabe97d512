.form-group-login {
  &.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
    transform: translateY(-1.28125em) scale(1) perspective(100px)
      translateZ(0.001px) !important;
    -ms-transform: translateY(-1.28125em) scale(0.75) !important;
    color: $color-gray-700;
    font-size: 12px;
    font-weight: normal;
    font-family: var(--body-fonts);
  }

  &.mat-form-field-appearance-legacy .mat-form-field-label,
  input.mat-input-element {
    font-size: 1rem;
    font-weight: 600;
    font-family: Semibold;
  }

  .mat-form-field-wrapper {
    padding-bottom: 0rem !important;
  }

  input.mat-input-element {
    color: #000000;
  }

  .mat-form-field-underline {
    width: 0px !important;
    font-family: var(--body-fonts);
  }
}

.mat-form-field-subscript-wrapper {
  font-size: 90% !important;
}

.mat-tab-label-active {
  color: $color-blue-primary !important;
  opacity: 1 !important;
}

.ng-select {
  width: 100%;
}

.ng-select {
  &.ng-select-single .ng-select-container,
  &.ng-select-multiple .ng-select-container {
    min-height: 40px !important;
    border-radius: 0.5rem;
    border: solid 1px $color-gray-300;
    line-height: 16px;
    .ng-value-container {
      flex-wrap: nowrap!important;
      .ng-placeholder,
      .ng-input {
        top: 12px;
        font-weight: normal;
      }
    }

    .ng-value {
      span {
        font-size: 14px!important;
        font-family: var(--body-fonts) !important;
        font-weight: normal;
      }
    }
    .ng-value-label {
      border: none!important;
    }
  }

  &.ng-select-disabled {
    .ng-value-container {
      .ng-input {
        input {
          color: $color-gray-900;
        }
      }

      .ng-value {
        color: $color-gray-900;
      }
    }
  }

  &.ng-select-focused:not(.ng-select-opend) > .ng-select-container {
    border-color: $color-blue-primary;
    box-shadow: none;
  }

  .ng-dropdown-panel {
    z-index: 9999999;
    .ng-dropdown-panel-items .ng-option {
      span {
        font-size: 13px;
        font-family: var(--body-fonts);
        font-weight: normal;
      }
    }
  }

  .ng-dropdown-panel-items {
    max-height: 176px !important;

    .ng-option {
      font-size: 1rem;
      font-family: var(--body-fonts);
    }
  }
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
  white-space: nowrap!important;
  background-color: #E6E8EE!important;
  min-height: 28px!important;
  padding: 2px!important;
  border-radius: 14px!important;
  display: flex;
  align-items: center;
  span {
    color: #6E779C!important;
    font-weight: normal;
  }
  .ng-value-icon.right {
    border: none!important;
    font-size: 20px !important;
    padding: 0 10px!important;
    border-radius: 50% !important;
    margin-bottom: 3px;
  }
}

.ng-select-error.ng-select .ng-select-container {
  border: 1px solid $color-sematic-error !important;
}

.error-select {
  font-size: 75%;
  padding: 0 1rem 0.9rem 1rem;
  margin-top: 0.6666666667rem;
  top: calc(100% - 1.7916666667rem);
}

// Custom form field with search
.search-form-field .mat-form-field-prefix {
  top: 0.6rem !important;

  .mat-icon {
    font-size: 30px;
  }
}

.search-form-field .mat-form-field-suffix {
  top: 0.4rem !important;
}

// Change expansion material

.mat-expansion-panel-content {
  font-size: #{$font-size-base}px;
}

.mat-drawer-content {
  background-color: $color-gray-200!important;
}

.app-status-inprocess {
  color: $color-blue-primary;
  background-color: $color-light-blue;
}

.app-status-approved {
  color: $color-sematic-success;
  background-color: $color-light-success;
}

.app-status-waiting {
  color: #e59d01;
  background-color: #fff2d3;
}

.app-status-warning {
  color: $color-blue-primary;
  background-color: $sematic-warning;
}

.app-status-reject {
  color: $color-sematic-error;
  background-color: $color-light-error;
}

.app-status-update {
  color: $color-blue-primary;
  background-color: $color-light-blue;
}

.app-status-halfapprove {
  color: $color-blue-primary;
  background-color: $color-sematic-disabled;
}

.app-status-waitingapprove {
  color: $color-sematic-success;
  background-color: $color-light-success;
}

.app-status-delete {
  color: $color-sematic-error;
  background-color: $color-light-error;
}

.app-status-cancel {
  color: $color-gray-900;
  background-color: $color-gray-400;
}

.app-status-dot {
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.dot-status {
  width: 8px;
  height: 8px;
  display: block;
  border-radius: 50%;
  margin-right: 8px;
  &.app-status-inprocess {
    background-color: $color-blue-primary;
  }

  &.app-status-approved {
    background-color: $color-sematic-success;
  }

  &.app-status-waiting {
    background-color: #e59d01;
  }

  &.app-status-warning {
    background-color: $sematic-warning;
  }

  &.app-status-reject {
    background-color: $color-sematic-error;
  }

  &.app-status-unlock {
    background-color: #7B5FFF;
  }
}

.app-status {
  border-radius: 17px;
  white-space: nowrap;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 auto;
  padding: 8px 15px;
  font-weight: bold;
  font-size: 14px;
}

.login .swiper-pagination-bullet {
  width: 29px !important;
  height: 4px !important;
  opacity: 0.4 !important;
  border-radius: 2.5px !important;
  background-color: $color-neutral-white !important;
}

.login .swiper-pagination-bullet-active {
  opacity: 1 !important;
}

.mat-slide-toggle-label {
  span {
    font-size: 1rem;
    font-weight: bold;
    color: #303030;
  }
}

.mat-tooltip {
  background-color: $color-neutral-white !important;
  opacity: 1;
  color: $color-neutral-black !important;
  padding: 0.8rem 1.2rem !important;
  font-size: 14px !important;
  font-family: var(--body-fonts) !important;
  line-height: 24px !important;
  border-radius: 0.5rem;
  overflow: visible !important;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-shadow:
    0px 1px 2px #00000029,
    0px 2px 4px #0000001f,
    0px 1px 8px #0000001a;
}

.mat-tooltip:before {
  border-right-color: white !important;
}

.mat-tooltip:after {
  content: '';
  position: absolute;
  margin-top: -5px;
  border-width: 10px;
  border-style: solid;
}

.mat-tooltip[style*='transform-origin: center top']:after {
  bottom: 100%;
  left: 45%;
  border-color: transparent transparent #fff transparent;
}

.mat-tooltip[style*='transform-origin: center bottom']:after {
  top: 110%;
  left: 45%;
  border-color: #fff transparent transparent transparent;
}

.mat-tooltip[style*='transform-origin: left center']:after {
  top: 40%;
  right: 100%;
  border-color: transparent #fff transparent transparent;
}

.mat-tooltip[style*='transform-origin: right center']:after {
  top: 40%;
  left: 100%;
  border-color: transparent transparent transparent #fff;
}
.mat-mdc-tab-body-content {
  overflow: visible!important;
}
.mat-mdc-tab-body {
  width: 100%;
  &.mat-mdc-tab-body-active {
    overflow: visible!important;
  }
}
