.swiper-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  .swiper-slide {
    height: 0;
    overflow: hidden;
    background: white;
    position: relative;
    width: 100%;
    height: 100%;
    > div {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      img {
        height: 100%;
        width: auto;
      }
    }
  }
  &.swiper-noti {
    .swiper-slide {
    }
    .swiper-button-next,
    .swiper-button-prev {
      background: none;
    }
    .swiper-button-next {
      text-align: right;
    }
    .swiper-pagination {
      padding-right: 10px;
      display: flex;
      justify-content: flex-end;
      .swiper-pagination-bullet {
        background: #ffffff;
        opacity: 0.4;
      }
      .swiper-pagination-bullet-active {
        opacity: 1;
        background: #ffffff;
      }
    }
  }
  &.swiper-left {
    .swiper-slide {
      > div {
        .info {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          z-index: 3;
          height: calc(100% - 90px);
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          .bold-txt {
            font-family: var(--body-fonts);
            font-size: 30px;
            @include query-lt-lg {
              font-size: 1.25rem;
            }
            color: #ffffff;
            text-transform: uppercase;
          }
          .txt {
            font-family: var(--body-fonts);
            font-size: 30px;
            @include query-lt-lg {
              font-size: 1.25rem;
            }
            color: #ffffff;
            margin-bottom: 20px;
            text-transform: uppercase;
          }
          .read-more {
            &:hover {
              text-decoration: none;
              color: $color-neutral-white !important;
              background: $color-blue-primary;
            }
            box-shadow: 0 4px 9px 0 rgba(0, 0, 0, 0.2);
            border-width: 1px;
            border-color: rgb(255, 255, 255);
            border-style: solid;
            border-radius: 5px;
            width: 173px;
            margin-left: auto;
            margin-right: auto;
            display: block;
            font-family: var(--body-fonts);
            font-size: 16px;
            color: $color-blue-primary;
            background-color: $color-neutral-white;
            text-decoration: none !important;
            line-height: 40px;
            text-align: center;
          }
        }
      }
    }
    .swiper-button-next,
    .swiper-button-prev {
      background: none;
    }
    .swiper-button-prev {
      left: 30px;
    }
    .swiper-button-next {
      right: 30px;
    }
    .swiper-pagination {
      bottom: 30px;
      .swiper-pagination-bullet {
        background: #677196;
      }
      .swiper-pagination-bullet-active {
        background: #d3d6e1;
      }
    }
  }
}
