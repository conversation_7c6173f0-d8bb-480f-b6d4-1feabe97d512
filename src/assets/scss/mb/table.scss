// ==================================================
// GRID
// ==================================================
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  > table {
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    //   margin-bottom: 1rem;
    background-color: transparent;
  }
}

@mixin table-thead-common {
  thead {
    tr {
      background-color: $color-light-blue;
      th {
        padding: 0rem 0.5rem !important;
        vertical-align: middle;
        color: $color-neutral-black;
        font-size: 14px;
        font-weight: 600;
        text-align: left;
        border-bottom-width: 0px;
        border-top-width: 0px;
        white-space: normal;
        min-width: 90px;
        text-overflow: unset;
      }
      &.mat-mdc-row {
        min-height: 43px !important;
      }
      th:first-child {
        padding-left: 1rem !important;
      }
      th.action .resize-handle {
        display: none;
      }
    }
  }
}

table {
  &.app-table {
    width: 100%;
    tr {
      vertical-align: middle;
      td {
        padding: 0rem 0.5rem !important;
        word-break: break-word !important;
      }
      td:first-child {
        padding-left: 1rem !important;
      }
      //td:nth-child(1) {
      //  font-weight: 600;
      //}
      &.group-header {
        vertical-align: middle !important;
      }
    }
    &.table-primary {
      thead {
        tr {
          background-color: $color-blue-primary;
          th {
            height: 4rem;
            vertical-align: middle;
            color: white;
            font-size: 12px;
            text-align: center;
            .mat-mdc-sort-header-container {
              justify-content: center;
            }
          }
        }
      }

      tbody tr {
        td {
          text-align: center;
        }
      }

      tbody tr {
        td.left-align {
          text-align: left;
        }
        td.ft-weight {
          font-weight: 600;
        }
      }
    }
    &.table-info {
      @include table-thead-common;
      tbody tr {
        &:hover {
          background-color: $color-light-blue;
        }
        td {
          text-align: left;
        }
        .action-view {
          padding: 0.4rem !important;
        }
      }
      tbody tr {
        td.center-align {
          text-align: center;
        }
        td.ft-weight {
          font-weight: 600;
        }
      }
      .mat-mdc-sort-header-arrow {
        color: $color-blue-primary;
      }
      .heard-group {
        padding: 0 0 0 0.5rem !important;
        background-color: #f7f7f7 !important;
        border-bottom-width: 0px !important;
      }
      .mat-mdc-row {
        height: 48px !important;

      }
      .border-bottom-group-expand {
        border-bottom-width: 1px !important;
      }
      thead tr {
        height: 48px;
      }
    }
    &.table-styles-odd-even,
    &.table-striped {
      tbody {
        tr:nth-child(odd) {
          background-color: transparent;
        }
        tr:nth-child(even) {
          background-color: $color-gray-300;
        }
      }
    }
  }
  &.table-expand {
    @include table-thead-common;
    tr.detail-row {
      height: 0;
      display: none;
      &.open {
        display: table-row;
      }
      td {
        padding: 0!important;
      }
    }

    tr.element-row:not(.expanded-row):hover {
      background: whitesmoke;
    }

    tr.element-row:not(.expanded-row):active {
      background: #efefef;
    }
    > thead > .mat-mdc-header-cell {
      word-break: normal;
      padding: 0.75rem;
      .mat-mdc-sort-header-button {
        text-transform: uppercase;
      }
    }
  }
  &.table-layout-fixed {
    table-layout: fixed;
  }
  &.table-resize-col {
    table-layout: unset;
    display: table;
  }
  &.table-layout-unset {
    table-layout: unset;
  }
}

.app-table {
  .mat-mdc-header-cell {
    word-break: normal;
    padding: 0.75rem;
    .mat-mdc-sort-header-button {
      text-transform: uppercase;
    }
  }

  .mat-mdc-cell {
    word-break: normal;
    padding: 1.25rem;
    // border-bottom-style: none;
    max-width: 20rem;
    overflow: inherit;
  }

  .mat-mdc-footer-cell {
    white-space: nowrap;
    word-break: break-word;
    padding: 0.75rem;
    // border-bottom-style: none;
  }
  .position-sticky {
    position: sticky;
    top: 0;
    left: 3rem;
  }
  .position-sticky-1 {
    position: sticky;
    top: 0;
    left: 0.5rem;
  }
  .position-fixed-end {
    right: 1.1rem;
    position: sticky;
    z-index: 1;
    flex-direction: row;
    box-sizing: border-box;
    display: flex;
  }
}

.table-over {
  max-width: 100%;
  overflow: auto;
}

th.action {
  width: 160px;
}
.red {
  color: red
}
.dragCursor {
  cursor: grabbing!important;
}

table th {
  position: relative;
}

.resize-handle {
  display: inline-block;
  border-right: 1px solid lightgray;
  position: absolute;
  top: calc(50% - 24px / 2);
  right: 0;
  height: 24px;
  cursor: col-resize;
  opacity: 1;
  transition: border 200ms ease-in-out;
}

.resize-handle:hover {
  width: 20px;
  border-right: 2px solid var(--color-blue-primary);
}
