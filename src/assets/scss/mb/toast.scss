
// .toast-container{

// }
.toast-top-right {
  top: 70px !important;
}
// .toast-title{
//     font-weight: bold !important;
//     color: #000 !important;
// }
// .toast-message{
//     color: $color-gray-700 !important;
// }

// .toast-success{
//     background-image: url('assets/images/icons/<EMAIL>') !important;
//     background-size: 40px !important;
//     padding-left: 60px !important;
//     background-color: $color-neutral-white !important;
// }
// .toast-warning{
//     background-image: url('assets/images/icons/<EMAIL>') !important;
//     background-size: 40px !important;
//     padding-left: 60px !important;
//     background-color: $color-neutral-white !important;
// }
// .toast-error{
//     background-image: url('assets/images/icons/<EMAIL>') !important;
//     background-size: 40px !important;
//     padding-left: 60px !important;
//     background-color: $color-neutral-white !important;
// }
// .toast-info{
//     background-image: url('assets/images/icn_success.png') !important;
//     background-size: 40px !important;
//     padding-left: 60px !important;
//     background-color: $color-neutral-white !important;
// }
