// Reboot
:root {
  --body-fonts: "<PERSON><PERSON>", sans-serif;
}
body {
  margin: 0;
  line-height: normal;
  font-family: var(--body-fonts);
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

html,
body {
  position: relative; // 1
  height: 100%;
  overflow: auto; // 2
  font-size: #{$font-size-base}px;
  color: $color-gray-900;
}

*,
::after,
::before {
  box-sizing: border-box;
}

* {
  &:active,
  :focus {
    outline: none !important;
  }
}

label {
  margin-bottom: 0;
}

body {
  margin: 0px;
  height: 100%;
}

ul,
li {
  margin: 0px;
  list-style-type: none;
  padding-left: 0px;
}

button,
input {
  background: none;
  border: none;
}

button:focus,
a:focus,
input:focus,
input:active {
  outline: none !important;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none !important;
  color: $color-blue-primary !important;
  cursor: pointer !important;
}

a.btn,
a.btn:hover {
  text-decoration: none !important;
}

code {
  padding: 3px;
  font-size: 90%;
  word-break: break-word;
}

dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}
