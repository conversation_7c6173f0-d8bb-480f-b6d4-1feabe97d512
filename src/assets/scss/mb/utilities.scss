@use "sass:math";
@import 'styles-variables';
// Size Height and Weight
@for $i from 1 through 100 {
  .w-#{ "" + $i*1} {
    width: math.percentage(math.div(($i*1), 100)) !important;
  }

  .h-#{ "" + $i*1} {
    height: math.percentage(math.div(($i*1), 100)) !important;
  }

  @each $breakpoint in $breakpoints {
    @include create-responsives-style($breakpoint) {
      .w-#{ "" + $breakpoint}-#{ "" + $i*1} {
        width: math.percentage(math.div(($i*1), 100)) !important;
      }

      .h-#{ "" + $breakpoint}-#{ "" + $i*1} {
        height: math.percentage(math.div(($i*1), 100)) !important;
      }
    }
  }
}

.w-33 {
  width: 33.33333333%!important;
}

/* max width - max height*/
@for $i from 1 through 100 {
  .max-w-#{ "" + $i*1} {
    width: math.percentage(math.div(($i*1), 100)) !important;
  }

  .max-h-#{ "" + $i*1} {
    height: math.percentage(math.div(($i*1), 100)) !important;
  }

  @each $breakpoint in $breakpoints {
    @include create-responsives-style($breakpoint) {
      .max-w-#{ "" + $breakpoint}-#{ "" + $i*1} {
        max-width: math.percentage(math.div(($i*1), 100)) !important;
      }

      .max-h-#{ "" + $breakpoint}-#{ "" + $i*1} {
        max-height: math.percentage(math.div(($i*1), 100)) !important;
      }
    }
  }
}


@for $i from 1 through 100 {
  .flex-#{ "" + $i*1} {
    width: math.percentage(math.div(($i*1), 100)) !important;
    max-width: math.percentage(math.div(($i*1), 100)) !important;
  }
}


// BackgroundColor
@each $name,
$color in $colors {
  .bg-#{ "" + $name} {
    background-color: $color;
  }
}

.br-header-r {
  border-right: solid 2px $color-gray-300;
}

@each $type in (w, h) {
  img.fit-#{ "" + $type} {
    @if $type==w {
      width: 100%;
      height: auto;
    } @else if $type==h {
      width: auto;
      height: 100%;
    }
  }
}

// Shadow
.shadow-inner-sm {
  box-shadow: $color-gray-900
}

.shadow-outer {
  @include shadow(0 0 1rem 0 opacity-color($color-gray-900, 0.2));
}

// Flex
.flex-sticky {
  display: flex;
  flex-direction: column;
}

.flex-content {
  flex: 1 0 auto;
}

.flex-footer {
  flex-shrink: 1;
}

.cursor-pointer {
  cursor: pointer;
}

// Word Break
.break-all {
  word-break: break-all !important;
}

.title-page {
  font-size: 24px !important;
  font-weight: bold !important;
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}
.header-page {
  //background: #fff;
}

.br {
  border-radius: 0.5rem;
}

.hidden {
  display: none !important;
}

.fullWidth {
  width: 100%;
}

.txt-dot {
  display: inline-block;
  max-width: 500px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}

.flex-center-row {
  display: flex;
  flex-direction: row;
  align-items: center
}

.flex-start-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.flex-end-row {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
}

.d-flex, .flex {
  display: flex !important;
}

@each $class,
$styles in $flex-justify-content {
  .justify-content-#{ "" + $class} {
    justify-content: $styles !important;
  }
}

* {
  @include scrollbars(6px, rgb(158, 158, 158), #353434, transparent);
}

* {
  &:active,
  :focus {
    outline: none !important;
  }
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill { flex: 1 1 auto !important; }

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {
  $min: breakpoint-min($name, $breakpoints);
  @if $min {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {
  $min: map-get($breakpoints, $name);
  @return if($min != 0, $min, null);
}

@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {
  @return if(breakpoint-min($name, $breakpoints) == null, "", "-#{$name}");
}

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

    .flex#{$infix}-row            { flex-direction: row !important; }
    .flex#{$infix}-column         { flex-direction: column !important; }
    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }
    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }

    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }
    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }
    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }
    .flex#{$infix}-fill         { flex: 1 1 auto !important; }
    .flex#{$infix}-grow-0       { flex-grow: 0 !important; }
    .flex#{$infix}-grow-1       { flex-grow: 1 !important; }
    .flex#{$infix}-shrink-0     { flex-shrink: 0 !important; }
    .flex#{$infix}-shrink-1     { flex-shrink: 1 !important; }

    .justify-content#{$infix}-start   { justify-content: flex-start !important; }
    .justify-content#{$infix}-end     { justify-content: flex-end !important; }
    .justify-content#{$infix}-center  { justify-content: center !important; }
    .justify-content#{$infix}-between { justify-content: space-between !important; }
    .justify-content#{$infix}-around  { justify-content: space-around !important; }

    .align-items#{$infix}-start    { align-items: flex-start !important; }
    .align-items#{$infix}-end      { align-items: flex-end !important; }
    .align-items#{$infix}-center   { align-items: center !important; }
    .align-items#{$infix}-baseline { align-items: baseline !important; }
    .align-items#{$infix}-stretch  { align-items: stretch !important; }

    .align-content#{$infix}-start   { align-content: flex-start !important; }
    .align-content#{$infix}-end     { align-content: flex-end !important; }
    .align-content#{$infix}-center  { align-content: center !important; }
    .align-content#{$infix}-between { align-content: space-between !important; }
    .align-content#{$infix}-around  { align-content: space-around !important; }
    .align-content#{$infix}-stretch { align-content: stretch !important; }

    .align-self#{$infix}-auto     { align-self: auto !important; }
    .align-self#{$infix}-start    { align-self: flex-start !important; }
    .align-self#{$infix}-end      { align-self: flex-end !important; }
    .align-self#{$infix}-center   { align-self: center !important; }
    .align-self#{$infix}-baseline { align-self: baseline !important; }
    .align-self#{$infix}-stretch  { align-self: stretch !important; }
  }
}


.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}


/* MARGIN */

.ml-auto {
  margin-left: auto;
}
.mr-auto {
  margin-right: auto;
}
