@import url('~ngx-toastr/toastr');

.toast-container {
  .ngx-toastr {
    min-height: 40px;
    padding: 14px 16px;
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.7);
    //background-color: #323232;

    //@include mat-elevation(6);

    // &:hover {
    //   @include mat-elevation(10);
    // }
  }

  .toast-success,
  .toast-info,
  .toast-warning,
  .toast-error {
    box-shadow: 0 0 12px #e2e2e2!important;
    border-radius: 8px;
    min-height: 44px;
    min-width: 400px !important;
    align-items: center;
    padding-right: 30px;
    justify-content: space-between; /* Đảm bảo icon ở bên phải */
    //background-image: none;
  }

  /* Thêm container cho title và message để sắp xếp theo column */
  .toast-message-container {
    display: flex;
    flex-direction: column; /* Sắp xếp title và message theo chiều dọc */
    flex-grow: 1; /* Chiếm toàn bộ không gian bên trái */
  }

  /* <PERSON><PERSON><PERSON> thông báo riêng biệt */

  .toast-error {
    background-color: #FEDDD4 !important;
    border: 1px solid #F4A8A0 !important;
    color: #141B4D !important;
    background-image: url("../../images/icons/error-circle.svg");
    background-position: center right 10px !important;
  }
  .toast-success {
    background-color: #edfdf0 !important;
    border: 1px solid #2fd197 !important;
    color: #141B4D !important;
    background-image: url("../../images/icons/success-circle.svg");
    background-position: center right 10px !important;
  }

  .toast-warning {
    background-color: #f9ead4 !important;
    border: 1px solid #f89406 !important;
    color: #141B4D !important;
    background-image: url("../../images/icons/warning-circle.svg");
    background-position: center right 10px !important;
  }

  .toast-info {
    background-color: #eaeffc !important;
    border: 1px solid #4169e1 !important;
    color: #141B4D !important;
    background-image: url("../../images/icons/info-circle.svg");
    background-position: center right 10px !important;
  }

  /* Nút đóng */
  .toast-close-button {
    font-size: inherit;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);

    &:hover {
      color: inherit;
      opacity: 0.6;
    }
  }
}
