$swift-ease-out-duration: 400ms;
$swift-ease-out-timing-function: cubic-bezier(.25, .8, .25, 1);

@function swift-ease-out($property) {
    // The Material default animation curves.
    $transition: $property $swift-ease-out-duration $swift-ease-out-timing-function;

    @return $transition;
}

@function fast-out-slow($property) {
    // The Material default animation curves.
    $transition: $property 225ms cubic-bezier(.4, 0, .2, 1);

    @return $transition;
}