.mat-drawer.sidebarNav {
  width: $sidenav-desktop;
  flex-shrink: 0;
  transition: swift-ease-out(width);
  position: absolute;
  overflow-x: hidden;
  border: 0 !important;
}

.branding {
  padding: 13px 20px;
}

.sidebarNav-mini {
  .sidebarNav {
    width: $sidenav-mini;

    .sidebar-list {
      .mdc-list-group__subheader {
        .caption-icon {
          display: inline;
        }
        .caption-text {
          display: none;
        }
      }
      .menu-list-item {
        padding: 8px 12px !important;

        .mdc-list-item__content {
          display: none;
        }

        .mdc-list-item__start {
          margin-left: 6px !important;
          margin-right: 8px !important;
        }
      }
    }

    &:hover {
      width: $sidenav-desktop;
      box-shadow: 0 10px 20px rgba(0,0,0,0.12);
      .profile-bar {
        display: block;
      }

      .sidebar-list {
        .menu-list-item {
          padding: 11px 16px !important;

          .mdc-list-item__content {
            display: inline;
          }

          .mdc-list-item__start {
            margin-left: 0 !important;
          }
        }

        &.mdc-list {
          padding: 0 16px;
          width: $sidenav-desktop;
          .mdc-list-group__subheader {
            text-align: left;
            .caption-icon {
              display: none;
            }
            .caption-text {
              display: inline;
            }
          }
        }
      }
    }
  }

  

  .hideMenu {
    overflow: hidden;
    width: $sidenav-mini;
  }

  .branding {
    width: $sidenav-mini - 15px;
    overflow: hidden;
  }

  .sidebar-list {
    &.mdc-list {
      padding: 0 12px;
      width: $sidenav-mini;
      .mdc-list-group__subheader {
        text-align: center;
      }
    }
  }

  .contentWrapper {
    transition: swift-ease-out(width);
  }
}

@media (min-width: 1024px) {
  .sidebarNav-mini {
    .contentWrapper {
      margin-left: $sidenav-mini !important;
    }
  }
}

.customizerBtn {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 9;
}

.sidebar-list {
  &.mdc-list {
    padding: 0 16px;
    width: $sidenav-desktop;
    .mdc-list-group__subheader {
      margin: 0;
      text-transform: uppercase;
      font-size: 0.75rem;
      font-weight: 600;
      letter-spacing: 1.5px;
      padding: 7px 0;
      line-height: 26px;
      //color: $textPrimary;

      .caption-icon {
        display: none;
      }
      .caption-text {
        display: inline;
      }
    }

    .sub-item-icon {
      width: 16px !important;
      height: 16px !important;
      opacity: 0.8;
      margin-inline-end: 13px;
    }

    .sidebar-divider {
      height: 0.5px;
      display: block;
      margin: 12px 0;
      background: $borderColor;
      width: 100%;
    }

    .menu-list-item {
      border-radius: $border-radius;
      height: 45px;
      padding: 11px 16px !important;
      margin-bottom: 4px;

      &.twoline {
        height: 60px;
        align-items: center;
      }

      &:before,
      &:focus {
        z-index: -1;
      }

      &.disabled {
        opacity: 0.38;
      }

      .item-chip {
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      &.activeMenu {
        box-shadow: 0 17px 20px -8px rgba(77, 91, 236, 0.231372549);
        .mdc-list-item__primary-text {
          color: $white !important;
        }

        .mat-mdc-list-item-icon {
          color: $white !important;
        }
      }

      .mdc-list-item__start {
        margin-right: 10px;
        margin-left: 0 !important;
        width: 18px;
        height: 18px;
        line-height: 0px;
        fill: transparent !important;
        color: $textPrimary;
      }

      .mdc-list-item__primary-text {
        // color: $textPrimary;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .arrow-icon {
          display: flex;

          .mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

.flex-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}
