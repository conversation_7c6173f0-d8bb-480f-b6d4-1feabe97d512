// card
.mat-mdc-card {
  margin-bottom: 30px;
  //border-radius: $border-radius;
}
.mat-mdc-card-header {
  padding: 30px 30px 0;
}

.mat-mdc-card-content {
  padding: 0 30px;
}

.mat-mdc-card {
  background-color: $cardbg;
  > .mat-mdc-card-content {
    padding: 30px;
  }
}

.cardWithShadow,
.mat-expansion-panel {
  box-shadow: $cardshadow !important;
}

.mat-mdc-card-title {
  line-height: 1.6rem;
  margin-bottom: 8px;
}

.mat-mdc-card-subtitle {
  color: $bodyColor;
}

.mdc-card__actions {
  padding: 30px;
}

.theme-card.mat-mdc-card {
  .mat-mdc-card-header {
    padding: 16px 30px;
  }
  .mat-mdc-card-content {
    padding: 30px;
  }
}

.card-hover {
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  &:hover {
    scale: 1.01;
    transition: all 0.1s ease-in 0s;
  }
}

// chip
html .mat-mdc-chip {
  height: 30px;
  font-size: 14px;
  background-color: $light;
}

.cardBorder {
  .mdc-card,
  .mat-expansion-panel {
    box-shadow: none !important;
    border: 1px solid $borderColor !important;
    &.shadow-none {
      border: 0 !important;
    }
  }
}

.mat-mdc-progress-bar {
  border-radius: 6px;
}
