@charset "utf-8";


@font-face {
  font-family: 'icomoon';
  src: url('icons.eot?7e22yj');
  src:
    url('icons.eot?7e22yj#iefix')
      format('embedded-opentype'),
    url('icons.ttf?7e22yj') format('truetype'),
    url('icons.woff?7e22yj') format('woff'),
    url('icons.svg?7e22yj#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

/* AVERTA */
@font-face {
  font-family: Roboto;
  src: url('averta/Intelligent-Design-AvertaStdCY-Regular_3.otf');
  font-weight: normal;
}

@font-face {
  font-family: Semibold;
  src: url('averta/Intelligent-Design-AvertaStdCY-Semibold_1.otf');
  font-weight: normal;
}

@font-face {
  font-family: Roboto-bold;
  src: url('averta/Intelligent-Design-AvertaStdCY-Bold_1.otf');
  font-weight: 600;
}

@font-face {
  font-family: Extrabold;
  src: url('averta/Intelligent-Design-AvertaStdCY-Extrabold_1.otf');
  font-weight: normal;
}

@font-face {
  font-family: Roboto-bold-Italic;
  src: url('averta/Intelligent-Design-AvertaStdCY-BoldItalic_1.otf');
  font-weight: 600;
}
