{"home": "Trang chủ", "validations": {"required": "<PERSON><PERSON> lòng nhập thông tin bắt buộc", "minlength": "<PERSON><PERSON><PERSON> hãy nhập ít nhất {{number}} kí tự", "maxlength": "<PERSON><PERSON><PERSON> hãy nhập nhỏ hơn {{number}} kí tự", "min": "<PERSON><PERSON><PERSON> hãy nhập giá trị lớn hơn hoặc bằng {{number}}", "max": "<PERSON><PERSON><PERSON> hãy nhập giá trị nhỏ hơn hoặc bằng {{number}}", "exist": "<PERSON><PERSON><PERSON> trị {{value}} không tồn tại", "inconsistent": "<PERSON><PERSON><PERSON><PERSON> phù hợp với {{value}}", "email": "Địa chỉ <PERSON><PERSON> k<PERSON> h<PERSON> lệ", "matDatepickerMin": "<PERSON><PERSON><PERSON> hãy nhật lớn hơn ngày {{date}}", "matDatepickerMax": "<PERSON><PERSON><PERSON> hãy nhập nhỏ hơn ngày {{date}}", "no-required-validate": "<PERSON><PERSON> liệu ch<PERSON> validate", "search-process": "Bạn vui lòng nhập điều kiện tìm kiếm", "errortype": "<PERSON><PERSON><PERSON> trị nhập không đúng định dạng", "search-require": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> nhập thông tin tìm kiếm", "not-null": "kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống", "whitespace": "<PERSON><PERSON><PERSON><PERSON> nhập ký tự bắt đầu là khoảng trắng", "required-import": "Vui lòng chọn file import", "`required-upload-file-audio`": "Vui lòng chọn file import", "limitProfile": "<PERSON><PERSON><PERSON><PERSON> quá số HS chưa phân giao", "maxDateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian tìm kiếm tối đa là {{number}} ngày"}, "title": {"contact-history": "<PERSON><PERSON><PERSON> s<PERSON> liên hệ", "change-history": "<PERSON><PERSON><PERSON> sử thay đổi"}, "error": {"title": "<PERSON>rang quý khách đang truy cập không tồn tại. Ngân Hàng TMCP Quân Đội xin lỗi quý khách vì sự bất tiện này.", "button": "Quay về trang chủ", "login_permission": "<PERSON><PERSON><PERSON> không có quyền truy cập hệ thống", "4xx-error": "<PERSON><PERSON><PERSON> cập thông tin xảy ra lỗi. <PERSON><PERSON><PERSON> hệ CNTT để được hỗ trợ", "error-connect": "Bạn không có quyền truy cập vào hệ thống Dform", "emb-error-connect": "<PERSON><PERSON>n không có quyền truy cập vào hệ thống <PERSON>", "retail-error-connect": "<PERSON><PERSON>n không có quyền truy cập vào hệ thống Retail", "tablau-error-connect": "<PERSON><PERSON><PERSON> không có quyền truy cập vào hệ thống", "error-account-category": "Không có categoryId", "error-token": "<PERSON>ử lý xác thực xảy ra lỗi. <PERSON><PERSON><PERSON> hệ CNTT để được hỗ trợ", "error-identifier-account": "<PERSON><PERSON><PERSON> chọn tài khoản cần đ<PERSON>nh danh", "http-errorcode-401": "<PERSON><PERSON><PERSON> giao d<PERSON>ch không hợp lệ. <PERSON><PERSON> lòng đăng nhập lại.", "error-update-account": "<PERSON><PERSON> xảy ra lỗi xử lý cập nhật số tài k<PERSON>n. <PERSON>ui lòng thử lại!", "timeout_message": "<PERSON><PERSON><PERSON> chủ không phản hồi. <PERSON><PERSON> lòng thử lại sau hoặc liên hệ CNTT để được hỗ trợ."}, "btn": {"accept": "Đồng ý", "approved": "<PERSON><PERSON>", "reject": "<PERSON><PERSON> chối", "cancel": "<PERSON><PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "close": "Đ<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "send-approve": "<PERSON><PERSON><PERSON>", "create-ticket": "Tạo ticket", "search": "<PERSON><PERSON><PERSON>", "fullscreen": "<PERSON><PERSON><PERSON> màn hình"}, "dialog": {"notification": "<PERSON><PERSON><PERSON><PERSON> báo", "confirm": "<PERSON><PERSON><PERSON>", "message-error": "<PERSON><PERSON> xảy ra lỗi xử lý", "valid-error": "<PERSON><PERSON> liệu ch<PERSON> validate", "add-data-success": "<PERSON><PERSON><PERSON><PERSON> mới thành công", "add-data-error": "<PERSON><PERSON><PERSON><PERSON> mới thất bại", "change-data-success": "Thay đổi dữ liệu thành công", "approved-success": "<PERSON><PERSON>t yêu cầu thành công", "approved-error": "<PERSON><PERSON> du<PERSON>t yêu cầu thất bại", "reject-success": "Từ chối phê duyệt thành công", "reject-error": "Từ chối phê duyệt thất bại", "change-data-error": "Thay đổi dữ liệu không thành công", "delete-data-success": "<PERSON><PERSON><PERSON> thành công", "delete-data-error": "<PERSON><PERSON><PERSON> thất bại!", "export-data-error": "<PERSON><PERSON>t excel thất bại!", "export-data-success": "<PERSON><PERSON><PERSON> excel thành công, vui lòng kiểm tra thư mục tải về", "import-data-error": "<PERSON><PERSON><PERSON> nhập dữ liệu thất bại!", "import-data-success": "<PERSON><PERSON><PERSON> nhập dữ liệu thành công", "valid-co-owner": "<PERSON>ui lòng chọn đồng sở hữu!", "khongcungcap-email": "Địa chỉ <PERSON><PERSON> k<PERSON> h<PERSON> lệ", "khongcungcap-mobie": "Số mobile không hợp lệ", "signature_and_seal_sample": "Chữ ký và mẫu dấu", "confirm-delete": "Bạn chắc chắn muốn thực hiện xóa?", "confirm-cancel-import": "Bạn chắc chắn muốn thực hiện hủy?", "send-approve-success": "<PERSON><PERSON><PERSON> phê du<PERSON><PERSON>t thành công", "not-config-delete": "Chưa cấu hình API DELETE", "r-u-sure-delete": "Bạn có chắc muốn xóa", "event-has-not-assign-yet": "<PERSON><PERSON><PERSON> cài đặt để xử lý sự kiện", "create-interact-success": "<PERSON><PERSON><PERSON> t<PERSON> tác thành công", "upload-file-failed": "<PERSON><PERSON><PERSON> file thất bại", "edit-interact-success": "Chỉnh sửa tương tác thành công", "max-quantity-error": "<PERSON><PERSON><PERSON><PERSON> quá dung lượng file đ<PERSON>h kèm"}, "msg": {"import-preview-count": "Tải file lên thành công"}, "lbl": {"add": "<PERSON><PERSON><PERSON><PERSON> mới", "no-data": "Chưa có dữ liệu", "stt": "STT", "isActive": "<PERSON><PERSON><PERSON><PERSON> thái", "app": {"title": "DANH SÁCH ỨNG DỤNG TRIỂN KHAI WEBPORTAL", "name": "<PERSON><PERSON><PERSON> th<PERSON>ng", "action": "<PERSON><PERSON> quyền hệ thống", "info": "Thông tin", "start-time": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "end-time": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "phone-number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email"}, "interact": {"customer-info": "Thông tin khách hàng", "general-info": "Th<PERSON>ng tin chung", "card": "Thẻ", "card-detail": "Thông tin chi tiết thẻ", "card-w4": "Th<PERSON>ng tin thẻ (Way4)", "card-portal": "Thông tin thẻ (Card Portal)", "form-detail": "<PERSON><PERSON><PERSON><PERSON> tin nhập liệu", "change-history": "<PERSON><PERSON><PERSON> sử thay đổi", "maximum-file-each-request": "<PERSON><PERSON><PERSON> đa 10 file/ 1 yêu cầu", "empty-place-holder": "Định dạng .xlsx - <PERSON><PERSON> l<PERSON> 10MB - <PERSON><PERSON><PERSON> đa 10 file", "get-data-failed": "<PERSON><PERSON>y dữ liệu form thất bại", "person-in-change-place-holder": "<PERSON><PERSON><PERSON> nguời phụ trách", "new-value": "<PERSON><PERSON><PERSON> trị mới", "old-value": "<PERSON><PERSON><PERSON> trị cũ", "campaign-name": "Tên Campaign", "script": "<PERSON><PERSON><PERSON>", "profile-info": "<PERSON><PERSON><PERSON><PERSON> tin hồ sơ", "additional-info": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung"}, "card-info": {"card-number": "Số thẻ", "card-class": "Loại SP thẻ", "card-level": "Hạng thẻ", "card-type": "Loại thẻ", "customer-name": "Tên chủ thẻ", "customer-identity-number": "CMND/HC", "card-issued-branch-name": "<PERSON> nhánh phát hành thẻ", "card-debit-account-number": "Số tài <PERSON>n", "registered-phone-number": "SĐT thẻ", "registered-email": "Email thẻ", "secure-3d-phone-number": "SĐT 3D thẻ", "secure-3d-email": "Email 3D thẻ", "card-receive-method": "<PERSON><PERSON><PERSON> thức nhận thẻ", "card-receive-address": "Địa chỉ nhận thẻ", "card-secure-3d-sms-entry": "SL cho phép sai OTP3DSMS thẻ", "card-secure-3d-email-entry": "SL cho phép sai OTP3DEmail thẻ", "tracking-delivery": "<PERSON>ã vận đơn", "delivery-partner": "Đơn vị vận chuyển", "card-issued-branch-name-requested": "<PERSON> nh<PERSON>h yêu cầu", "delivery-status": "<PERSON><PERSON>nh trạng chuyển ph<PERSON>t", "delivery-note": "<PERSON><PERSON> chú chuyển phát", "contract": "<PERSON><PERSON> hợp đồng", "card-id": "ID thẻ", "rbs-number": "Rbs Num", "card-open-date": "<PERSON><PERSON><PERSON> ph<PERSON>t hành thẻ", "card-expire-date": "<PERSON><PERSON><PERSON> hết hạn thẻ", "card-activate-date": "<PERSON><PERSON><PERSON> k<PERSON> ho<PERSON> thẻ", "card-credit-limit": "<PERSON><PERSON><PERSON> mức <PERSON> c<PERSON>p", "card-amount-available": "<PERSON><PERSON><PERSON> mức còn lại", "card-payment-method": "<PERSON><PERSON><PERSON> thức thanh toán", "card-status": "Tình trạng thẻ", "card-ecommerce-status": "Tình trạng đóng mở chi tiêu qua Internet", "card-activate-status": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "card-pin-attempts": "<PERSON><PERSON> lần nh<PERSON>p sai P<PERSON>", "card-debit-method": "<PERSON><PERSON><PERSON> thức trích nợ", "card-interest-rate": "<PERSON><PERSON><PERSON>", "registered-phone-with-card": "SĐT đăng ký với thẻ", "registered-email-with-mb": "<PERSON>ail đ<PERSON> ký với <PERSON>", "card-main-or-sub": "Thẻ chính hay thẻ phụ"}}, "duplicateSystemGroupPermissionTitleError": "<PERSON><PERSON><PERSON><PERSON> thể chọn hệ thống đã đư<PERSON><PERSON> chọn trước đó", "existGroupPermissionTitleError": "<PERSON><PERSON> thống đã đ<PERSON><PERSON><PERSON> gán với chức danh", "groupPermissionAssignedError": "<PERSON><PERSON><PERSON><PERSON> quyền đã đ<PERSON><PERSON><PERSON> gán chức danh", "duplicateSystemError": "<PERSON><PERSON><PERSON><PERSON> thể chọn hệ thống đã đư<PERSON><PERSON> chọn trước đó", "existUserInSystemError": "<PERSON><PERSON> tồn tại user trong hệ thống", "invalid.input": "<PERSON>hông tin đầu vào không hợp lệ", "Validation failed": "<PERSON>hông tin đầu vào không hợp lệ", "There was an error processing the request, please contact the administrator!": "<PERSON><PERSON> lỗi xảy ra. <PERSON><PERSON> lòng liên hệ CNTT để được hỗ trợ."}