<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): About Janus</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top a[href='index.html']").addClass("active");
		});
		$(".footer").load("footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="css/demo.css" type="text/css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>About</h1>
			</div>
			<h3 class="mt-3">Janus: the general purpose WebRTC server</h3>
			<div class="alert alert-warning">
				<b>Note Well:</b> these are the demos and documentation for the <code>multistream</code> version
				of Janus, which is a new version. If you want to check the previous version of
				Janus instead (i.e., <code>0.x</code>, a.k.a. "legacy") click
				<a href="https://janus-legacy.conf.meetecho.com">here</a> instead.
			</div>
			<div class="row">
				<div class="col-md-8">
					<p>Janus is a WebRTC Server developed by <a href="http://www.meetecho.com">Meetecho</a>
					conceived to be a general purpose one. As such, it doesn't provide any functionality per
					se other than implementing the means to set up a WebRTC media communication with a browser,
					exchanging JSON messages with it, and relaying RTP/RTCP and messages between browsers and
					the server-side application logic they're attached to. Any specific feature/application
					is provided by server side plugins, that browsers can then contact via Janus to take advantage of the
					functionality they provide. Example of such plugins can be implementations of applications
					like echo tests, conference bridges, media recorders, SIP gateways and the like.</p>
				</div>
				<div class="col-md-4 mt-auto mb-auto">
					<img src="janus-logo.png">
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<p>The reason for this is simple: we wanted something that would have a
					<code>small footprint</code> (hence a C implementation) and that we could only
					equip with what was <code>really needed</code> (hence pluggable modules). That is,
					something that would allow us to deploy either a full-fledged WebRTC
					gateway on the cloud, or a small nettop/box to handle a specific use case.</p>
				</div>
			</div>
			<div class="row">
				<div class="alert alert-primary">Check the <a class="alert-link" href="docs/">Documentation</a>
				for more details about Janus, or check out the <a class="alert-link" href="demos/">Demos</a>
				to see it in action.</div>
			</div>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>
</body>
</html>
