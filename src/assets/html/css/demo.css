body {
	padding-top: 80px;
}

a {
	text-decoration: none;
}

.hide {
	display: none !important;
}

.z-2 {
	z-index: 2000;
}

.rounded {
	border-radius: 5px;
}

.centered {
	display: block;
	margin: auto;
}

.relative {
	position: relative;
}

.top-left {
	position: absolute;
	top: 0px;
	left: 0px;
}

.top-right {
	position: absolute;
	top: 0px;
	right: 0px;
}

.bottom-left {
	position: absolute;
	bottom: 0px;
	left: 0px;
}

.bottom-right {
	position: absolute;
	bottom: 0px;
	right: 0px;
}

.navbar-brand {
	margin-left: 0px !important;
}

.navbar {
	-webkit-box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.49);
	-moz-box-shadow:    0px 3px 5px rgba(100, 100, 100, 0.49);
	box-shadow:         0px 3px 5px rgba(100, 100, 100, 0.49);
}

.navbar-header {
	padding-left: 40px;
}

.btn-group-xs > .btn, .btn-xs {
	padding: 1px 5px;
	font-size: 12px;
	line-height: 1.5;
	border-radius: 3px;
}

.divider {
	width: 100%;
	text-align: center;
}

.divider hr {
	margin-left: auto;
	margin-right: auto;
	width: 45%;
}

.fa-2 {
	font-size: 2em !important;
}
.fa-3 {
	font-size: 4em !important;
}
.fa-4 {
	font-size: 7em !important;
}
.fa-xl {
	font-size: 12em !important;
}
.fa-6 {
	font-size: 20em !important;
}

div.no-video-container {
	position: relative;
}

.no-video-icon {
	width: 100%;
	height: 240px;
	text-align: center;
	padding-top: 5rem !important;
}

.no-video-text {
	text-align: center;
	position: absolute;
	bottom: 0px;
	right: 0px;
	left: 0px;
	font-size: 24px;
}

.meetecho-logo {
	padding: 12px !important;
}

.meetecho-logo > img {
	height: 26px;
}

pre {
	white-space: pre-wrap;
	white-space: -moz-pre-wrap;
	white-space: -pre-wrap;
	white-space: -o-pre-wrap;
	word-wrap: break-word;
	background-color: #f5f5f5;
}

.bg-gray {
	background-color: #f5f5f5;
}

.januscon {
	font-weight: bold;
	animation: pulsating 1s infinite;
}
@keyframes pulsating {
	30% {
		color: #FFD700;
	}
}
