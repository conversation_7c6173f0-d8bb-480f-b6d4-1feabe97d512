<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): Admin/Monitor</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/6.0.0/bootbox.min.js"></script>
<script type="text/javascript" src="admin.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top li.dropdown").addClass("active");
			$(".fixed-top a[href='admin.html']").addClass("active");
		});
		$(".footer").load("../footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="../css/demo.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" type="text/css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="../forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>Janus WebRTC Server: Admin/Monitor</h1>
			</div>

			<div class="mt-4">
				<ul id="admintabs" class="nav nav-tabs" role="tablist">
					<li role="presentation" class="nav-item"><a class="nav-link active" href="#home" aria-controls="home" role="tab" data-bs-toggle="tab">Home</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#serverinfo" aria-controls="serverinfo" role="tab" data-bs-toggle="tab">Server Info</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#settings" aria-controls="settings" role="tab" data-bs-toggle="tab">Settings</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#plugins" aria-controls="plugins" role="tab" data-bs-toggle="tab">Plugins</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#transports" aria-controls="transports" role="tab" data-bs-toggle="tab">Transports</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#handlesinfo" aria-controls="handlesinfo" role="tab" data-bs-toggle="tab">Handles</a></li>
					<li role="presentation" class="nav-item"><a class="nav-link disabled" href="#tokens" aria-controls="tokens" role="tab" data-bs-toggle="tab">Stored Tokens</a></li>
				</ul>

				<div class="tab-content" style="padding: 20px;">
					<div role="tabpanel" class="tab-pane fade in active show" id="home">
						<p>This is just an example of how you can build an UI on top of the
						existing <code>Admin/Monitor</code> interface. This page will only
						work as it is if you enabled the API (which is disabled by default)
						and you're using the default values. Edit the backend settings in
						the <code>admin.js</code> JavaScript code to adapt it to your
						configuration if you changed anything.</p>
						<p>The <code>Server Info</code> tab, as the name suggests, provides
						you with a view of the information related to the Janus instance
						you're using, e.g., in terms of the features that have been enabled,
						the modules that are available and so on. That's the same info you'd
						get contacting the Janus API at the <code>/janus/info</code> backend.</p>
						<p>The <code>Settings</code> tab instead allows you to inspect a
						few of the current settings in Janus (e.g., debug level and so on)
						and provides you with a way to change them dynamically.</p>
						<p>The <code>Plugins</code> tab presents the list of media plugins
						available in this Janus instance, and allows you to interact with
						them, assuming they implement the <code>handle_admin_message</code> API.
						The <code>Transports</code> tab does the same for transport plugins,
						and allows you to send requests to tweak the behaviour of the plugin
						or query its internal state, assuming they support this somehow.</p>
						<p>The <code>Handles</code> tab allows you to browse the currently active sessions
						and handles in Janus. Selecting a specific handle will provide you
						with all the current info related to it, including plugin it is
						attached to, any plugin specific information that may be relevant,
						ICE/DTLS states, amount of data being exchanged and so on. This
						section is especially helpful when you want to debug issues with
						a PeerConnection: you can find more details in
						<a href="http://www.meetecho.com/blog/understanding-the-janus-admin-api/" target="_blank">this blog post</a>.</p>
						<p>Finally, the <code>Stored Tokens</code> tab allows you to list
						existing authentication tokens, create new ones, associate plugin
						permissions and the like. This feature will only be possible if
						you enabled the stored-token authentication mechanism in Janus, of course.</p>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="serverinfo">
						<h5>Server Info</h5>
						<div>
							<table class="table table-striped mb-5" id="server-details">
							</table>
						</div>
						<h5>Dependencies</h5>
						<div>
							<table class="table table-striped mb-5" id="server-deps">
							</table>
						</div>
						<h5>Plugins</h5>
						<div>
							<table class="table table-striped mb-5" id="server-plugins">
							</table>
						</div>
						<h5>Transports</h5>
						<div>
							<table class="table table-striped mb-5" id="server-transports">
							</table>
						</div>
						<h5>Event handlers</h5>
						<div>
							<table class="table table-striped mb-5" id="server-handlers">
							</table>
						</div>
						<h5>Loggers</h5>
						<div>
							<table class="table table-striped mb-5" id="server-loggers">
							</table>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="settings">
						<h5>Settings <i id="update-settings" class="fa-solid fa-rotate" title="Refresh settings" style="cursor: pointer;"></i></h5>
						<div>
							<table class="table table-striped" id="server-settings">
							</table>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="plugins">
						<h5>Plugins</h5>
						<div class="row">
							<div class="col-md-3">
								<ul class="list-group" id="plugins-list">
								</ul>
							</div>
							<div id="plugin-message" class="col-md-9 hide">
								<div class="row">
									<h5>Request</h5>
									<table class="table" id="plugin-request">
									</table>
								</div>
								<div class="row">
									<h5>Response</h5>
									<pre class="card card-body bg-gray w-100" id="plugin-response"></pre>
								</div>
							</div>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="transports">
						<h5>Transports</h5>
						<div class="row">
							<div class="col-md-3">
								<ul class="list-group" id="transports-list">
								</ul>
							</div>
							<div id="transport-message" class="col-md-9 hide">
								<div class="row">
									<h5>Tweak/Query</h5>
									<table class="table" id="transport-request">
									</table>
								</div>
								<div class="row">
									<h5>Response</h5>
									<pre class="card card-body bg-gray w-100" id="transport-response"></pre>
								</div>
							</div>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="handlesinfo">
						<div class="row">
							<div id="sessions" class="col-md-2">
								<h5>Sessions (<span id="sessions-num">0</span>) <i id="update-sessions" class="fa-solid fa-rotate" title="Refresh list of sessions" style="cursor: pointer;"></i></h5>
								<div id="sessions-list" class="list-group">
								</div>
							</div>
							<div id="handles" class="col-md-2">
								<h5>Handles (<span id="handles-num"></span>) <i id="update-handles" class="fa-solid fa-rotate" title="Refresh list of handles" style="cursor: pointer;"></i></h5>
								<div id="handles-list" class="list-group">
								</div>
							</div>
							<div id="info" class="col-md-8">
								<h5>Handle Info <i id="update-handle" class="fa-solid fa-rotate" title="Refresh handle info" style="cursor: pointer;"></i></h5>
								<div id="options" class="hide">
									<label class="checkbox-inline" title="Autorefresh this info every 5s">
										<input id="autorefresh" type="checkbox" value="" title="Autorefresh this info every 5s">Autorefresh
									</label>
									<label class="checkbox-inline" title="Show information as HTML">
										<input id="prettify" type="checkbox" value="" title="Show information as HTML">Prettify
									</label>
									<label class="checkbox-inline" title="Start of stop capturing traffic to .pcap">
										<input id="capture" type="checkbox" value="" title="Start of stop capturing traffic to .pcap">
										<span id="capturetext">Start capture</span>
									</label>
								</div>
								<div id="handle-info">
								</div>
							</div>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane fade" id="tokens">
						<h5>Stored Tokens <i id="update-tokens" class="fa-solid fa-rotate" title="Refresh tokens" style="cursor: pointer;"></i></h5>
						<div>
							<table class="table table-striped" id="auth-tokens">
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>
</body>
</html>
