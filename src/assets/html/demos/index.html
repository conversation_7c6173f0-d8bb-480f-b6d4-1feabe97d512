<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): Demo Tests</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top li.dropdown").addClass("active");
			$(".fixed-top a[href='index.html']").addClass("active");
		});
		$(".footer").load("../footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="../css/demo.css" type="text/css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="../forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>Janus WebRTC Server: Demo Tests</h1>
			</div>
			<table class="table table-striped mt-5">
				<tr>
					<td class="table-info" colspan=2><h3>Plugin demos</h3></td>
				</tr>
				<tr>
					<td><a href="echotest.html">Echo Test</a></td>
					<td>A simple Echo Test demo, with knobs to control the bitrate.</td>
				</tr>
				<tr>
					<td><a href="streaming.html">Streaming</a></td>
					<td>A media Streaming demo, with sample live and on-demand streams.</td>
				</tr>
				<tr>
					<td><a href="videocall.html">Video Call</a></td>
					<td>A Video Call demo, a bit like AppRTC but with media passing through Janus.</td>
				</tr>
				<tr>
					<td><a href="sip.html">SIP Gateway</a></td>
					<td>A SIP Gateway demo, allowing you to register at a SIP server and start/receive calls.</td>
				</tr>
				<tr>
					<td><a href="videoroom.html">Video Room</a></td>
					<td>A videoconferencing demo, allowing you to join a video room with up to six users.</td>
				</tr>
				<tr>
					<td><a href="mvideoroom.html">Video Room (multistream)</a></td>
					<td>The same videoconferencing demo, but using one PeerConnection to receive multiple streams.</td>
				</tr>
				<tr>
					<td><a href="audiobridge.html">Audio Room</a></td>
					<td>An audio mixing/bridge demo, allowing you join an Audio Room.</td>
				</tr>
				<tr>
					<td><a href="textroom.html">Text Room</a></td>
					<td>A text room demo, using DataChannels only.</td>
				</tr>
				<tr>
					<td><a href="recordplay.html">Recorder/Playout</a></td>
					<td>A demo to record audio/video messages, and subsequently replay them through WebRTC.</td>
				</tr>
				<tr>
					<td><a href="screensharing.html">Screen Sharing</a></td>
					<td>A webinar-like screen sharing session, based on the Video Room plugin.</td>
				</tr>
			</table>
			<table class="table table-striped mt-5">
				<tr>
					<td class="table-info" colspan=2><h3>Other legacy demos</h3></td>
				</tr>
				<tr>
					<td><a href="nosip.html">NoSIP (SDP/RTP)</a></td>
					<td>A legacy interop demo (e.g., with a SIP peer) where signalling is up to the application.</td>
				</tr>
			</table>
			<table class="table table-striped mt-5">
				<tr>
					<td class="table-info" colspan=2><h3>Advanced demos</h3></td>
				</tr>
				<tr>
					<td><a href="devices.html">Device Selection</a></td>
					<td>A variant of the Echo Test demo, that allows you to choose a specific capture device.</td>
				</tr>
				<tr>
					<td><a href="e2e.html">End-to-end Encryption</a></td>
					<td>A variant of the Echo Test demo, that allows you to encrypt the video in a way that Janus can't access it, but can still route it.</td>
				</tr>
				<tr>
					<td><a href="multiopus.html">Multichannel Opus (surround)</a></td>
					<td>A variant of the Echo Test demo, that shows multichannel/surround Opus support.</td>
				</tr>
				<tr>
					<td><a href="canvas.html">Canvas Capture</a></td>
					<td>A variant of the Echo Test demo, that shows how to use a canvas element as a WebRTC media source.</td>
				</tr>
				<tr>
					<td><a href="webaudio.html">Web Audio Processing</a></td>
					<td>A variant of the Echo Test demo, that shows how to use Web Audio to process audio before sending it to Janus.</td>
				</tr>
				<tr>
					<td><a href="virtualbg.html">Virtual Background</a></td>
					<td>A variant of the Echo Test demo, that shows how to use something like MediaPipe to add a virtual background before sending video to Janus.</td>
				</tr>
				<tr>
					<td><a href="admin.html">Admin/Monitor</a></td>
					<td>A simple page showcasing how you can use the Janus Admin/Monitor API.</td>
				</tr>
			</table>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>
</body>
</html>
