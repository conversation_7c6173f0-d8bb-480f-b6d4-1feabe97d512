<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): Device Selection Test</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/webrtc-adapter/8.2.3/adapter.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/6.0.0/bootbox.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<script type="text/javascript" src="settings.js" ></script>
<script type="text/javascript" src="janus.js" ></script>
<script type="text/javascript" src="devices.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top li.dropdown").addClass("active");
			$(".fixed-top a[href='devices.html']").addClass("active");
		});
		$(".footer").load("../footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="../css/demo.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="../forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>Device Selection
					<button class="btn btn-secondary" autocomplete="off" id="start">Start</button>
				</h1>
			</div>
			<div class="container" id="details">
				<div class="row">
					<div class="alert alert-primary mt-2 mb-5">
						Want to learn more about the <strong>EchoTest</strong> plugin?
						Check the <a target="_blank" href="https://janus.conf.meetecho.com/docs/echotest">Documentation</a>.
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<h3>Demo details</h3>
						<p>This is a variant of the Echo Test demo: everything is exactly
						the same in term of available controls, features, and the like, with
						the substantial difference that you can select which of the available
						devices (microphones, webcams) you want to use for the media setup.</p>
						<p>The demo will start by automatically selecting some default devices,
						pretty much as the regular Echo Test demo already does. Once done,
						you'll be able to individually change the capture audio and/or video
						device: this will result in the Echo Test channel being reset and
						recreated, in order to use the device(s) you selected instead. Just
						as with the regular Echo Test demo, the <code>?simulcast=true</code>
						query string will allow you to test simulcasting as well.</p>
						<p>The demo exploits a functionality made available in the
						<code>janus.js</code> library, meaning you should be able to
						easily adapt all the other demos to follow the same approach as
						well, and that you'll be able to do the same in your Janus-based
						web application too.</p>
						<p>Press the <code>Start</code> button above to launch the demo.</p>
					</div>
				</div>
			</div>
			<div class="container mt-4">
				<div class="row mb-4 hide" id="devices">
					<div class="row">
						<div class="col-md-4">
							<div class="form-group">
								<label for="audio-device">Audio device (input):</label>
								<select id="audio-device" class="form-control"></select>
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group">
								<label for="video-device">Video device (input):</label>
								<select id="video-device" class="form-control"></select>
							</div>
						</div>
						<div class="col-md-4 mt-auto">
							<div class="form-group">
								<div id="change-devices" class="form-control btn btn-primary">Change devices</div>
							</div>
						</div>
					</div>
				</div>
				<hr/>
 				<div class="row hide" id="videos">
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Local Stream
									<div class="btn-group btn-group-sm top-right hide">
										<button class="btn btn-danger" autocomplete="off" id="toggleaudio">Disable audio</button>
										<button class="btn btn-danger" autocomplete="off" id="togglevideo">Disable video</button>
										<div class="btn-group btn-group-sm">
											<button id="bitrateset" autocomplete="off" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
												Bandwidth</span>
											</button>
											<ul id="bitrate" class="dropdown-menu" role="menu">
												<a class="dropdown-item" href="#" id="0">No limit</a>
												<a class="dropdown-item" href="#" id="128">Cap to 128kbit</a>
												<a class="dropdown-item" href="#" id="256">Cap to 256kbit</a>
												<a class="dropdown-item" href="#" id="512">Cap to 512kbit</a>
												<a class="dropdown-item" href="#" id="1024">Cap to 1mbit</a>
												<a class="dropdown-item" href="#" id="1500">Cap to 1.5mbit</a>
												<a class="dropdown-item" href="#" id="2000">Cap to 2mbit</a>
											</ul>
										</div>
									</div>
								</span>
							</div>
							<div class="card-body" id="videoleft"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-up"></i></span>
							<input type="text" class="form-control" placeholder="Write a DataChannel message" autocomplete="off" id="datasend" onkeypress="return checkEnter(event);" disabled>
						</div>
					</div>
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Remote Stream
									<span class="badge bg-primary hide" id="curres"></span>
									<span class="badge bg-info hide" id="curbitrate"></span>
									<div class="btn-group btn-group-sm bottom-right hide" id="output-devices">
										<button id="outputdeviceset" autocomplete="off" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
											Output device
										</button>
										<ul id="audiooutput" class="dropdown-menu" role="menu">
										</ul>
									</div>
								</span>
							</div>
							<div class="card-body" id="videoright"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-down"></i></span>
							<input type="text" class="form-control" id="datarecv" disabled>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>

</body>
</html>
