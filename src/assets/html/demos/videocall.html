<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): Video Call Demo</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/webrtc-adapter/8.2.3/adapter.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/6.0.0/bootbox.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<script type="text/javascript" src="settings.js" ></script>
<script type="text/javascript" src="janus.js" ></script>
<script type="text/javascript" src="videocall.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top li.dropdown").addClass("active");
			$(".fixed-top a[href='videocall.html']").addClass("active");
		});
		$(".footer").load("../footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="../css/demo.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="../forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>Plugin Demo: Video Call
					<button class="btn btn-secondary" autocomplete="off" id="start">Start</button>
				</h1>
			</div>
			<div class="container" id="details">
				<div class="row">
					<div class="alert alert-primary mt-2 mb-5">
						Want to learn more about the <strong>VideoCall</strong> plugin?
						Check the <a target="_blank" href="https://janus.conf.meetecho.com/docs/videocall">Documentation</a>.
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<h3>Demo details</h3>
						<p>This Video Call demo is basically an example of how you can achieve a
						scenario like the famous AppRTC demo but with media flowing through Janus. It
						basically is an extension to the Echo Test demo, where in this case the media
						packets and statistics are forwarded between the two involved peers.</p>
						<p>Using the demo is simple. Just choose a simple username to register
						at the plugin, and then either call another user (provided you know
						which username was picked) or share your username with a friend and
						wait for a call. At that point, you'll be in a video call with the
						remote peer, and you'll have the same controls the Echo Test demo
						provides to try and control the media: that is, a button to mute/unmute
						your audio and video, and a knob to try and limit your bandwidth. If
						the browser supports it, you'll also get a view of the bandwidth
						currently used by your peer for the video stream.</p>
						<p>If you're interested in testing how simulcasting can be used within
						the context of this sample videocall application, just pass the
						<code>?simulcast=true</code> query string to the url of this page and
						reload it. If you're using a browser that does support simulcasting
						(Chrome or Firefox) and the call will end up using VP8, you'll
						send multiple qualities of the video you're capturing. Notice that
						simulcasting will only occur if the browser thinks there is enough
						bandwidth, so you'll have to play with the Bandwidth selector to
						increase it. New buttons to play with the feature will automatically
						appear for your peer; at the same time, if your peer enabled simulcasting
						new buttons will appear for you when watching the remote stream. Notice that
						no simulcast support is needed for watching, only for publishing.</p>
						<p>A very simple chat based on Data Channels is available as well:
						just use the text area under your local video to send messages
						to your peer. Incoming messages will be displayed below the
						remote video instead.</p>
						<p>Press the <code>Start</code> button above to launch the demo.</p>
					</div>
				</div>
			</div>
			<div class="container hide" id="videocall">
				<div class="row mt-4">
					<div class="col-md-6 container invisible" id="login">
						<div class="input-group mt-3 mb-1">
							<span class="input-group-text"><i class="fa-solid fa-user"></i></span>
							<input class="form-control" type="text" placeholder="Choose a username" autocomplete="off" id="username" onkeypress="return checkEnter(this, event);" />
						</div>
						<button class="btn btn-success mb-1" autocomplete="off" id="register">Register</button> <span class="hide badge bg-info" id="youok"></span>
					</div>
					<div class="col-md-6 container invisible" id="phone">
						<div class="input-group mt-3 mb-1">
							<span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
							<input class="form-control" type="text" placeholder="Who should we call?" autocomplete="off" id="peer" onkeypress="return checkEnter(this, event);" />
						</div>
						<button class="btn btn-success mb-1" autocomplete="off" id="call">Call</button>
					</div>
				</div>
				<div id="videos" class="row mt-4 hide">
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Local Stream
									<div class="btn-group btn-group-sm top-right hide">
										<button class="btn btn-danger" autocomplete="off" id="toggleaudio">Disable audio</button>
										<button class="btn btn-danger" autocomplete="off" id="togglevideo">Disable video</button>
										<div class="btn-group btn-group-sm">
											<button autocomplete="off" id="bitrateset" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
												Bandwidth
											</button>
											<ul id="bitrate" class="dropdown-menu" role="menu">
												<a class="dropdown-item" href="#" id="0">No limit</a>
												<a class="dropdown-item" href="#" id="128">Cap to 128kbit</a>
												<a class="dropdown-item" href="#" id="256">Cap to 256kbit</a>
												<a class="dropdown-item" href="#" id="512">Cap to 512kbit</a>
												<a class="dropdown-item" href="#" id="1024">Cap to 1mbit</a>
												<a class="dropdown-item" href="#" id="1500">Cap to 1.5mbit</a>
												<a class="dropdown-item" href="#" id="2000">Cap to 2mbit</a>
											</ul>
										</div>
									</div>
								</span>
							</div>
							<div class="card-body" id="videoleft"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-up"></i></span>
							<input type="text" class="form-control" placeholder="Write a DataChannel message" autocomplete="off" id="datasend" onkeypress="return checkEnter(this, event);" disabled>
						</div>
					</div>
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Remote Stream
									<span class="badge bg-info hide" id="callee"></span>
									<span class="badge bg-primary hide" id="curres"></span>
									<span class="badge bg-info hide" id="curbitrate"></span>
								</span>
							</div>
							<div class="card-body" id="videoright"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-down"></i></span>
							<input type="text" class="form-control" id="datarecv" disabled>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>

</body>
</html>
