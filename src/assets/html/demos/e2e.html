<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Janus WebRTC Server (multistream): End-to-end Encryption Test</title>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/webrtc-adapter/8.2.3/adapter.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" ></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.2/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/6.0.0/bootbox.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<script type="text/javascript" src="settings.js" ></script>
<script type="text/javascript" src="janus.js" ></script>
<script type="text/javascript" src="e2e.js"></script>
<script>
	$(function() {
		$(".fixed-top").load("navbar.html", function() {
			$(".fixed-top li.dropdown").addClass("active");
			$(".fixed-top a[href='e2e.html']").addClass("active");
		});
		$(".footer").load("../footer.html");
	});
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootswatch/5.3.2/cerulean/bootstrap.min.css" type="text/css"/>
<link rel="stylesheet" href="../css/demo.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css"/>
</head>
<body>

<a href="https://github.com/meetecho/janus-gateway"><img style="position: absolute; top: 0; left: 0; border: 0; z-index: 2001;" src="../forkme_left_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="pb-2 mt-4 mb-2 border-bottom">
				<h1>Plugin Demo: End-to-end Encryption
					<button class="btn btn-secondary" autocomplete="off" id="start">Start</button>
				</h1>
			</div>
			<div class="container" id="details">
				<div class="row">
					<div class="alert alert-primary mt-2 mb-5">
						Want to learn more about the <strong>EchoTest</strong> plugin?
						Check the <a target="_blank" href="https://janus.conf.meetecho.com/docs/echotest">Documentation</a>.
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<h3>Demo details</h3>
						<p>This is a variant of the Echo Test demo: everything is exactly
						the same in term of available controls, features, and the like, with
						the substantial difference that it shows how you can configure an
						end-to-end encryption context using the recently introduced
						<a target="_blank" href="https://www.chromestatus.com/feature/6321945865879552">Insertable Streams</a>.</p>
						<p>Specifically, this demo will prompt for a secret, and then use the same transform functions as
						<a target="_blank" href="https://webrtc.github.io/samples/src/content/peerconnection/endtoend-encryption/">this official demo</a>
						to encrypt the media: this means Janus will NOT have access to the media, but
						will still be able to pass the packets to the EchoTest plugin and send them
						back, thus ensuring an end-to-end encryption of the content. In this context,
						the same page that encrypted the media will also decrypt it, which means
						that if you see both local and remote video, then it's working as expected.
						You'll probably want to then experiment with this feature by playing with
						other plugins as well, e.g., the VideoRoom, for E2E-encrypted conferences.</p>
						<p>Notice that at the time of writing, only video can be end-to-end encrypted,
						and not audio. More importantly, at the moment this will only work if you're
						using a recent of Chrome version that has been started either with the following flag:</p>
						<pre class="card card-body bg-gray">--enable-experimental-web-platform-features --force-fieldtrials=WebRTC-GenericDescriptorAdvertised/Enabled/</pre>
						<p>Press the <code>Start</code> button above to launch the demo.</p>
					</div>
				</div>
			</div>
			<div class="container mt-4 hide" id="videos">
				<div class="row">
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Local Stream
									<div class="btn-group btn-group-sm top-right hide">
										<button class="btn btn-danger" autocomplete="off" id="toggleaudio">Disable audio</button>
										<button class="btn btn-danger" autocomplete="off" id="togglevideo">Disable video</button>
										<div class="btn-group btn-group-sm">
											<button id="bitrateset" autocomplete="off" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
												Bandwidth
											</button>
											<ul id="bitrate" class="dropdown-menu" role="menu">
												<a class="dropdown-item" href="#" id="0">No limit</a>
												<a class="dropdown-item" href="#" id="128">Cap to 128kbit</a>
												<a class="dropdown-item" href="#" id="256">Cap to 256kbit</a>
												<a class="dropdown-item" href="#" id="512">Cap to 512kbit</a>
												<a class="dropdown-item" href="#" id="1024">Cap to 1mbit</a>
												<a class="dropdown-item" href="#" id="1500">Cap to 1.5mbit</a>
												<a class="dropdown-item" href="#" id="2000">Cap to 2mbit</a>
											</ul>
										</div>
									</div>
								</span>
							</div>
							<div class="card-body" id="videoleft"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-up"></i></span>
							<input type="text" class="form-control" placeholder="Write a DataChannel message" autocomplete="off" id="datasend" onkeypress="return checkEnter(event);" disabled>
						</div>
					</div>
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<span class="card-title">Remote Stream <span class="badge bg-primary hide" id="curres"></span> <span class="badge bg-info hide" id="curbitrate"></span></span>
							</div>
							<div class="card-body" id="videoright"></div>
						</div>
						<div class="input-group mt-3 mb-3">
							<span class="input-group-text"><i class="fa-solid fa-cloud-arrow-down"></i></span>
							<input type="text" class="form-control" id="datarecv" disabled>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<hr>
	<div class="footer">
	</div>
</div>

</body>
</html>
