export const environment = {
  production: false,
  key: 'app_pbx',
  uat: true,
  logger: true,
  skipAuth: false,
  origin: 'http://localhost:4200', // need for micro frontend
  base_path: 'app_pbx',
  base_url: 'http://localhost:4200',
  hostApi: 'https://pbx-dev.mbbank.com.vn',
  webportalApi: 'https://webportal-uat.mbbank.com.vn/api',
  urlConfigBuilder: 'http://localhost:3000',
  keepStateUrlLogin: false,
  appRootElement: 'app_pbx-app-element',
  services: {
    pbxCms: 'pbx-cms',
    pbxSbc: 'pbx-sbc',
    jobs: 'pbx-jobs',
    dcmsCoreAdmin: 'dcmcore-administration',
    portalCategory: 'portal-category',
    portalFiles: 'portal-files',
    portalAdmin: 'portal-admin',
    dcmsCoreCampaign: 'dcmcore-campaign',
    dcmsCoreCase: 'dcmcore-cases'
  },
  keycloak: {
    issuer: 'https://identity.pronexus.vn',
    realm: 'pronexus_dev',
    client: 'pronexus_dev_frontend',
    relationShip: 'pbx-service',
    realmManagement: 'realm-management'
  },
  keycloakId: 'pronexus_dev_frontend',
  timeOut: 60000 * 4
}
