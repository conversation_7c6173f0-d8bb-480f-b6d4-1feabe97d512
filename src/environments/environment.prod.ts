export const environment = {
  production: true,
  key: 'app_pbx',
  uat: false,
  logger: false,
  skipAuth: false,
  origin: 'https://pbx.mbbank.com.vn', // need for micro frontend
  base_path: 'app_pbx',
  base_url: 'https://pbx.mbbank.com.vn',
  hostApi: 'https://pbx.mbbank.com.vn',
  webportalApi: 'https://webportal.mbbank.com.vn/api',
  urlConfigBuilder: 'http://localhost:3000',
  keepStateUrlLogin: false,
  appRootElement: 'app_pbx-app-element',
  janusServerUrl: 'https://mbreport-uat.mbbank.com.vn/call/janus', // Update this with your actual Janus server URL
  services: {
    pbxCms: 'pbx-cms',
    pbxSbc: 'pbx-sbc',
    jobs: 'pbx-jobs',
    dcmsCoreAdmin: 'dcmcore-administration',
    portalCategory: 'portal-category',
    portalFiles: 'portal-files',
    portalAdmin: 'portal-admin',
    dcmsCoreCampaign: 'dcmcore-campaign',
    dcmsCoreCase: 'dcmcore-cases',
  },
  keycloak: {
    issuer: 'https://keycloak-internal.mbbank.com.vn/auth/',
    realm: 'internal',
    client: 'pbx-frontend',
    relationShip: 'pbx-service',
    realmManagement: 'realm-management'
  },
  keycloakId: '********-fee1-49dd-b9c0-99b88ea3b8c1',
  timeOut: 60000 * 4
}
