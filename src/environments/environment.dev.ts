export const environment = {
  production: false,
  key: 'app_pbx',
  uat: true,
  logger: true,
  skipAuth: false,
  origin: 'https://pbx-dev.mbbank.com.vn', // need for micro frontend
  base_path: 'app_pbx',
  base_url: 'https://pbx-dev.mbbank.com.vn',
  hostApi: 'https://pbx-dev.mbbank.com.vn',
  webportalApi: 'https://webportal-uat.mbbank.com.vn/api',
  urlConfigBuilder: 'http://localhost:3000',
  keepStateUrlLogin: false,
  appRootElement: 'app_pbx-app-element',
  janusServerUrl: 'https://pbx-dev.mbbank.com.vn/pbx-cms/v1/janus', // Update this with your actual Janus server URL
  services: {
    pbxCms: 'pbx-cms',
    pbxSbc: 'pbx-sbc',
    jobs: 'pbx-jobs',
    dcmsCoreAdmin: 'dcmcore-administration',
    portalCategory: 'portal-category',
    portalFiles: 'portal-files',
    portalAdmin: 'portal-admin',
    dcmsCoreCampaign: 'dcmcore-campaign',
    dcmsCoreCase: 'dcmcore-cases',
  },
  keycloak: {
    issuer: 'https://keycloak-internal-uat.mbbank.com.vn/auth/',
    realm: 'internal',
    client: 'pbx-frontend',
    relationShip: 'pbx-service',
    realmManagement: 'realm-management'
  },
  keycloakId: 'ea270582-6953-4627-9676-cb2b07f0a4b1',
  timeOut: 60000 * 4
}
