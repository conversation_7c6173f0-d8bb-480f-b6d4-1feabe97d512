/* You can add global styles to this file, and also import other style files */

// Import theme mới từ Material Dashboard
@import './assets/scss/style.scss';

.ui-builder {
  /* bootstrap's utilities only */
  @import "../node_modules/bootstrap/dist/css/bootstrap-grid.css";
  @import '../projects/dynamic-builder-ui/styles/ui-builder';
}

// Loại bỏ theme mặc định của Angular Material vì đã được định nghĩa trong style.scss
// @import '@angular/material/prebuilt-themes/indigo-pink.css';

@import '@ng-select/ng-select/themes/default.theme.css';

@import 'styles-variables';
@import './assets/fonts/material_icons';
@import './assets/fonts/fonts';
@import './assets/scss/mb/index';

* button {
  cursor: pointer;
}
form .btn {
  min-height: 40px;
  border-radius: 0.5rem;
}

.btn-back-page {
  height: 30px;
  width: 30px;
}

.search-box .form-group {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

::ng-deep .mat-mdc-option {
  font-size: 14px !important;
}
.mat-mdc-option.mdc-list-item {
  align-items: center;
  font-size: 14px !important;
}

.truncate {
  white-space: nowrap;
  width: 95%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.unset-overflow {
  overflow: unset !important;
}

.multiline-tooltip {
  white-space: pre-wrap !important;
}
.sticky-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px;
}

.content-dialog-over {
  overflow: auto;
}
.dialog-fixed-bottom {
  width: 100%;
  position: absolute !important;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: #fff;
  padding: 20px !important;
}
.text-right {
  text-align: right;
}

.mat-expansion-panel-body {
  padding: 0.5rem !important;
}

.theme-override {
  /* Placeholder cho các styles ghi đè */
  color: inherit;
}
