declare var Janus: any;

interface JanusConfig {
    server: string;
    success: () => void;
    error: (error: any) => void;
}

interface JanusPluginConfig {
    plugin: string;
    opaqueId: string;
    success: (pluginHandle: any) => void;
    error: (error: any) => void;
}

interface JanusPluginHandle {
    send: (config: { message: any; success?: () => void; error?: (error: any) => void; }) => void;
}
