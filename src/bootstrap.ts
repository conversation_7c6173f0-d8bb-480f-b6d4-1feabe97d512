import { createApplication } from '@angular/platform-browser'
import { appConfig } from './app/app.config'
import { AppComponent } from './app/app.component'
import { createCustomElement } from '@angular/elements'
import { environment } from '@env/environment'

;(async () => {
  const app = await createApplication(appConfig)

  const customElement = createCustomElement(AppComponent, {
    injector: app.injector
  })

  customElements.define(environment.appRootElement, customElement)
})()
