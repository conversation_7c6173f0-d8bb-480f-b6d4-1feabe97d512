import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { getSelectOptions, HttpClientService, HttpOptions, Verbs } from '@shared'
import { removeNullUndefined } from '../shared/utils/object.utils'
import { NgxIndexedDBService } from 'ngx-indexed-db'

export const PATH = {
  GET_LIST_USERS: `${environment.services.pbxCms}/mhr-user/search`,
  DOWNLOAD_FILE: `${environment.services.portalFiles}/common-file/download`,

  SEARCH_TITLE: `${environment.services.pbxCms}/title/search`,
  CONFIG_BUILDER: `${environment.services.pbxCms}/v1/config-builder`,
  GET_MENU: `${environment.services.portalCategory}/users/get-menu`,
  GET_USER_INFO: `${environment.services.pbxCms}/v1/user/info`
}

@Injectable({
  providedIn: 'root'
})
export class AppService {
  constructor(
    private httpClient: HttpClientService,
    private http: HttpClient,
    private dbService: NgxIndexedDBService
  ) {}

  /**
   * lấy thông tin + quyền user đang đăng nhập
   * @param id
   */
  getUserInfo() {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH.GET_USER_INFO
    }

    return this.httpClient.get(options)
  }

  /**
   * lấy danh sách menu được phân quyền của user
   */
  getMenu() {
    const options: HttpOptions = {
      url: environment.webportalApi,
      path: PATH.GET_MENU,
      headers: {
        keycloakId: environment.keycloakId
      }
    }
    return this.httpClient.get(options)
  }

  /*
   * Get list config category common
   */
  getListCommonCategory(codes: any) {
    const options: HttpOptions = {
      url: environment.webportalApi,
      path: `${environment.services.portalCategory}/common-categories/find-by-codes`,
      body: {
        codes: codes
      }
    }

    return this.httpClient.post(options)
  }

  downloadPdf(fileId, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH.DOWNLOAD_FILE}/${fileId}`,
      params: params
    }
    return this.httpClient.download(Verbs.GET, options)
  }

  download(url, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${url}`,
      params: removeNullUndefined(params)
    }
    return this.httpClient.download(Verbs.GET, options)
  }

  searchTitle = (params) => {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH.SEARCH_TITLE}`,
      params: params
    }
    return new Promise((resolve, reject) => {
      this.httpClient.get(options).subscribe(
        (res: any) => {
          if (res?.base?.code === 0) {
            if (res?.content && res?.content?.content) {
              resolve(getSelectOptions(res.data?.content, 'jobId', ['jobName']))
            } else {
              reject(res)
            }
          } else {
            reject(res)
          }
        },
        (err) => {
          reject(err)
        }
      )
    })
  }

  createConfigBuilder(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH.CONFIG_BUILDER,
      body
    }
    return this.httpClient.post(options)
  }

  getConfigBuilder(componentName: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH.CONFIG_BUILDER}/${componentName}?nocache=${new Date().getTime()}`,
      params: {}
    }
    return this.httpClient.get(options)
  }

  getListConfigBuilder(page, size, filter) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH.CONFIG_BUILDER}`,
      params: {
        page: page,
        size: size,
        ...filter
      }
    }
    return this.httpClient.get(options)
  }

  /**
   * thao tac voi indexdb save local config
   * @param data
   */
  addOrUpdateComponentConfig(data) {
    // Kiểm tra xem bản ghi với id đã tồn tại hay chưa
    this.dbService.getByID('componentConfigs', data.id).subscribe({
      next: (existingRecord) => {
        if (existingRecord) {
          // Nếu bản ghi đã tồn tại, thực hiện cập nhật
          this.dbService.update('componentConfigs', data).subscribe({
            next: () => {
              console.log('Record updated successfully')
            },
            error: (error) => {
              console.error('Error updating record:', error)
            }
          })
        } else {
          // Nếu bản ghi chưa tồn tại, thêm mới
          this.dbService.add('componentConfigs', data).subscribe({
            next: (key) => {
              console.log('Record added successfully with key:', key)
            },
            error: (error) => {
              console.error('Error adding record:', error)
            }
          })
        }
      },
      error: (error) => {
        console.error('Error checking record existence:', error)
      }
    })
  }

  addOrUpdateRoutesConfig(routerName: string, newKey: string) {
    // Sử dụng getAll để tìm kiếm bản ghi theo routerName
    this.dbService.getAll('routerConfigs').subscribe({
      next: (records) => {
        // @ts-ignore
        const existingRecord = records.find((record) => record.routerName === routerName)
        if (existingRecord) {
          // Nếu bản ghi đã tồn tại, cập nhật mảng values
          // @ts-ignore
          const updatedValues = this.updateValues(existingRecord.values, newKey)
          // @ts-ignore
          const updatedRecord = { ...existingRecord, values: updatedValues }

          this.dbService.update('routerConfigs', updatedRecord).subscribe({
            next: () => {
              console.log('Record updated successfully')
            },
            error: (error) => {
              console.error('Error updating record:', error)
            }
          })
        } else {
          // Nếu bản ghi chưa tồn tại, tạo mới
          const newRecord = {
            routerName: routerName,
            values: [newKey]
          }

          this.dbService.add('routerConfigs', newRecord).subscribe({
            next: (key) => {
              console.log('Record added successfully with key:', key)
            },
            error: (error) => {
              console.error('Error adding record:', error)
            }
          })
        }
      },
      error: (error) => {
        console.error('Error checking record existence:', error)
      }
    })
  }

  updateValues(existingValues: string[], newKey: string): string[] {
    // Kiểm tra xem key đã tồn tại trong mảng chưa
    if (existingValues.includes(newKey)) {
      return existingValues // Không cần cập nhật nếu key đã tồn tại
    } else {
      return [...existingValues, newKey] // Thêm key mới vào mảng
    }
  }
}
