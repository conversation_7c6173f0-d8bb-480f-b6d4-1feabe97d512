import { TestBed } from '@angular/core/testing'
import { AppService } from './app.service'
import { HttpClientModule } from '@angular/common/http'

describe('Kiểm tra đăng nhập tài khoản', () => {
  let service: AppService
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientModule],
      providers: [AppService]
    })
    service = TestBed.inject(AppService)
  })

  afterEach(function () {
    localStorage.setItem('TOKEN_TEST', '')
  })

  describe('Kiểm tra tài khoản đã được gán quyền chưa?', () => {
    let service: AppService
    beforeEach(() => {
      TestBed.configureTestingModule({
        imports: [HttpClientModule],
        providers: [AppService]
      })
      service = TestBed.inject(AppService)
    })

    afterEach(function () {
      localStorage.setItem('TOKEN_TEST', '')
    })
  })
})
