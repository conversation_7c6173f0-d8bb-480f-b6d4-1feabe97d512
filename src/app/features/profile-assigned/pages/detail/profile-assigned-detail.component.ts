import { Component, Injector, ViewChild } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { DonutChartComponent } from '@features/campaign-voice-blaster/shared/pie-chart/donut-chart.component'
import { ProfileAssignedService } from '@features/profile-assigned/services/profile-assigned.service'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { NgForOf, NgIf } from '@angular/common'
import { MatIcon } from '@angular/material/icon'
import { DateRangeControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import { AppTableTreeComponent } from '@shared/components/data-display'
import { RatingControlComponent } from '@shared/components/element/app-row-detail/rating/rating-control.component'

@Component({
  selector: 'app-profile-assigned-detail',
  standalone: true,
  imports: [
    DonutChartComponent,
    AppTableBuilderComponent,
    NgIf,
    MatIcon,
    SelectControlComponent,
    TreeSelectControlComponent,
    TextControlComponent,
    DateRangeControlComponent,
    AppTableTreeComponent,
    RatingControlComponent,
    NgForOf
  ],
  templateUrl: './profile-assigned-detail.component.html',
  styleUrl: './profile-assigned-detail.component.scss'
})
export class ProfileAssignedDetailComponent extends ComponentAbstract {
  @ViewChild('chart') chart: DonutChartComponent
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent
  campaignId = ''
  campaignData: any

  constructor(
    protected override injector: Injector,
    private service: ProfileAssignedService
  ) {
    super(injector)
    this.initBuilderUIConfig('management-profile-in-campaign').then((r) => {
      this.tableBuilderComponent.panelOpenState = true
      this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true
      this.campaignId = this.route.snapshot.paramMap.get('id')

      setTimeout(() => {
        //TODO: Khi nao merge thì sửa config builder thành disableAutoCallOnChange
        this.setDefaultFormData()
      }, 800)
    })
  }

  setDefaultFormData() {
    this.tableBuilderComponent._appTableBodyComponent.configComponent.data.apiList = `dcmcore-campaign/v1/profile/assigned?campaignId=${this.campaignId}`
    this.tableBuilderComponent.searchAdvanced(0)
  }
  componentInit(): void {}

  initChartPanel($event) {
    if ($event.campaignInfo) {
      this.campaignData = $event.campaignInfo
      if (this.chart) {
        this.chart.chartData1 = [
          {
            label: 'Số lượng HS đã tương tác',
            color: '#45d39d',
            value: $event.campaignInfo?.interactedProfiles
          },
          {
            label: 'Số lượng HS chưa tương tác',
            color: '#FF7B2C',
            value: $event.campaignInfo?.notInteractedProfiles
          }
        ]
        this.chart.createChart()
      }
    }
  }
  onButtonClick($event: any) {}

  onTableActionClick($event: any) {}
}
