import { DateRangeItem, NgSelectItem, TextboxItem, TreeDropdownItem } from '@shared'
import dayjs from 'dayjs/esm'

export const txtCustomerCode = () =>
  new TextboxItem({
    key: 'customerCode',
    label: 'Mã KH',
    placeholder: `<PERSON>hập mã KH`,
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 60
  })

export const txtPhoneNumber = () =>
  new TextboxItem({
    key: 'phoneNumber',
    label: 'SĐT',
    placeholder: `Nhập SĐT`,
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 10,
  })

export const txtLastResultInteract = () =>
  new TreeDropdownItem({
    countMaxLength: true,
    value: "",
    key: "lastInteractionResult",
    label: "Kết quả tương tác gần nhất",
    required: false,
    maxLength: 200,
    order: 1,
    controlType: "treeselect",
    type: "single",
    placeholder: "<PERSON><PERSON>n kết quả tương tác gần nhất",
    placeHolderSearch: "",
    focus: false,
    options: [],
    layout: "100",
    directives: "",
    customValidate: ["customMessageError"],
    updateOn: "",
    template: "",
    reset: false,
    paramData: {
      isTag: false,
      url: "",
      preLoad: true,
      key: "",
      typeheadKey: ""
    },
    title: false,
    pattern: "",
    checked: false,
    readOnly: false,
    minRow: "2",
    clearable: false,
    checkBoxKey: "value",
    upperCase: false,
    requiredMessage: "",
    tooltipMessage: "",
  })
export const txtLastCallDate = () =>
  new DateRangeItem({
    countMaxLength: true,
    value: {
      start: dayjs().utc().startOf('day').subtract(30, 'day'),
      end: dayjs().utc().endOf('day')
    },
    key: "lastCallDate",
    label: "Thời gian gọi gần nhất",
    required: false,
    maxLength: 200,
    order: 1,
    controlType: "daterange",
    type: "single",
    placeholder: "Thời gian gọi gần nhất",
    placeHolderSearch: "",
    focus: false,
    options: [],
    layout: "100",
    directives: "",
    customValidate: ["customMessageError"],
    updateOn: "",
    template: "",
    reset: false,
    paramData: {
      isTag: false,
      url: "",
      preLoad: true,
      key: "",
      typeheadKey: ""
    },
    title: false,
    pattern: "",
    checked: false,
    readOnly: false,
    minRow: "2",
    clearable: false,
    checkBoxKey: "value",
    upperCase: false,
    requiredMessage: "",
    tooltipMessage: "",
    displayFormat: "DD/MM/YYYY"
  })

export const txtInteractTime = () =>
  new NgSelectItem({
    countMaxLength: true,
    value: "",
    key: "totalInteraction",
    label: "Số lần tương tác",
    required: false,
    maxLength: 200,
    order: 1,
    controlType: "ngselect",
    type: "single",
    placeholder: "Chọn số lần tương tác",
    placeHolderSearch: "",
    focus: false,
    options: [
      { key: "0", value: "0" },
      { key: "1", value: "1" },
      { key: "2", value: "2" },
      { key: "3", value: "3" },
      { key: "4", value: "4" },
      { key: "5", value: "5" },
      { key: "gt:5", value: ">5" }
    ],
    layout: "100",
    directives: "",
    customValidate: ["customMessageError"],
    updateOn: "",
    template: "",
    reset: false,
    paramData: {
      isTag: false,
      url: "",
      preLoad: true,
      key: "",
      typeheadKey: ""
    },
    title: false,
    pattern: "",
    checked: false,
    readOnly: false,
    minRow: "2",
    clearable: false,
    checkBoxKey: "value",
    upperCase: false,
    requiredMessage: "",
    tooltipMessage: "",
  })

export const tableProfileInCampaign = {
  type: "table",
  id: "table-1",
  data: {
    quickSearchFields: [
      {
        key: "formName",
        text: "Tên mẫu"
      },
      {
        key: "formCode",
        text: "Mã mẫu"
      }
    ],
    apiList: "dcmcore-campaign/v1/profile/assigned",
    tableTitle: "mẫu nhập liệu",
    apiCreate: "",
    displayedColumns: [
      {
        name: "STT",
        field: "no",
        path: "no",
        show: true
      },
      {
        name: "Tên KH",
        field: "customerName",
        path: "customerName",
        show: true,
        type: "textbox"
      },
      {
        name: "SĐT",
        field: "customerPhoneNumber",
        path: "customerPhoneNumber",
        show: true,
        type: "textbox",
        isQuickSearch: true,
        isAdvancedSearch: true,
        maxLength: "60"
      },
      {
        name: "Ngày phân giao",
        field: "assignedDate",
        path: "assignedDate",
        show: true,
        type: "textbox"
      },
      {
        name: "Số lần gọi",
        field: "totalInteract",
        path: "totalInteract",
        show: true,
        type: "textbox",
        enumText: [
          {
            key: "0",
            text: "Ngừng sử dụng",
            class: "app-status-reject"
          },
          {
            key: "1",
            text: "Đang sử dụng",
            class: "app-status-approved"
          }
        ]
      },
      {
        name: "Thời gian gọi gần nhất",
        field: "lastCallDate",
        path: "lastCallDate",
        show: true,
        type: "textbox"
      },
      {
        name: "Kết quả tương tác gần nhất",
        field: "employeeInChargeTeams",
        path: "employeeInChargeTeams",
        show: true,
        type: "mapAll",
        maxLength: "36",
        mapField: "teamName"
      },
      {
        name: "Hành động",
        field: "action",
        path: "",
        show: true
      }
    ],
    pageSize: '10',
    buttonLists: [
      {
        title: "Sửa",
        type: "EDIT",
        class: "",
        icon: "ic-edit fc-primary",
        navigationType: "emit"
      }
    ],
    columnActionLists: [
      {
        title: "Tạo tương tác",
        type: "CREATE",
        class: "",
        icon: "ic-plus",
        routerName: "/campaign/profile-assigned/profile-in-campaign/create/:profileContentId",
        scope: "02_EDIT",
        navigationType: "nav"
      }
    ],
    apiDelete: "dcmcore-campaign/v1/profile/assigned",
    isTreeData: false,
    disableAutoCallOnChange: true
  },
  classes: ""
}
