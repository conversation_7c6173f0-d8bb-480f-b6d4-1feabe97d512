<app-table-builder #tableBuilderComponent (onButtonClick)="onButtonClick($event)" [hideBreadCrumb]="true" (onTableActionClick)="onTableActionClick($event)" (responseSearchChanged)="initChartPanel($event)">
  <ng-container top-screen>
    <div class="panel chart-panel" style='margin-bottom: 20px'>
      <div class="panel-body">
        <div class="d-flex flex-center-row align-items-center justify-content-between pdl-8 pdr-8 pdb-7 pdt-7" style='background-color: #EDF0FE'>
          <div>
            <app-rating-control-detail [disabled]="true" [rating]="campaignData?.priorityLevel"></app-rating-control-detail>
            <div size="12px">Tên Campaign</div>
            <div class="title-page-detail">{{ campaignData?.campaignName }}</div>
            <div size="12px">Mã Campaign</div>
            <div class="title-page-detail">{{ campaignData?.campaignCode }}</div>
          </div>
          <app-donut-chart #chart></app-donut-chart>
        </div>
      </div>
    </div>
  </ng-container>
</app-table-builder>
