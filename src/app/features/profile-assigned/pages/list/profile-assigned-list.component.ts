import { Component, Injector } from '@angular/core'
import { AppReportTableBuilderComponent } from '@features/report/pages/app-report-table-builder/report-table-builder.component'
import { ComponentAbstract } from '@shared'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { ROUTES_NAME_CAMPAIGN } from '../../../../app.routes'

@Component({
  selector: 'app-profile-assigned-list',
  standalone: true,
  imports: [AppReportTableBuilderComponent, AppTableBuilderComponent],
  templateUrl: './profile-assigned-list.component.html',
  styleUrl: './profile-assigned-list.component.scss'
})
export class ProfileAssignedListComponent extends ComponentAbstract {
  constructor(protected override injector: Injector) {
    super(injector)
    this.initBuilderUIConfig('management-profile-assigned').then((r) => {})
    // this.initBuilderUIConfigLocal('management-product-list').then((r) => {
    //   this.config = r
    // })
  }
  protected componentInit(): void {}

  onButtonClick($event: any) {}

  onTableActionClick($event: any) {
    console.log('vao day', $event)
    const id = $event?.row?.campaignId
    const event = String($event?.type || '').toUpperCase()
    if (event === 'CELLCLICK') {
      this.goTo(`${ROUTES_NAME_CAMPAIGN.PROFILE_IN_CAMPAIGN}/${id}`)
    }
  }
  actionContextMenuClick($event) {
    const id = $event?.row?.campaignId
    switch ($event.type) {
      case 0: {
        this.openRouterNewTab(`${ROUTES_NAME_CAMPAIGN.PROFILE_IN_CAMPAIGN}/${id}`)
        break
      }
      default:
        break
    }
  }
}
