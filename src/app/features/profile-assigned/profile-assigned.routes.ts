import { Routes } from '@angular/router'
import { RESOURCE_CODE } from '@shared'

export const routes: Routes = [
  {
    path: '',
    title: '<PERSON><PERSON> sơ được phân giao',
    loadComponent: async () => (await import('./pages/list/profile-assigned-list.component')).ProfileAssignedListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
    // canActivate: [RoleGuard],
  },
  {
    path: 'profile-in-campaign/:id',
    title: '<PERSON><PERSON> sách hồ sơ trong Campaign',
    loadComponent: async () => (await import('./pages/detail/profile-assigned-detail.component')).ProfileAssignedDetailComponent,
    data: {
      breadcrumb: '<PERSON>h sách hồ sơ trong Campaign',
      resources: [RESOURCE_CODE.PROFILE_IN_CAMPAIGN]
    }
  },
  {
    path: 'profile-in-campaign',
    title: 'Tạo tương tác',
    loadChildren: async () => [
      {
        path: 'create/:id',
        title: 'T<PERSON><PERSON> tương tác',
        loadComponent: async () => (await import('../request/pages/create/interact-create.component')).InteractCreateComponent,
        data: {
          showBreadcrumb: false,
          breadcrumb: 'Tạo tương tác'
        }
      }
    ],
    data: {
      breadcrumb: 'Danh sách hồ sơ trong Campaign'
    }
  }
]
