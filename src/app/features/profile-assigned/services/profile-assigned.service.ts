import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { PATH_API } from '../constants'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'

@Injectable({
  providedIn: 'root'
})
export class ProfileAssignedService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * tạo
   * @param campaignId
   * @param params
   */
  getDetail(campaignId: string, params?: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.DETAIL,
      params: {
        ...params,
        campaignId: campaignId
      }
    }
    return this.httpClient.get(options)
  }
}
