import { Component, Injector, isDevMode } from '@angular/core'
import { ComponentAbstract, Status } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { DecimalPipe, NgForOf, NgIf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { ROUTES_NAME_SERVICE } from '../../../../app.routes'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>n<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er<PERSON><PERSON>,
  Mat<PERSON>ooter<PERSON>owDef,
  MatHeader<PERSON>ell,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable
} from '@angular/material/table'
import { MatTooltip } from '@angular/material/tooltip'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { ResizeHandleDirective } from '../../../../shared/directives/resize-handle.directive'
import { ToggleColumnsPipe } from '../../../../shared/pipe'
import { BreadcrumbComponent } from '@shared/components/navigation/breadcrumb/breadcrumb.component'
import { MatFormField } from '@angular/material/form-field'
import { MatSelect } from '@angular/material/select'
import { finalize, takeUntil } from 'rxjs'
import { AddressOverviewService } from '@features/address-overview/services/address-overview.service'
import { SubscriberChartComponent } from '@features/dashboard/subscriber-chart/subscriber-chart.component'
import { LockedPhoneNumberChartReportComponent } from '@features/address-overview/pages/locked-phone-number-chart-report/locked-phone-number-chart-report.component'
import { CallReportComponent } from '@features/dashboard/call-report/call-report.component'
import { CallInstanceData } from '@features/address-overview/pages/locked-phone-number-chart-report/locked-phone-number-chart-report.model'

@Component({
  selector: 'address-overview',
  templateUrl: './address-overview-list.component.html',
  styleUrls: ['./address-overview-list.component.scss'],
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,

    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent,
    DecimalPipe,
    MatCell,
    MatCellDef,
    MatColumnDef,
    MatFooterCell,
    MatFooterRow,
    MatFooterRowDef,
    MatHeaderCell,
    MatHeaderRow,
    MatHeaderRowDef,
    MatRow,
    MatRowDef,
    MatTable,
    MatTooltip,
    NgIf,
    NoDataComponent,
    ResizeHandleDirective,
    ToggleColumnsPipe,
    BreadcrumbComponent,
    MatFormField,
    MatSelect,
    SubscriberChartComponent,
    LockedPhoneNumberChartReportComponent,
    CallReportComponent
  ]
})
export class AddressOverviewListComponent extends ComponentAbstract {
  telcoAddressOverviewData: any[] = []
  filterSelect = 'week'
  telcoPhoneNumberOverviewData: CallInstanceData
  telcoPhoneNumberDetails: any[]
  lockCode: { [key: string]: { description: string } } = {
    '100': { description: 'Đầu số khóa tạm thời (thử nghiệm)' },
    '900': { description: 'Đầu số được khóa chủ động' },
    '403': { description: 'Đầu số không nằm trong whitelist' },
    '404': { description: 'Đầu số không tồn tại hoặc không đăng ký' },
    '480': { description: 'Đầu số đang tắt hoặc mất kết nối' }
  }

  constructor(
    protected override injector: Injector,
    private _addressOverviewService: AddressOverviewService
  ) {
    super(injector)
    // this.initBuilderUIConfig('pbx_management-address-overview').then((r) => {})
    // this.initBuilderUIConfigLocal('management-product-list').then((r) => {
    //   this.config = r
    // })
  }

  componentInit(): void {
    this.getDataTelcoAddressOverview()
    this.getDataTelcoPhoneNumberOverview()
    this.getDataTelcoPhoneNumberDetail()
  }

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    if ($event.type === 'copy') {
      this.goTo(ROUTES_NAME_SERVICE.CREATE, { action: $event.type, id: $event?.row?.serviceCode })
    }
  }

  onButtonClick($event: any) {}

  getDataTelcoAddressOverview() {
    this.indicator.showActivityIndicator(true)
    this._addressOverviewService
      .getTelcoAddressOverview()
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS && res?.data) {
            const { data } = res
            this.telcoAddressOverviewData = data
          }
        },
        () => {}
      )
  }

  getDataTelcoPhoneNumberOverview() {
    this.indicator.showActivityIndicator(true)
    this._addressOverviewService
      .getTelcoPhoneNumberOverview()
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS && res?.data) {
            const { data } = res
            this.telcoPhoneNumberOverviewData = data
          }
        },
        () => {}
      )
  }

  getDataTelcoPhoneNumberDetail() {
    this.indicator.showActivityIndicator(true)
    this._addressOverviewService
      .getTelcoPhoneNumberDetail(this.filterSelect)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS && res?.data) {
            const { data } = res
            this.telcoPhoneNumberDetails = data.reasons
          }
        },
        () => {}
      )
  }

  formatLockedCode(val: string): string {
    return this.lockCode[val].description || val
  }
}
