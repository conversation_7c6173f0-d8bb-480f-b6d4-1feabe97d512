<!--<app-table-form-search></app-table-form-search>-->

<div class="d-flex justify-content-between mt-2 mb-2">
  <app-breadcrumb [forceShow]="true"></app-breadcrumb>
</div>

<div class="d-flex flex flex-wrap justify-content-between gap-16 mrb-4">
  <locked-phone-number-chart-report *ngIf="telcoPhoneNumberOverviewData"
                                    [callInstanceData]="telcoPhoneNumberOverviewData"
                                    title="Biểu đồ theo dõi tình trạng kho đầu số"></locked-phone-number-chart-report>
</div>
<div>
  <h2>Thống kê theo nguyên nhân</h2>
  <table class='mat-mdc-table mdc-data-table__table cdk-table mat-sort app-table table-info w-full ng-animate-disabled'>
    <thead class="ng-star-inserted">
    <tr class="mat-mdc-header-row mdc-data-table__header-row cdk-header-row header-cell ng-star-inserted">
      <th><PERSON><PERSON> lỗi</th>
      <th><PERSON><PERSON> tả</th>
      <th><PERSON><PERSON> l<PERSON> (dvt: <PERSON><PERSON><PERSON>t)</th>
    </tr>
    </thead>
    <tbody class="mdc-data-table__content ng-star-inserted">
    <tr *ngFor="let item of telcoPhoneNumberDetails" class="mat-mdc-row mdc-data-table__row cdk-row ng-star-inserted">
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item.lockCode }}</td>
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ formatLockedCode(item.lockCode) }}</td>
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item.totalLockedPhoneNumber }}</td>
    </tr>
    </tbody>
  </table>
  <br/>
  <h2>Thống kê theo nhà mạng</h2>
  <table class='mat-mdc-table mdc-data-table__table cdk-table mat-sort app-table table-info w-full ng-animate-disabled'>
    <thead class="ng-star-inserted">
    <tr class="mat-mdc-header-row mdc-data-table__header-row cdk-header-row header-cell ng-star-inserted">
      <th
        class="mat-mdc-header-cell mdc-data-table__header-cell cdk-header-cell cdk-column-campaignName ng-star-inserted">
        Nhà mạng
      </th>
      <th
        class="mat-mdc-header-cell mdc-data-table__header-cell cdk-header-cell cdk-column-campaignName ng-star-inserted">
        Tổng số đầu số
      </th>
      <th
        class="mat-mdc-header-cell mdc-data-table__header-cell cdk-header-cell cdk-column-campaignName ng-star-inserted">
        Số lượng đầu số đang hoạt động
      </th>
      <th
        class="mat-mdc-header-cell mdc-data-table__header-cell cdk-header-cell cdk-column-campaignName ng-star-inserted">
        Số lượng đầu số đang bị khóa
      </th>
    </tr>
    </thead>
    <tbody class="mdc-data-table__content ng-star-inserted">
    <tr *ngFor="let item of telcoAddressOverviewData" class="mat-mdc-row mdc-data-table__row cdk-row ng-star-inserted">
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item?.telcoGroup }}</td>
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item?.totalPhoneNumber }}</td>
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item?.totalActivePhoneNumber }}</td>
      <td
        class="mat-mdc-cell mdc-data-table__cell cdk-cell cdk-column-campaignName ng-star-inserted">{{ item?.totalLockedPhoneNumber }}</td>
    </tr>
    </tbody>
  </table>
</div>

