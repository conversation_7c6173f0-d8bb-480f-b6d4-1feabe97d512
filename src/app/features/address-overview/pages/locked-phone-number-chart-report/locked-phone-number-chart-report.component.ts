import { Component, Input, OnChanges, SimpleChanges } from '@angular/core'
import { NgApexchartsModule } from 'ng-apexcharts'
import { CallInstanceData } from '@features/address-overview/pages/locked-phone-number-chart-report/locked-phone-number-chart-report.model'
import { ApexOptions } from 'ng-apexcharts/lib/model/apex-types'

@Component({
  selector: 'locked-phone-number-chart-report',
  templateUrl: './locked-phone-number-chart-report.component.html',
  standalone: true,
  imports: [NgApexchartsModule],
  styleUrls: ['./locked-phone-number-chart-report.component.scss']
})
export class LockedPhoneNumberChartReportComponent implements OnChanges {
  @Input() title = ''
  @Input() callInstanceData!: CallInstanceData
  chartOptions!: ApexOptions

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['callInstanceData'] && this.callInstanceData) {
      this.updateChart()
    }
  }

  constructor() {}

  updateChart(): void {
    const totalLockedPhoneNumber = this.callInstanceData.totalLockedPhoneNumber
    const totalPhoneNumber = this.callInstanceData.totalPhoneNumber

    this.chartOptions = {
      chart: {
        width: 400,
        type: 'bar',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '40%',
          borderRadius: 5,
          dataLabels: {
            position: 'top'
          },
          distributed: true
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val + ''
        },
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#304758']
        }
      },
      series: [
        {
          name: '',
          data: [totalPhoneNumber, totalLockedPhoneNumber]
        }
      ],
      xaxis: {
        categories: ['Tổng số', 'Đang khóa'],
        axisTicks: {
          show: false
        }
      },
      colors: [
        '#2196F3', // Blue
        '#fc0000' // Red
      ],
      legend: {
        show: false
      }
    }
  }
}
