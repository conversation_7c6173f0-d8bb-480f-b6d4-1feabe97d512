import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { PATH_API } from '../constants'
import { HttpClientService, HttpOptions, Verbs } from '@shared'
import { AppService } from '@services/app.service'

@Injectable({
  providedIn: 'root'
})
export class AddressOverviewService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  getTelcoAddressOverview() {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.GET_TELCO_ADDRESS_OVERVIEW}`
    }
    return this.httpClient.get(options)
  }

  getTelcoPhoneNumberOverview() {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.GET_TELCO_PHONE_NUMBER_OVERVIEW}`
    }
    return this.httpClient.get(options)
  }

  getTelcoPhoneNumberDetail(periodType) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.GET_LOCKED_PHONE_NUMBER_OVERVIEW}`,
      params: {
        periodType
      }
    }
    return this.httpClient.get(options)
  }
}
