import { Routes } from '@angular/router'
import { RESOURCE_CODE, Scopes } from '@shared'
import { ChangeStatusImportComponent } from '@features/address/pages/import/change-status-import.component'
import { environment } from '@env/environment'

export const ROUTES_NAME_ADDRESS = {
  LIST: environment.base_path + '/system-management/address'
}
export const routes: Routes = [
  {
    path: '',
    title: 'Tổng quan đầu số',
    loadComponent: async () =>
      (await import('@features/address-overview/pages/overview/address-overview-list.component')).AddressOverviewListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  }
  // {
  //   path: 'create',
  //   title: 'Thêm mới đầu số',
  //   loadComponent: async () => (await import('./pages/create/address-create.component')).AddressCreateComponent,
  //   data: { breadcrumb: 'Thêm mới đầu số' }
  // },
  // {
  //   path: 'edit/:id',
  //   title: 'Chỉnh sửa đầu số',
  //   loadComponent: async () => (await import('./pages/edit/address-edit.component')).AddressEditComponent,
  //   data: { breadcrumb: 'Chỉnh sửa đầu số' }
  // },
  // {
  //   path: 'import',
  //   title: 'Cập nhật trạng thái theo lô',
  //   loadComponent: async () => (await import('./pages/import/change-status-import.component')).ChangeStatusImportComponent,
  //   data: { breadcrumb: 'Cập nhật trạng thái theo lô' }
  // }
]
