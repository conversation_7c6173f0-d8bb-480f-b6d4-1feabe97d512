/**
 * API PATH
 */
import { environment } from '@env/environment'

export const PATH_API = {
  CREATE_UPDATE: `${environment.services.pbxCms}/v1/voice-blaster-campaign`,
  RUN_CAMPAIGN: `${environment.services.pbxCms}/v1/voice-blaster-campaign`,
  UPDATE_STATUS: `${environment.services.pbxCms}/v1/voice-blaster-campaign`,

  GETLIST_EMPLOYEE: `${environment.services.dcmsCoreCampaign}/v1/campaigns/agents`,
  UPLOAD_PROFILE: `${environment.services.dcmsCoreCampaign}/v1/profile/upload`,
  PREVIEW_PROFILE: `${environment.services.dcmsCoreCampaign}/v1/profile/preview`,
  SAVE_PROFILE: `${environment.services.dcmsCoreCampaign}/v1/profile/save`,
  ASSIGN: `${environment.services.dcmsCoreCampaign}/v1/profile/assign`,
  REVOKE: `${environment.services.dcmsCoreCampaign}/v1/profile/revoke`,
  PROFILE_CONTENT: `${environment.services.dcmsCoreCampaign}/v1/profile/profile-content`,
  PROFILE_CONTENT_V2: `${environment.services.dcmsCoreCampaign}/v2/profile/search-profile-content`,
  DELETE_PROFILE_CONTENT: `${environment.services.dcmsCoreCampaign}/v2/profile/profile-content`,
  PROFILE: `${environment.services.dcmsCoreCampaign}/v1/profile`,
  PIN: `${environment.services.dcmsCoreCampaign}/v1/campaigns`
}

export enum CampaignEnumStatus {
  NEW = 0,
  RUNNING = 1,
  COMPLETED = 2
}

export const CampaignStatus = {
  NEW: 'NEW',
  READY: 'READY',
  COMPLETED: 'COMPLETED'
}

export enum CampaignType {
  DRAFT = 0,
  NORMAL = 1
}

export enum ImplementType {
  NHAN_SU = 0,
  BOT = 1
}
export const StatusDescription: { [key in CampaignEnumStatus]: string } = {
  [CampaignEnumStatus.NEW]: 'New',
  [CampaignEnumStatus.RUNNING]: 'Running',
  [CampaignEnumStatus.COMPLETED]: 'Completed'
}
