export interface ProfileUploadData {
  fileHash: string
  fileId: string
  profileAttributes: ProfileAttribute[]
}

export interface ProfileAttribute {
  excelColumnIndex: number
  profileAttributeKey: string
  profileAttributeName: string
  duplicateCheck: boolean
}

export interface SelectedColumn {
  excelColumnIndex: number
  profileAttributeKey: string
  duplicateCheck: boolean
}

export interface MetaDataSaveProfile {
  total:               number;
  numberOfSuccessRows: number;
  fileExtension:       string;
  profileSetId:        number;
  numberOfFailRows:    number;
}
