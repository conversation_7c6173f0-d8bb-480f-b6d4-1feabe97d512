export interface DetailCampaignModel {
  campaignId?: number
  campaignCode?: string
  campaignStatus?: string
  campaignName?: string
  campaignDescription?: string
  campaignType?: number
  productCode?: string
  productName?: string
  productPath?: string
  businessCode?: string
  businessName?: string
  businessPath?: string
  departmentCode?: string
  departmentName?: string
  startDate?: string
  endDate?: string
  priorityLevel?: number
  callingScenario?: string
  activeRate?: number
  pickupRate?: number
  implementType?: number
  createdBy?: string
  updatedBy?: string
  createdAt?: string
  updatedAt?: string
  saleCloseContents?: any[]
  profiles?: any[]
  implementAgents?: any[]
  maximumCallNumbers?: string
  totalProfiles?: number
  assignedProfiles?: number
  unassignedProfiles?: number
  inputManualProfiles?: number
  blacklistProfiles?: number
  pinnedBy?:boolean
}

export interface ProfileInCampaign {
  campaignInfo: DetailCampaignModel,
  content: ProfileContentModel[]
}

export interface ProfileContentModel {
  profileContentId: number
  profileSetId: number
  customerCode: string
  customerName: string
  customerEmail: string
  customerPhoneNumber: string
  customerCreditLimit: string
  customerBranchNameLevel1: string
  customerExtraData: string
  profileStatus: number
  agentEmployeeId: number
  assignedAgent: string
  assignedDate: string
  campaignData: DetailCampaignModel
  interactedProfiles: string
  notInteractedProfiles: string
}

// export interface DetailCampaignModel {
//   campaignId?: number
//   campaignCode?: string
//   campaignStatus?: string
//   campaignName?: string
//   campaignDescription?: string
//   campaignType?: number
//   productCode?: string
//   productName?: string
//   productPath?: string
//   businessCode?: string
//   businessName?: string
//   businessPath?: string
//   departmentCode?: string
//   departmentName?: string
//   startDate?: string
//   endDate?: string
//   priorityLevel?: number
//   callingScenario?: string
//   activeRate?: number
//   pickupRate?: number
//   implementType?: number
//   createdBy?: string
//   updatedBy?: string
//   createdAt?: string
//   updatedAt?: string
//   saleCloseContents?: any[]
//   profiles?: any[]
//   implementAgents?: any[]
//   maximumCallNumbers?: string
//   totalProfiles?: number
//   assignedProfiles?: number
//   unassignedProfiles?: number
//   inputManualProfiles?: number
//   blacklistProfiles?: number
//   pinnedBy?:boolean
// }
//
// export interface ProfileInCampaign {
//   campaignInfo: DetailCampaignModel,
//   profileContentViews: {
//     content: ProfileContentModel[]
//   }
// }

