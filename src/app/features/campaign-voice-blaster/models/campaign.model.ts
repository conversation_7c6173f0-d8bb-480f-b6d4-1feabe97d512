export interface ReqCreateCampaign {
  sourceId?: any
  campaignDescription?: string
  campaignType?: number
  campaignStatus?: string
  productCode?: string
  businessCode?: string
  startDate?: any
  endDate?: any
  departmentCode?: string
  priorityLevel?: string
  callingScenario?: string
  activeRate?: number
  maximumCallNumbers?: string
  pickupRate?: number
  implementType?: number
  saleCloseContents?: SaleCloseContent[]
  profiles?: Profile[]
  implementAgents?: ImplementAgent[]
  agentConfig?: AgentConfig
  pinnedBy?: string,
  campaignId?: string,
}

export interface SaleCloseContent {
  originalQuestionId: number
  answerType: string
  isDecisive: boolean
  answers: Answer[]
}

export interface Answer {
  originalAnswerId: number
  isDecisive: boolean
}

export interface AgentConfig {}

export interface ImplementAgent {
  agentEmployeeId: number
}

export interface Profile {
  profileSetId: number
}
