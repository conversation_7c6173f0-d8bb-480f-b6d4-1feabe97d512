<div id="chart" class='d-flex flex-center-row chart-container justify-content-center'>
  <apx-chart
    [plotOptions]="chartOptions.plotOptions"
    [series]="chartOptions.series"
    [chart]="chartOptions.chart"
    [labels]="chartOptions.labels"
    [dataLabels]="chartOptions.dataLabels"
    [responsive]="chartOptions.responsive"
    [legend]="chartOptions.legend"
    [colors]="chartOptions.colors"
    class='chart'
  ></apx-chart>
  <ul class="custom-list ul-cls mrt-5">
    <li *ngFor="let item of chartData1" [ngStyle]="{'--circle-color': item.color}">
      <div class='d-flex justify-content-between flex-center-row legends'>
        <div>
        <span class="circle"></span>
        {{ item.label }}</div>
        <div class='value'>{{ item.value }} HS</div>
      </div>
    </li>
  </ul>
</div>


