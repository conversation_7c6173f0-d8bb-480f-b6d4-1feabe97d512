import { Component, Injector, ViewChild } from '@angular/core'
import { ComponentAbstract, SharedSMModule } from '@shared'
import { ChartComponent, NgApexchartsModule } from 'ng-apexcharts'
import { ApexOptions } from 'ng-apexcharts/lib/model/apex-types'
import { CommonModule } from '@angular/common'
import { DEFAULT_LABEL_SIZE } from 'bpmn-js/lib/util/LabelUtil'

@Component({
  selector: 'app-donut-chart',
  templateUrl: './donut-chart.component.html',
  styleUrls: ['./donut-chart.component.scss'],
  standalone: true,
  imports: [NgApexchartsModule, SharedSMModule, CommonModule]
})
export class DonutChartComponent extends ComponentAbstract {
  @ViewChild('chart') chart: ChartComponent
  public chartOptions: Partial<ApexOptions>
  chartData1 = []
  constructor(protected override injector: Injector) {
    super(injector)
  }

  protected override componentInit() {
    this.createChart()
  }

  createChart(): void {
    this.chartOptions = {
      // series: this.chartData,
      series: this.chartData1.map((e) => e.value),
      chart: {
        type: 'donut',
        height: 350
      },
      plotOptions: {
        pie: {
          customScale: 0.9,
          donut: {
            labels: {
              show: true,
              name: {},
              value: {},
              total: {
                show: true,
                showAlways: true,
                label: 'Tổng số HS',
                fontSize: '12px',
                fontFamily: 'Roboto, sans-serif',
                fontWeight: 600,
                color: '#373d3f',
                formatter: function (w) {
                  return w.globals.seriesTotals.reduce((a, b) => {
                    return a + b
                  }, 0)
                }
              }
            }
          }
        }
      },
      colors: this.chartData1.map((e) => e.color),
      labels: this.chartData1.map((e) => e.label),
      // responsive: [
      //   {
      //     breakpoint: 480,
      //     options: {
      //       chart: {
      //         width: 140,
      //         height: 140
      //       },
      //       legend: {
      //         position: 'bottom'
      //       }
      //     }
      //   }
      // ],
      dataLabels: {
        enabled: false,
        formatter: function (val) {
          return val + ''
        }
      },
      legend: {
        show: false
      }
    }
  }

  protected readonly Number = Number
  protected readonly DEFAULT_LABEL_SIZE = DEFAULT_LABEL_SIZE
}
