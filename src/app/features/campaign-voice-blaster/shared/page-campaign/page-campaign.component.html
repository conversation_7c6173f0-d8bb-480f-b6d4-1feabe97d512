<div  class="ui-builder">
  <ng-container #outlet >
  </ng-container>
</div>
<ng-template #content>
  <uib-zone id="page" class="page-content">
    <ng-container *uib-template="'title'; let config">
      <div class="flex flex-row align-items-center">
        <div class='title-page'>
          {{ config?.title }}
        </div>
        <ng-container *ngIf="config?.tooltip">
          <mat-icon
            [matTooltip]="config?.tooltip"
            [matTooltipPosition]="'above'"
            class="mat-icon cursor-pointer mbb-icon material-icons mat-ligature-font mat-icon-no-color mrb-1 mrl-1">
            help
          </mat-icon>
        </ng-container>
      </div>
    </ng-container>
    <ng-container *uib-template="'typo'; let config">
      <div>{{ config?.title }}</div>
    </ng-container>
    <ng-container *uib-template="'panel-heading'; let config">
      <div *ngIf="config?.tableTitle" class="panel-heading justify-content-between bdt">
        <label>
          {{ config?.title }}
        </label>
      </div>
    </ng-container>
    <ng-container *uib-template="'table'; let config">
      <app-table-tree #table [configComponent]="config" (onButtonClick)="onButtonClick($event)" (onTableActionClick)="onTableActionClick($event)"></app-table-tree>
    </ng-container>
    <ng-container *uib-template="'table-expand'; let config">
      <app-table-expand #table [configComponent]="config" (onButtonClick)="onButtonClick($event)" (onTableActionClick)="onTableActionClick($event)"></app-table-expand>
    </ng-container>
    <ng-template uib-template="formControl" let-config>
      <app-form-control-renderer
        [config]="config"
        (clickMessageError)="clickMessageError($event)"
        (formChanged)="formChanged($event)">
      </app-form-control-renderer>
<!--      <ng-container [ngSwitch]="config?.data?.controlType">-->
<!--        <app-text-control *ngSwitchCase="FORM_CONTROL_TYPE.TEXT_BOX" [item]="config?.data || {}" (onChanged)="formChanged($event)"></app-text-control>-->
<!--        <app-textarea-control *ngSwitchCase="FORM_CONTROL_TYPE.TEXTAREA" [item]="config?.data || {}" (onChanged)="formChanged($event)"></app-textarea-control>-->
<!--        <app-checkbox-control *ngSwitchCase="FORM_CONTROL_TYPE.CHECKBOX" [item]="config?.data || {}" (onChanged)="formChanged($event)"></app-checkbox-control>-->
<!--        <app-select-control *ngSwitchCase="FORM_CONTROL_TYPE.DROPDOWN" [item]="config?.data || {}" (onChanged)="formChanged($event)" (clickMessageError)="clickMessageError($event)"></app-select-control>-->
<!--        <app-date-control *ngSwitchCase="FORM_CONTROL_TYPE.DATE" [item]="config?.data || {}" (onChanged)="formChanged($event)"></app-date-control>-->
<!--        <app-date-time-control *ngSwitchCase="FORM_CONTROL_TYPE.DATETIME" [item]="config?.data || {}" (onChanged)="formChanged($event)"></app-date-time-control>-->
<!--      </ng-container>-->
    </ng-template>
  </uib-zone>
</ng-template>

