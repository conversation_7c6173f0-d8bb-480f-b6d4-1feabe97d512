import { Component, EventEmitter, Output, TemplateRef, <PERSON>Child, ViewContainerRef, ViewEncapsulation } from '@angular/core'
import { ComponentAbstract, EVENT_FORM_CONTROL, FORM_CONTROL_TYPE } from '@shared'
import { Checkbox<PERSON>ontrolComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatIconModule } from '@angular/material/icon'
import { NgIf, NgSwitch, NgSwitchCase } from '@angular/common'
import { TemplateNameDirective, ZoneComponent } from '@mb/ngx-ui-builder'
import { AppTableComponent, AppTableTreeComponent } from '@shared/components/data-display'
import { AppTableExpandedComponent } from '@shared/components/data-display/app-table-expand/app-table-expand.component'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'

@Component({
  selector: 'page-campaign',
  templateUrl: './page-campaign.component.html',
  styleUrls: ['./page-campaign.component.scss'],
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [
    ZoneComponent,
    TemplateNameDirective,
    NgIf,
    MatIconModule,
    MatTooltipModule,
    TextControlComponent,
    TextareaControlComponent,
    CheckboxControlComponent,
    SelectControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    AppTableComponent,
    NgSwitch,
    NgSwitchCase,
    FormControlRendererComponent,
    AppTableTreeComponent,
    AppTableExpandedComponent
  ]
})
export class PageCampaignComponent extends ComponentAbstract {
  @ViewChild('outlet', { read: ViewContainerRef }) outletRef: ViewContainerRef
  @ViewChild('content', { read: TemplateRef }) contentRef: TemplateRef<any>
  @ViewChild('table') table: AppTableTreeComponent
  @Output() onChanged = new EventEmitter<any>()
  @Output() onFormInit = new EventEmitter<any>()

  /**
   * repaint Ui
   * @private
   */
  private rePaint() {
    this.outletRef?.clear()
    this.outletRef.createEmbeddedView(this.contentRef)
  }

  protected componentInit(): void {}

  override afterView() {
    this.rePaint()
    this.cdRef.detectChanges()
  }

  /**
   * trả ra sự kiện khi bấm vào component có type là button
   * @param $event
   */
  onButtonClick($event: any) {
    this.onChanged.emit({ type: 'button', event: $event })
  }

  onTableActionClick($event: any) {
    this.onChanged.emit({ type: 'buttonTable', event: $event })
  }

  formChanged($event: any) {
    if ($event?.data == EVENT_FORM_CONTROL.CREATE_CONTROL) {
      this.form.setControl($event?.item.key, $event.form.get($event?.item.key))
      this.onFormInit.emit({
        type: EVENT_FORM_CONTROL.CREATE_CONTROL,
        form: this.form,
        items: this.configService.getAllConfig().filter((x) => x.type === 'formControl')
      })
    } else {
      this.onChanged.emit({ type: 'formData', event: $event })
    }
  }

  validateForm() {
    this.validateAllFields(this.form)
  }

  get formRawValue(): any {
    const obj = this.form.getRawValue()
    Object.keys(obj).forEach((key) => {
      if (obj[key] === undefined || obj[key] === 'undefined') {
        delete obj[key]
      }
    })
    return obj
  }

  clickMessageError($event: any) {
    this.onChanged.emit({ type: 'clickMessageError', event: $event })
  }

  protected readonly FORM_CONTROL_TYPE = FORM_CONTROL_TYPE
}
