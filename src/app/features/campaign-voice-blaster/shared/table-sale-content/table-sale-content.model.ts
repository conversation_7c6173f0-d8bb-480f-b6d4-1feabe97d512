// To parse this data:
//
//   import { Convert, TableModel } from "./file";
//
//   const tableModel = Convert.toTableModel(json);

import { ComponentConfig } from '@mb/ngx-ui-builder'

export interface TableSaleContentComponentConfig extends ComponentConfig {
  type: string | '_container'
  data: TableSaleContentConfigModel
}

export interface TableSaleContentConfigModel {
  isStaticTable?: boolean
  isTreeData?: boolean
  noPaging?: boolean
  hideSearchAdvanced?: boolean // hide button seach advanced
  childrenAttr?: string
  pageSize?: string
  tableTitle?: string
  apiDetail?: string
  apiList?: string
  apiCreate?: string
  apiUpdate?: string
  apiDelete?: string
  buttonLists?: any[]
  displayedColumns?: DisplayedColumnSaleContent[]
  quickSearchFields?: QuickSearchFieldSaleContent[]
  columnActionLists?: ColumnActionSaleContent[]
}

export interface ColumnActionSaleContent {
  title?: string
  type?: string
  class?: string
  icon?: string
  navigationType?: string
  routerName?: string
  scope?: string
  row?: any // data click
  maxLength?: string
  pathExpand?: string
}

export interface DisplayedColumnSaleContent {
  name?: string
  field?: string
  path?: string
  show?: boolean
  type?: string
  enumText?: OptionEnumTextSaleContent[]
}

export interface OptionEnumTextSaleContent {
  key?: string
  text?: string
  class?: string
}

export interface QuickSearchFieldSaleContent {
  key?: string
  text?: string
}

// Converts JSON strings to/from your types
export class Convert {
  public static toTableModel(json: string): TableSaleContentConfigModel {
    return JSON.parse(json)
  }

  public static tableModelToJson(value: TableSaleContentConfigModel): string {
    return JSON.stringify(value)
  }
}
