import { Component, EventEmitter, Injector, Input, isDevMode, Output, SimpleChanges, ViewChild } from '@angular/core'
import { MatTable, MatTableModule } from '@angular/material/table'
import { AppPaginationComponent, ComponentAbstract, extractValueFromColon, MessageSeverity } from '@shared'
import { Subject } from 'rxjs'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { JsonPipe, NgFor, NgIf } from '@angular/common'
import { SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { FlatTreeControl } from '@angular/cdk/tree'
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree'
import { DisplayedColumn, TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'

import _ from 'lodash'
import { AppTableCellDisplayComponent } from '@shared/components/data-display/app-table-cell-display/app-table-cell-display.component'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { FunctionCallerPipe, ToggleColumnsPipe, TruncatePipe } from '../../../../shared/pipe'
import { getElementByPath, getTooltipByPath } from '../../../../shared/utils/table.utils'
import { CdkDrag, CdkDragDrop, CdkDragPreview, CdkDropList } from '@angular/cdk/drag-drop'
import { MatCheckbox } from '@angular/material/checkbox'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { ArrayToStringPipe } from '../../../../shared/pipe/array-to-string-multiline'

@Component({
  selector: 'table-sale-content',
  templateUrl: './table-sale-content.component.html',
  styleUrls: ['./table-sale-content.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    TextControlComponent,
    SelectControlComponent,
    MatIconModule,
    NgFor,
    MatExpansionModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    NoDataComponent,
    AppPaginationComponent,
    ToggleColumnsPipe,
    ZoneComponent,
    JsonPipe,
    FunctionCallerPipe,
    TruncatePipe,
    AppTableCellDisplayComponent,
    CdkDropList,
    CdkDrag,
    MatCheckbox,
    TextareaControlComponent,
    CdkDragPreview,
    ArrayToStringPipe
  ]
})
export class TableSaleContentComponent extends ComponentAbstract {
  @ViewChild('table', { static: true }) table: MatTable<any>
  @Input() configComponent: TableComponentConfig
  @Input() isSearchAdvanced = false
  @Input() isSearchByKeyword = false
  @Input() isTreeSource: boolean = true

  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onDeleteQuestion = new EventEmitter<any>()
  @Output() onDeleteAnswer = new EventEmitter<any>()
  visibleColumns: DisplayedColumn[] = []
  @Input() dataTable = []
  datasourceTree: MatTreeFlatDataSource<any, any, any>
  expandedNodeNames: any[] = []
  private unsubscribe$: Subject<void> = new Subject<void>()

  dragDisabled = true
  enabledDragAndDrop = true

  drop(event: CdkDragDrop<any>) {
    // Return the drag container to disabled.
    this.dragDisabled = true

    const previousIndex = this.datasourceTree.data.findIndex((d) => d?.questionId === event.item.data?.questionId)

    // Cập nhật vị trí của phần tử trong cây
    const nodeToMove = this.datasourceTree.data.splice(previousIndex, 1)[0]
    this.datasourceTree.data.splice(event.currentIndex, 0, nodeToMove)

    // Cập nhật lại datasource
    this.datasourceTree.data = [...this.datasourceTree.data]

    // Render lại bảng
    this.table.renderRows()
  }

  saveExpandedState() {
    this.expandedNodeNames = this.treeControl.dataNodes.filter((node) => this.treeControl.isExpanded(node)).map((node) => node.key) // Sử dụng khóa duy nhất của node
  }

  restoreExpandedState() {
    this.expandedNodeNames.forEach((key) => {
      const node = this.treeControl.dataNodes.find((node) => node.key === key)
      if (node) {
        this.treeControl.expand(node)
      }
    })
  }

  private transformer = (node: any, level: number) => {
    return {
      expandable: !!node[this.configComponent.data.childrenAttr],
      ...node,
      level: level
    }
  }

  treeControl = new FlatTreeControl<any>(
    (node) => node.level,
    (node) => node.expandable
  )

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node[this.configComponent.data.childrenAttr]
  )

  constructor(protected override injector: Injector) {
    super(injector)
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.dataTable = []
  }

  initQuickSearchForm() {}

  get columns() {
    return this.visibleColumns
  }

  get isStaticTable() {
    return this.configComponent?.data.isStaticTable || false
  }

  get columnActionLists() {
    return this.configComponent?.data?.columnActionLists || []
  }

  get advancedSearchFields() {
    return this.configComponent?.data.displayedColumns || []
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {
    this.loadDataTable()
  }

  override ngOnDestroy() {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }

  /**
   * do not remove
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    isDevMode() && console.log('TableSaleContentComponent', changes)
    this.loadDataTable()
    this.setVisibleColumns()
  }

  setVisibleColumns() {
    if (this.configComponent?.data?.displayedColumns) {
      this.visibleColumns = [] // trigger re-render live edit ux builder
      setTimeout(() => {
        const setDefaultColumnCell = this.configComponent?.data?.displayedColumns.map((e) => {
          if (e.type) {
            return e
          } else {
            return {
              ...e,
              type: 'textbox'
            }
          }
        })
        this.visibleColumns = _.uniqBy([...this.visibleColumns, ...setDefaultColumnCell] || [], 'field')
      }, 100)
    }
  }

  loadDataTable() {
    this.totalItem = this.dataTable.length
    // this.pageSize = this.configComponent.data.pageSize ? Number(this.configComponent.data.pageSize) : 10
    this.setDataSource()
  }

  setDataSource() {
    this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.dataTable)
  }

  expandNode(index: number) {
    if (this.treeControl.dataNodes?.length) {
      this.treeControl.expand(this.treeControl.dataNodes[index])
    }
  }

  getStaticRecords(pageIndex: number, pageSize: number) {
    const startIndex = pageIndex * pageSize
    const endIndex = startIndex + pageSize
    return this.dataTable.slice(startIndex, endIndex)
  }

  onDelete(key, element: any) {
    // const index = this.dataTable.findIndex((e) => e.key === key)
    this.saveExpandedState()
    this.dataTable = this.dataTable.filter((e) => String(e.questionId) != String(key))
    console.log('delete table', this.dataTable)
    this.loadDataTable()
    this.onDeleteQuestion.emit(element)
    this.restoreExpandedState()
  }

  deleteChild(answer: any) {
    const id = answer.answerId
    this.saveExpandedState()
    const question = this.dataTable.find((x) => x.answers.findIndex((a) => a.answerId === id) !== -1)
    const countAnswerDecisive = question.answers?.filter((e) => e.isDecisive).length
    if (question.answers?.length === 1) {
      this.toastr.showToastr('1 câu hỏi có ít nhất 1 câu trả lời', '', MessageSeverity.error)
    } else {
      if (countAnswerDecisive < 2 && question.isDecisive && answer.isDecisive === true) {
        this.toastr.showToastr('Phải có ít nhất 1 câu trả lời mang tính quyết định', '', MessageSeverity.error)
      } else {
        this.dataTable = this.datasourceTree.data.map((q) => {
          return {
            ...q,
            answers: q.answers.filter((e) => {
              return e.answerId !== id
            })
          }
        })
        this.onDeleteAnswer.emit(answer)
        this.datasourceTree.data = [...this.dataTable]
        this.restoreExpandedState()
      }
    }
  }

  /**
   * thanhnx
   * Bấm vào button ở table column action
   * @param button
   */
  onActionColumnClick(button, element) {
    const propertyId = extractValueFromColon(button.routerName)
    //TODO: check role
    if (element.expandable) {
      this.onDelete(element[propertyId], element)
    } else {
      this.deleteChild(element)
      // this.treeControl.expand(element)
    }
  }

  protected readonly getElementByPath = getElementByPath
  protected readonly _ = _

  onDisabled(b: boolean) {
    this.dragDisabled = b
    if (this.treeControl.expansionModel.selected?.length) {
      this.treeControl.collapseAll()
      this.saveExpandedState()
      this.toastr.showToastri18n('Tự động câu trả lời đang mở trước khi kéo thả câu hỏi', '', MessageSeverity.error)
    }
  }
  pathProperty(element) {
    return element.expandable ? 'path' : 'pathExpand'
  }
  getToolTipStatus(parentEle, cell) {
    return parentEle?.offsetWidth < cell?.eleRef?.nativeElement?.previousElementSibling?.offsetWidth
  }
  protected readonly getTooltipByPath = getTooltipByPath
}
