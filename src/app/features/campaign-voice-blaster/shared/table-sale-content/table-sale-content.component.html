<div class="table-over">
  <table class="app-table table-info w-full table-layout-fixed"
         #table
         cdkDropList
         (cdkDropListDropped)="drop($event)"
         cdkDropListData="dataSource"
         [cdkDropListDisabled]="dragDisabled"
         element.htmlForm mat-table
         [dataSource]="datasourceTree" [@.disabled]="true">
    <ng-container
      *ngFor="let column of columns, let i=index"
      [cdkColumnDef]="column.field"
    >
      <th mat-header-cell *matHeaderCellDef [class.action]="column.field === 'action'">{{ column.name }}</th>
      <td mat-cell *matCellDef="let element">
        <div class="flex-center-row" #parentEle>
          <ng-container *ngIf="i === 0 && configComponent?.data?.isTreeData">
            <button *ngIf="element.expandable && enabledDragAndDrop" (mousedown)="onDisabled(false)">
              <mat-icon matTooltipPosition='above' matTooltip="Kéo thả để thay đổi vị trí" class="mbb-icon ic-drag dragCursor drag-icon"></mat-icon>
            </button>
            <button mat-icon-button
                    [style.visibility]="!element.expandable ? 'hidden' : ''"
                    [style.margin-left.px]="element.level * 32"
                    (click)="treeControl.toggle(element)">
              <mat-icon class="mat-icon-rtl-mirror">
                {{treeControl.isExpanded(element) ? 'expand_more' : 'chevron_right'}}
              </mat-icon>
            </button>
          </ng-container>
          <ng-container *ngIf="column.field === 'action'; else elseNotAction">
            <ng-container *ngFor="let button of columnActionLists, let i=index">
              <button (click)="onActionColumnClick(button, element)" matTooltip="{{button?.title}}" mat-icon-button>
                <mat-icon class="mbb-icon mrr-0 {{button.icon}}"></mat-icon>
              </button>
            </ng-container>
          </ng-container>
          <ng-template #elseNotAction>
            <app-table-cell-display #cell [class.text-ellipsis]="column" [element]="element" [column]="column" [pathProperty]="element.expandable ? 'path' : 'pathExpand'" matTooltip="{{ getTooltipByPath | functionCaller: element : column : pathProperty(element) | arrayToString }}" [matTooltipDisabled]='!getToolTipStatus(parentEle, cell)'></app-table-cell-display>
          </ng-template>
        </div>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns | toggleColumns:columns;"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns | toggleColumns:columns;"
        cdkDrag
        [cdkDragData]="row"
        [cdkDragDisabled]="!row.expandable"
    >
      <table *cdkDragPreview class="app-table table-info drag-preview">
        <tbody>
        <tr>
          <td style="width: 100px">
            <div class="flex-center-row">
              <mat-icon class="mat-icon-rtl-mirror">
                {{treeControl.isExpanded(row) ? 'expand_more' : 'chevron_right'}}
              </mat-icon>
              {{ row.no }}
            </div>
          </td>
          <td>
            {{ row.questionContent }}
          </td>
        </tr>
        </tbody>
      </table>
    </tr>

  </table>
  <app-no-data *ngIf="!datasourceTree?.data?.length" message='Chưa có Nội dung chốt sale. Vui lòng Thêm vào danh sách.'></app-no-data>
</div>
<div class="panel-footer" *ngIf="!configComponent.data.noPaging">
  <app-pagination
    class="fullWidth"
    #pagePage
    [pageIndex]="0"
    [totalItem]="datasourceTree?.data?.length"
    [pageSize]="99999"
    [showTextPage]="true"
    [showPageSizeList]="false"
    [showPage]="false"
    ></app-pagination>
</div>
