// campaign-business-logic.user.ts
import { Injectable } from '@angular/core'
import { ProfileUploadData } from '@features/campaign-voice-blaster/models/profile-import.model'
import { BehaviorSubject } from 'rxjs'
import { Profile, ReqCreateCampaign, SaleCloseContent } from '@features/campaign-voice-blaster/models/campaign.model'
import moment from 'moment'
import { Router } from '@angular/router'
import { DetailCampaignModel } from '@features/campaign-voice-blaster/models/detail-campaign.model'

@Injectable({
  providedIn: 'root'
})
export class CampaignBusinessLogicService {
  buttonClick = new BehaviorSubject<any>(null)
  buttonClick$ = this.buttonClick.asObservable()

  // Biến chia sẻ dữ liệu ProfileUploadData
  profileUploadDataSource = new BehaviorSubject<ProfileUploadData>(null)
  profileUploadData$ = this.profileUploadDataSource.asObservable()

  // Khởi tạo BehaviorSubject với giá trị khởi tạo là null hoặc một giá trị mặc định
  private campaignDetailSubject = new BehaviorSubject<any | null>(null)

  // Observable để các thành phần khác có thể đăng ký theo dõi
  campaignDetail$ = this.campaignDetailSubject.asObservable()

  // Khởi tạo BehaviorSubject với giá trị khởi tạo là null hoặc một giá trị mặc định
  private campaignSubject = new BehaviorSubject<ReqCreateCampaign | null>(null)

  // Observable để các thành phần khác có thể đăng ký theo dõi
  campaign$ = this.campaignSubject.asObservable()

  saleContentCreate = new BehaviorSubject<any>(null)
  saleContentCreate$ = this.saleContentCreate.asObservable()

  lastProfileId = new BehaviorSubject<{ profileSetId: any }>(null)
  lastProfileId$ = this.lastProfileId.asObservable()

  profileContentIsSoftDelete = new BehaviorSubject<any>(null)
  profileContentIsSoftDelete$ = this.profileContentIsSoftDelete.asObservable()

  constructor(private router: Router) {}

  // Hàm cập nhật dữ liệu ProfileUploadData sau khi upload
  updateProfileUploadData(newData: ProfileUploadData) {
    this.profileUploadDataSource.next(newData)
  }

  // Phương thức để thêm Profile mới vào danh sách profiles
  addProfileId(newProfile: Profile): void {
    const currentCampaign = this.campaignSubject.value

    if (currentCampaign) {
      // Lấy danh sách profiles hiện tại
      const currentProfiles = currentCampaign.profiles || []

      // Kiểm tra xem Profile đã tồn tại hay chưa
      const profileExists = currentProfiles.some((profile) => profile.profileSetId === newProfile.profileSetId)

      if (!profileExists) {
        // Thêm Profile mới vào danh sách
        const updatedProfiles = [...currentProfiles, newProfile]

        // Cập nhật BehaviorSubject với danh sách mới
        this.updateCampaign({ profiles: updatedProfiles })
      }
    } else {
      // Nếu chưa có chiến dịch, tạo mới với Profile đầu tiên
      this.updateCampaign({ profiles: [newProfile] })
    }
  }

  // Phương thức để cập nhật dữ liệu chiến dịch
  updateCampaign(updatedCampaign: Partial<ReqCreateCampaign>): void {
    const currentCampaign = this.campaignSubject.value

    // Nếu giá trị hiện tại là null, khởi tạo đối tượng mới
    const newCampaign = currentCampaign ? { ...currentCampaign, ...updatedCampaign } : updatedCampaign

    // Convert moment startDate and endDate to ISO strings if they exist in the updatedCampaign
    if (updatedCampaign?.startDate && moment.isMoment(updatedCampaign.startDate)) {
      newCampaign.startDate = updatedCampaign.startDate.valueOf()
    }
    if (updatedCampaign?.endDate && moment.isMoment(updatedCampaign.endDate)) {
      newCampaign.endDate = updatedCampaign.endDate.valueOf()
    }

    // Nếu toàn bộ object được đặt thành null, gán null vào BehaviorSubject
    if (updatedCampaign === null) {
      this.campaignSubject.next(null)
    } else {
      this.campaignSubject.next(newCampaign as ReqCreateCampaign)
    }
  }

  updateCampaignDetail(data: Partial<any>, overwrite: boolean = true): void {
    if (overwrite) {
      this.campaignDetailSubject.next(data)
    } else {
      const currentDetail = this.campaignDetailSubject.getValue()
      const updatedDetail = {
        ...currentDetail,
        ...data
      }
      this.campaignDetailSubject.next(updatedDetail)
    }
  }

  // Phương thức để cập nhật saleCloseContents
  updateSaleCloseContents(newContents: SaleCloseContent[]): void {
    const currentCampaign = this.campaignSubject.value

    if (currentCampaign) {
      this.updateCampaign({ saleCloseContents: newContents })
    } else {
      // Nếu chưa có chiến dịch, tạo mới với saleCloseContents đầu tiên
      this.updateCampaign({ saleCloseContents: newContents })
    }
  }

  getLastProfile(): any | null {
    return this.lastProfileId.value
  }

  // Phương thức để cập nhật profile
  updateLastProfile(newProfileId: any): void {
    this.lastProfileId.next(newProfileId)
  }

  validate(campaign: ReqCreateCampaign): { valid: boolean; message: string } {
    if (campaign.campaignType === undefined || campaign.campaignType === null) {
      return { valid: false, message: 'Campaign type is required.' }
    }
    if (campaign.productCode === undefined || campaign.productCode === '') {
      return { valid: false, message: 'Product code is required.' }
    }
    if (campaign.businessCode === undefined || campaign.businessCode === '') {
      return { valid: false, message: 'Business code is required.' }
    }
    if (campaign.startDate === undefined || campaign.startDate === null) {
      return { valid: false, message: 'Start date is required.' }
    }
    if (campaign.departmentCode === undefined || campaign.departmentCode === '') {
      return { valid: false, message: 'Department code is required.' }
    }
    if (campaign.priorityLevel === undefined || campaign.priorityLevel === '') {
      return { valid: false, message: 'Priority level is required.' }
    }
    if (campaign.implementType === undefined || campaign.implementType === null) {
      return { valid: false, message: 'Implement type is required.' }
    }
    if (campaign.saleCloseContents === undefined || !Array.isArray(campaign.saleCloseContents) || campaign.saleCloseContents.length === 0) {
      return { valid: false, message: 'Sale close contents must be a non-empty array.' }
    }
    if (campaign.profiles === undefined || !Array.isArray(campaign.profiles) || campaign.profiles.length === 0) {
      return { valid: false, message: 'Profiles must be a non-empty array.' }
    }
    if (campaign.implementAgents === undefined || !Array.isArray(campaign.implementAgents) || campaign.implementAgents.length === 0) {
      return { valid: false, message: 'Implement agents must be a non-empty array.' }
    }
    return { valid: true, message: 'Validation passed.' }
  }

  sortSaleCloseContents(saleCloseContents: any[], saleCloseContentsSort: any[]) {
    if (saleCloseContentsSort?.length) {
      // Tạo một map từ questionId tới index trong arrayB
      const questionIdMap = new Map(saleCloseContentsSort.map((item, index) => [item.questionId, index]))

      // Sắp xếp arrayA dựa trên thứ tự của questionId trong arrayB
      return saleCloseContents?.sort((a, b) => questionIdMap.get(a.questionId) - questionIdMap.get(b.questionId))
    } else {
      return saleCloseContents
    }
  }

  // Hàm reset toàn bộ state hiện tại
  resetState(): void {
    console.log('resetState')
    this.profileUploadDataSource.next(null)
    this.campaignDetailSubject.next(null)
    this.campaignSubject.next(null)
    this.saleContentCreate.next(null)
    this.lastProfileId.next(null)
    this.profileContentIsSoftDelete.next(null)
  }
}
