import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'
import { PATH_API } from '@features/campaign-voice-blaster/constants'
import { RevokeAssignModel } from '@features/campaign-voice-blaster/models/revoke-assign.model'
import { HttpClient } from '@angular/common/http'

@Injectable({
  providedIn: 'root'
})
export class CampaignService {
  constructor(
    private httpClient: HttpClientService,
    private http: HttpClient,
    private appService: AppService
  ) {}

  /**
   * tạo
   * @param body
   */
  create(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.CREATE_UPDATE,
      body
    }
    return this.httpClient.post(options)
  }

  saveProfile(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.SAVE_PROFILE,
      body
    }
    return this.http.post<any>(`${options.url}/${options.path}`, body, {
      headers: options.headers,
      observe: 'response',
      responseType: 'blob' as 'json',
      params: options.params ?? null
    })
  }

  // uploadFormData(options: HttpOptions, formData: FormData) {
  //   options.headers = options.headers ?? {}
  //   options.headers = {
  //     ...options.headers,
  //     clientMessageId: uuidv4()
  //   }
  //   return this.http.post<any>(`${options.url}/${options.path}`, formData, {
  //     headers: options.headers,
  //     observe: 'response',
  //     responseType: 'blob' as 'json',
  //     params: options.params ?? null
  //   })
  // }

  /**
   * sửa
   * @param body
   * @param id
   */
  update({ body, id }: { body?: any; id?: string }) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`,
      body
    }
    return this.httpClient.put(options)
  }

  patch({ body, id }: { body?: any; id?: string }) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`,
      body
    }
    return this.httpClient.patch(options)
  }

  /**
   * chi tiết
   * @param id
   */
  getDetail(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`
    }
    return this.httpClient.get(options)
  }

  /**
   * xóa
   * @param id
   */
  delete(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`
    }
    return this.httpClient.delete(options)
  }

  /**
   * getList
   * @param id
   */
  getListEmployee({
    page,
    size,
    filter,
    fetchAllCampaignStatistic
  }: {
    page: number
    size: number
    filter?: any
    fetchAllCampaignStatistic: boolean
  }) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.GETLIST_EMPLOYEE}`,
      params: {
        ...filter,
        includeTeamData: true,
        isActiveDCMS: 1,
        page: page,
        size: size,
        fetchAllCampaignStatistic: fetchAllCampaignStatistic,
        assignedProfileContent: true
      }
    }
    return this.httpClient.get(options)
  }

  assign(isDraft: boolean, body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.ASSIGN}`,
      params: {
        isDraft: isDraft
      },
      body: body
    }
    return this.httpClient.post(options)
  }
  revoke(body: RevokeAssignModel) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.REVOKE}`,
      body: body
    }
    return this.httpClient.put(options)
  }

  deleteProfileContentIds(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.DELETE_PROFILE_CONTENT,
      body
    }
    return this.httpClient.delete(options)
  }

  getProfile(params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.PROFILE}`,
      params
    }
    return this.httpClient.get(options)
  }

  pinCampaign(id: number, action: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.PIN}/${id}/pin`,
      params: {
        action: action // pin va unpin
      }
    }
    return this.httpClient.post(options)
  }

  runCampaign(id: number, timeoutMillisecond: number) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.RUN_CAMPAIGN}/${id}/run`,
      params: {
        useMergeWay: true,
        timeoutMillisecond: timeoutMillisecond // pin va unpin
      }
    }
    return this.httpClient.post(options)
  }

  updateStatus(body: any, id) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.UPDATE_STATUS}/${id}/status`,
      body: body
    }
    return this.httpClient.put(options)
  }
}
