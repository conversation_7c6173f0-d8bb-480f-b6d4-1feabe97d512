import { Routes } from '@angular/router'
import { RESOURCE_CODE, Scopes } from '@shared'
import { CampaignProfileComponent } from '@features/campaign-voice-blaster/pages'
import { ProfileImportComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/profile-import.component'
import { SaleContentsEmbedComponent } from '@features/campaign-voice-blaster/pages/sale-contents-embed/sale-contents-embed.component'
import { SaleCloseContentsCreateComponent } from '@features/sale-close-content/pages'
import { environment } from '@env/environment'


export const ROUTES_NAME_VOICE_BLASTER_CAMPAIGN = {
  LIST: environment.base_path + '/voice-blaster/campaign',
  EDIT: environment.base_path + '/voice-blaster/campaign/edit',
  COPY: environment.base_path + '/campaign/campaign/copy',
  ASSIGN: environment.base_path + '/campaign/campaign/assign',
  JOBPROFILE: environment.base_path + '/campaign/job-profile',
  CREATE_INTERACT: environment.base_path + '/campaign/interact/create',
  PROFILE_IN_CAMPAIGN: environment.base_path + '/campaign/profile-assigned/profile-in-campaign',
}

export const routes: Routes = [
  {
    path: '',
    title: 'Campaign voice blaster',
    loadComponent: async () => (await import('./pages/list/campaign-list.component')).CampaignListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  },
  {
    path: 'create',
    title: 'Thêm mới campaign voice blaster',
    loadComponent: async () => (await import('./pages/create/campaign-create.component')).CampaignCreateComponent,
    data: {
      breadcrumb: 'Thêm mới campaign voice blaster',
      showBreadcrumb: true,
    }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa campaign voice blaster',
    loadComponent: async () => (await import('./pages/edit/campaign-edit.component')).CampaignEditComponent,
    data: {
      breadcrumb: 'Chỉnh sửa campaign voice blaster',
      showBreadcrumb: true,
    }
  },
  // {
  //   path: 'copy/:id',
  //   title: 'Thêm mới campaign',
  //   loadComponent: async () => (await import('./pages/edit/campaign-edit.component')).CampaignEditComponent,
  //   data: {
  //     breadcrumb: 'Thêm mới campaign',
  //     showBreadcrumb: false
  //   }
  // },
  // {
  //   path: 'assign/:id',
  //   title: 'Phân giao',
  //   loadComponent: async () => (await import('./pages/assign/assign.component')).AssignComponent,
  //   data: {
  //     breadcrumb: 'Phân giao',
  //     showBreadcrumb: true
  //   }
  // },
  {
    path: ':type/:id',
    title: 'Xem chi tiết campaign',
    loadComponent: async () => (await import('./pages/detail/campaign-detail.component')).CampaignDetailComponent,
    data: {
      breadcrumb: 'Xem chi tiết campaign',
      showBreadcrumb: false
    }
  },
  {
    path: ':id',
    title: 'Xem chi tiết campaign',
    loadComponent: async () => (await import('./pages/detail/campaign-detail.component')).CampaignDetailComponent,
    data: {
      breadcrumb: 'Xem chi tiết campaign',
      showBreadcrumb: false,
    }
  },
  {
    path: ':id/customer',
    title: 'Xem chi tiết campaign',
    loadComponent: async () => (await import('./pages/detail/customer-list/customer-list.component')).CustomerListComponent,
    data: {
      breadcrumb: 'Xem chi tiết campaign',
      showBreadcrumb: false,
    }
  },
  {
    path: 'edit/info',
    title: 'Thông tin',
    loadComponent: async () =>
      (await import('@features/campaign-voice-blaster/pages/edit/campaign-edit-info/campaign-edit-info.component')).CampaignEditInfoComponent,
    data: { breadcrumb: 'Sửa info'}
  },
  {
    path: 'create/import-history',
    title: 'Thêm mới campaign',
    loadComponent: async () => (await import('@features/campaign-voice-blaster/pages/create/info/campaign-info.component')).CampaignInfoComponent,
    data: { breadcrumb: 'Thêm mới'}
  },
  {
    path: 'create/sale-content',
    title: 'NDCS',
    loadComponent: async () => (await import('./pages/sale-contents-embed/sale-contents-embed.component')).SaleContentsEmbedComponent,
    data: { breadcrumb: 'Thêm mới'}
  },
  {
    path: 'mapping',
    title: 'NDCS',
    loadComponent: async () =>
      (await import('./pages/profile/profile-import/form-mapping-data/form-mapping-data.component')).FormMappingDataComponent,
    data: { breadcrumb: 'Thêm mới'}
    // canActivate: [RoleGuard],
    // data: { resources: [RESOURCE_CODE.SETTING_REQUEST_ACCESS_APPROVE_LEVEL23], scopes: [Scopes.VIEW, Scopes.CREATE] }
  },
  {
    path: 'job-profile',
    title: 'Hồ sơ tác nghiệp',
    loadComponent: async () => (await import('../job-profile/pages/list/job-profile.component')).JobProfileComponent,
    data: {
      breadcrumb: '',
      resources: [],
      scopes: [Scopes.VIEW, Scopes.EDIT],
    }
  },
]
