import { Component, Injector } from '@angular/core'
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { TranslateModule } from '@ngx-translate/core'
import { NgIf } from '@angular/common'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { ImageComponent } from '@shared/components/section/image/image.component'
import { ComponentDialogAbstract } from '../../../../../shared/abstract/component-dialog.abstract'
import { ButtonOutlinePrimaryComponent } from '@shared/components/element/button-outline-primary/button-outline-primary.component'

@Component({
  selector: 'mbb-dialog-success',
  templateUrl: './dialog-call-method.component.html',
  styleUrls: ['./dialog-call-method.component.scss'],
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatDialogModule, ImageComponent, NgIf, TranslateModule, ImageComponent, ButtonOutlinePrimaryComponent]
})
export class DialogCallMethodComponent extends ComponentDialogAbstract {
  constructor(
    protected override injector: Injector,
    public dialogRef: MatDialogRef<DialogCallMethodComponent>
  ) {
    super(injector)
    this.dialogRef.disableClose = true
  }

  onCloseConfirm() {
    this.dialogRef.close(true)
  }

  onCloseCancel() {
    this.dialogRef.close(true)
  }

  onClickToCall() {
    this.dialogRef.close({ type: 1 })
  }
}
