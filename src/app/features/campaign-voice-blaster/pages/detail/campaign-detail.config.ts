import { Scopes } from '@shared'

export const configDetailCampaign = [
  {
    type: 'button',
    id: 'button-1',
    data: {
      title: 'Phân giao',
      type: 'ASSIGN',
      icon: 'ic-assign',
      scopes: [Scopes.VIEW]
    }
  },
  {
    type: 'button',
    id: 'button-2',
    data: {
      title: 'Chỉnh sửa',
      type: 'EDIT',
      icon: 'ic-edit',
      scopes: [Scopes.VIEW]
    }
  },
  {
    type: 'button',
    id: 'button-3',
    data: {
      title: 'Sao chép',
      type: 'COPY',
      icon: 'ic-copy',
      scopes: [Scopes.VIEW]
    }
  },
  {
    type: 'button',
    id: 'button-4',
    data: {
      title: 'Ghim Campaign',
      type: 'PIN',
      icon: 'ic-pin',
      scopes: [Scopes.VIEW]
    }
  },
  {
    type: 'button',
    id: 'button-5',
    data: {
      title: 'Bỏ ghim',
      type: 'UNPIN',
      icon: 'ic-unpin',
      scopes: [Scopes.VIEW]
    }
  },
  {
    type: 'button',
    id: 'button-6',
    data: {
      title: 'Xóa Campaign',
      type: 'DELETE',
      icon: 'ic-delete',
      scopes: [Scopes.VIEW]
    }
  }
]
