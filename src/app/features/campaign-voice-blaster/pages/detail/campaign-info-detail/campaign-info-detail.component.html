<div class="panel chart-panel" style='margin-bottom: 20px'>
  <div class="panel-body">
    <div class="d-flex flex-center-row align-items-center justify-content-between pdl-8 pdr-8 pdb-7 pdt-7" style='background-color: #EDF0FE'>
      <div>
        <div size="12px">Tên Campaign</div>
        <div class="title-page-detail">{{ campaignData?.name }}</div>
        <div size="12px">Mã Campaign</div>
        <div class="title-page-detail">{{ campaignData?.campaignId }}</div>
        <div [innerHTML]="getDisplayCampaignStatus(campaignData?.status, campaignData?.campaignType)"></div>
      </div>
      <app-donut-chart #chart [hidden]='!showChart'></app-donut-chart>
    </div>
  </div>
</div>
<div class="panel pd-4 blacklist-detail-content">
  <div class="d-flex flex-row flex-wrap">
<!--    <app-detail-row class="flex-100" label="Mã Campaign" [value]="campaignData?.campaignCode"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Tên Campaign" [value]="campaignData?.campaignName"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Trạng thái" [value]="getDisplayCampaignStatus(campaignData?.campaignStatus, campaignData?.campaignCode)" type="html"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Sản phẩm" [value]="campaignData?.productName"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Nghiệp vụ" [value]="campaignData?.businessName"></app-detail-row>-->
    <app-detail-row class="flex-50" label="Ngày bắt đầu" [value]="campaignData?.startDate | formatDateTime" type="text"></app-detail-row>
    <app-detail-row class="flex-50" label="Ngày kết thúc" [value]="campaignData?.endDate | formatDateTime" type="text"></app-detail-row>
<!--    <app-detail-row class="flex-50" label="Loại triển khai" [value]="getDisplayImplementType(campaignData?.implementType)"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Kịch bản gọi" [value]="campaignData?.callingScenario" type="link"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Đơn vị yêu cầu" [value]="campaignData?.departmentName"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Mức độ ưu tiên" [value]="campaignData?.priorityLevel" type="priority"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="% Active cần đạt được" [value]="campaignData?.activeRate"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="% Pickup đạt được" [value]="campaignData?.pickupRate"></app-detail-row>-->
<!--    <app-detail-row class="flex-50" label="Số lần gọi tối đa" [value]="campaignData?.maximumCallNumbers"></app-detail-row>-->
<!--    <app-detail-row class="flex-100" label="Mô tả" [value]="campaignData?.campaignDescription" type="text"></app-detail-row>-->
  </div>
</div>
