import { Component, EventEmitter, Injector, Output, ViewChild } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, formatDate } from '@shared'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { DetailCampaignModel } from '@features/campaign-voice-blaster/models/detail-campaign.model'
import { FlexModule } from '@angular/flex-layout'
import { DetailRowComponent } from '@shared/components/element/app-row-detail/detail-row.component'
import { CampaignEnumStatus, CampaignStatus, ImplementType } from '@features/campaign-voice-blaster/constants'
import { takeUntil } from 'rxjs'
import { DonutChartComponent } from '@features/campaign-voice-blaster/shared/pie-chart/donut-chart.component'
import { JsonPipe } from '@angular/common'
import { formatDateTime } from '../../../../../shared/pipe/change-date-time.pipe'

@Component({
  selector: 'campaign-info-detail',
  templateUrl: './campaign-info-detail.component.html',
  standalone: true,
  imports: [FlexModule, DetailRowComponent, formatDate, DonutChartComponent, JsonPipe, formatDateTime],
  styleUrls: ['./campaign-info-detail.component.scss']
})
export class CampaignInfoDetailComponent extends ComponentAbstract {
  @Output() switchTab = new EventEmitter<number>()
  @ViewChild('chart') chart: DonutChartComponent
  campaignData: any

  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
  }

  get showChart() {
    if (this.campaignData?.campaignType && Number(this.campaignData?.campaignType)) {
      if (Number(this.campaignData?.campaignType) === 1) {
        return Number(this.campaignData?.campaignStatus) !== 0
      }
      return false
    } else return false
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
    this._campaignBusinessLogicService.campaignDetail$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((campaignData) => {
      this.onLoadDetail(campaignData)
      if (this.chart) {
        this.chart.chartData1 = [
          {
            label: 'Hồ sơ đã phân giao',
            color: '#45d39d',
            value: this.campaignData?.assignedProfiles
          },
          {
            label: 'Hồ sơ chưa phân giao',
            color: '#FF7B2C',
            value: this.campaignData?.unassignedProfiles
          },
          {
            label: 'Hạn chế liên hệ',
            color: '#9aa1bc',
            value: this.campaignData?.blacklistProfiles
          }
        ]
        this.chart.createChart()
      }
    })
  }

  private onLoadDetail(campaignData: DetailCampaignModel) {
    if (campaignData) {
      this.campaignData = campaignData
      this.cdRef.detectChanges()
    }
  }

  getDisplayImplementType(value: any) {
    switch (value) {
      case ImplementType.NHAN_SU:
        return 'Nhân Sự'
      case ImplementType.BOT:
        return 'BOT'
      default:
        return
    }
  }

  getDisplayCampaignStatus(campaignStatus: any, campaignType: any) {
    switch (campaignStatus) {
      case CampaignStatus.NEW:
        return `<div class="app-status-dot"><span class="dot-status app-status-approved"></span> Mới</div>`
      case CampaignStatus.READY:
        return `<div class="app-status-dot"><span class="dot-status app-status-inprocess"></span> Hoạt động</div>`
      case CampaignStatus.COMPLETED:
        return `<div class="app-status-dot"><span class="dot-status app-status-reject"></span> Kết thúc</div>`
      default:
        return
    }
  }
}
