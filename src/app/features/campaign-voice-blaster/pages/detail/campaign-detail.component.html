<div class="d-flex flex-row justify-content-between mt-2 mb-2">
  <app-breadcrumb [forceShow]="true"></app-breadcrumb>
  <div class='d-flex flex-row justify-content-between'>
<!--    <div *ngFor="let item of configDetails">-->
<!--      <button *ngIf="conditionShowButton(item?.data?.type)" type="button" class="btn btn-md button-header pds-4 mrl-2" (click)="onClickButton(item?.data?.type)">-->
<!--        <mat-icon class="mbb-icon {{item.data.icon}} mrr-2"></mat-icon>-->
<!--        <span>{{item.data.title}}</span>-->
<!--      </button>-->
<!--    </div>-->
    <!-- <button type="button" class="btn btn-md button-header pds-4 mrl-2" (click)="onClickButton()">
      <mat-icon class="mbb-icon ic-pin mrr-2"></mat-icon>
      <span>Ghim Campaign</span>
    </button>
    <button type="button" class="btn btn-md button-header pds-4 mrl-2" (click)="onClickButton()">
      <mat-icon class="mbb-icon ic-delete mrr-2"></mat-icon>
      <span>Xóa campaign</span>
    </button> -->
  </div>
</div>
<mat-tab-group #tabGroup animationDuration="0ms" [(selectedIndex)]="selectedTabIndex" (selectedIndexChange)="selectedIndexChange($event)" class="custom-tab-bar">
  <mat-tab label="Thông tin chung" class="custom-tab">
    <div class="mrt-4">
      <campaign-info-detail #campaignInfoComponent></campaign-info-detail>
    </div>
  </mat-tab>
  <mat-tab label="Khách hàng" class="custom-tab">
    <div class="mrt-4">
      <campaign-profile #campaignProfileComponent type="view"></campaign-profile>
    </div>
  </mat-tab>
<!--  <mat-tab label="Nội dung chốt sale" class="custom-tab">-->
<!--    <div class="mrt-4">-->
<!--      <app-sale-content-embed #saleContentsEmbedComponent type="view" [isDetail]="true"></app-sale-content-embed>-->
<!--    </div>-->
<!--  </mat-tab>-->
<!--  <mat-tab label="Nhân sự triển khai" class="custom-tab" *ngIf="isShowTabEmployee()">-->
<!--    <div class="mrt-4">-->
<!--      <app-deployment-personal #deploymentPersonalComponent type="view"></app-deployment-personal>-->
<!--    </div>-->
<!--  </mat-tab>-->
</mat-tab-group>
<div class="d-flex footer-container" *ngIf='!isHideButton'>
  <mat-icon class="mbb-icon ic-angle_left fc-dark-blue" (click)="pageMonitorEvent(0)"></mat-icon>
  <app-form-footer class='mrb-12 mrl-4 mr-4' [isFixedBottom]="false" [listButton]="listButton" (eventClick)="onClickBtn($event)"></app-form-footer>
  <mat-icon class="mbb-icon ic-angle_right fc-dark-blue" (click)="pageMonitorEvent(1)"></mat-icon>
</div>
