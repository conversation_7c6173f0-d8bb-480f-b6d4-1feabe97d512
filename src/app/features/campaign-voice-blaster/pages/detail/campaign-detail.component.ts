import { Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core'
import { MatTabGroup, MatTabsModule } from '@angular/material/tabs'
import { BUTTON_ASSIGN, BUTTON_SAVE, BUTTON_TYPE_BACK, BUTTO<PERSON>_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, MessageSeverity, Status } from '@shared'
import { SaleCloseContentsCreateComponent } from '@features/sale-close-content/pages'
import { CampaignInfoComponent } from '@features/campaign-voice-blaster/pages/create/info/campaign-info.component'
import { CampaignProfileComponent } from '@features/campaign-voice-blaster/pages'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { SaleContentsEmbedComponent } from '@features/campaign-voice-blaster/pages/sale-contents-embed/sale-contents-embed.component'
import { RouterOutlet } from '@angular/router'
import { BreadcrumbComponent } from '@shared/components/navigation/breadcrumb/breadcrumb.component'
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common'
import { finalize, takeUntil } from 'rxjs'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { ROUTES_NAME_CAMPAIGN } from '../../../../app.routes'
import { ProfileImportComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/profile-import.component'
import { CampaignEditInfoComponent } from '@features/campaign-voice-blaster/pages/edit/campaign-edit-info/campaign-edit-info.component'
import { environment } from '@env/environment'
import { CampaignInfoDetailComponent } from '@features/campaign-voice-blaster/pages/detail/campaign-info-detail/campaign-info-detail.component'
import { DialogCallMethodComponent } from '@features/campaign-voice-blaster/pages/detail/dialog-call-method/dialog-call-method.component'
import { TemplateNameDirective } from '@mb/ngx-ui-builder'
import { configDetailCampaign } from './campaign-detail.config'
import { DetailCampaignModel } from '@features/campaign-voice-blaster/models/detail-campaign.model'
import { CustomerListComponent } from '@features/campaign-voice-blaster/pages/detail/customer-list/customer-list.component'

@Component({
  selector: 'campaign-edit',
  standalone: true,
  templateUrl: './campaign-detail.component.html',
  styleUrl: './campaign-detail.component.scss',
  imports: [
    MatTabsModule,
    CampaignProfileComponent,
    MatIcon,
    FooterComponent,
    BreadcrumbComponent,
    NgIf,
    CampaignInfoDetailComponent
  ],
  encapsulation: ViewEncapsulation.None
})
export class CampaignDetailComponent extends ComponentAbstract {
  @ViewChild('tabGroup', { static: false }) tabGroup: MatTabGroup
  @ViewChild('campaignInfoComponent') campaignInfoComponent: CampaignInfoComponent
  @ViewChild('saleContentsEmbedComponent') saleContentsEmbedComponent: SaleContentsEmbedComponent
  @ViewChild('campaignProfileComponent') campaignProfileComponent: CampaignProfileComponent
  selectedTabIndex = 0
  isHideButton = false
  campaignId: string
  campaignData: DetailCampaignModel
  action: any
  viewType: string
  configDetails = configDetailCampaign
  pin = false
  constructor(
    protected override injector: Injector,
    private _campaignService: CampaignService,
    private campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    // Đăng ký theo dõi Observable để nhận dữ liệu chiến dịch
    this.campaignBusinessLogicService.campaignDetail$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((data) => {
      this.campaignData = data
      this.pin = !!data?.pinnedBy
      this.updateLogicButton()
    })
  }

  componentInit(): void {
    this.updateLogicButton()
    this.campaignId = this.route.snapshot.paramMap.get('id')
    this.viewType = this.route.snapshot.paramMap.get('type')
    this.action = this.queryParams['action'] || 'update'

    if (this.campaignId) {
      this.getDetailCampaign()
    }
  }

  updateLogicButton() {
    // ham nay khong can quan tam cai ...button vi minh chi can clone 1 nut tu danh sach chung ra
    const type = this.getDisplayCampaignStatus(this.campaignData?.campaignStatus, this.campaignData?.campaignType)
    const buttonUpdate = {
      ...BUTTON_UPDATE,
      title: 'Chạy Campaign'
    }
    console.log('haizzz', type)
    const buttonComplete = {
      ...BUTTON_ASSIGN,
      title: 'Kết thúc campaign	'
    }
    const buttonReOpen = {
      ...BUTTON_SAVE,
      title: 'Mở lại campaign'
    }
    switch (type) {
      case 0:
        this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, buttonUpdate)
        break
      case 1:
        this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, buttonComplete)
        break
      case 2:
        this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, buttonReOpen)
        break
      case 3:
        this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK)
        break
      default:
        break
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_BACK.typeBtn:
        this.goTo(ROUTES_NAME_CAMPAIGN.LIST)
        break
      case BUTTON_UPDATE.typeBtn:
        this.onUpdateRun($event)
        break
      case BUTTON_ASSIGN.typeBtn:
        // ket thuc campaign
        this.onUpdateRun($event)
        break
      case BUTTON_SAVE.typeBtn:
        // mo lai campaign
        this.onUpdateRun($event)
        break
    }
  }

  onSaveDraft(number: number) {
    // this.onSave()
  }

  onSaveSuccess() {
    this.dialogService.confirm(
      {
        title: 'Chạy Campaign thành công',
        message: '',
        imageName: 'success.png',
        textButtonLeft: 'btn.close',
        textButtonRight: 'Phương thức gọi'
      },
      (result) => {
        if (result) {
          this.showDialogChooseMethod()
        } else {
          this.goTo(ROUTES_NAME_CAMPAIGN.LIST)
        }
      }
    )
  }

  showDialogChooseMethod() {
    this.dialogService.componentDialog(
      DialogCallMethodComponent,
      {
        minWidth: '374px',
        data: {}
      },
      (res) => {
        if (res?.type === 1) {
          this.goTo(`${ROUTES_NAME_CAMPAIGN.ASSIGN}/${this.campaignId}`)
        } else if (res?.type === 2) {
        }
      }
    )
  }
  disabledButtonRun() {
    const buttonUpdate = {
      ...BUTTON_UPDATE,
      title: 'Chạy Campaign',
      disabled: true
    }
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, buttonUpdate)
  }
  async onUpdateRun($event) {
    if ($event === BUTTON_UPDATE.typeBtn) {
      this.indicator.showActivityIndicator(true)
      this.disabledButtonRun()
      this._campaignService
        .patch({
          id: this.campaignId,
          body: {
            type: 'run'
          }
        })
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => {
            return this.indicator.hideActivityIndicator(true)
          })
        )
        .subscribe(
          (res: any) => {
            if (res?.code === Status.SUCCESS) {
              this.onSaveSuccess()
            } else {
              this.showDialogErrorI18n(res?.error?.message, `Chạy campaign thất bại`)
            }
          },
          (response) => {
            this.showDialogErrorI18n(response?.error?.message, `Chạy campaign thất bại`)
          }
        )
    } else if ($event === BUTTON_ASSIGN.typeBtn || $event === BUTTON_SAVE.typeBtn) {
      const event = $event == BUTTON_SAVE.typeBtn ? 'REOPEN' : 'COMPLETE'
      this.dialogService.confirm(
        {
          title: this.translateService.instant(
            event === 'REOPEN' ? 'Bạn có chắc chắn muốn mở lại Campaign?' : 'Bạn có chắc chắn muốn kết thúc Campaign?'
          )
        },
        (result) => {
          if (result) {
            this._campaignService
              .patch({
                id: this.campaignId,
                body: {
                  type: String(event || '').toLowerCase()
                }
              })
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => {
                  return this.indicator.hideActivityIndicator(true)
                })
              )
              .subscribe(
                (res: any) => {
                  if (res?.code === Status.SUCCESS) {
                    this.showDialogSuccessI18n('', `${this.messageSuccessAction(event)} campaign thành công`)
                    this.getDetailCampaign()
                  }
                },
                (response) => {
                  this.showDialogErrorI18n(response?.error?.message, `${this.messageSuccessAction(event)} campaign thất bại`)
                }
              )
          }
        }
      )
    }
  }

  selectedIndexChange($event: number) {
    // const  {index } = $event
    if ($event === 1) {
    } else if ($event === 2) {
      // this.saleContentsEmbedComponent.loadData()
    } else if ($event === 3) {
      // this.deploymentPersonalComponent.loadDataEdit(this.campaignId)
    }
  }

  pageMonitorEvent(type: number) {
    if (type === 0) {
      this.tabGroup.selectedIndex--
    } else {
      this.tabGroup.selectedIndex++
    }
  }

  isShowTabEmployee() {
    return Number(this.campaignData?.implementType || '0') !== 1
  }

  /**
   * get detail
   */
  getDetailCampaign() {
    this.indicator.showActivityIndicator(true)
    this._campaignService
      .getDetail(this.campaignId)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe((res: any) => {
        if (res?.code === Status.SUCCESS) {
          const data = res.data

          const dataEdit = {
            // campaignType: data?.campaignType,
            campaignDescription: data?.campaignDescription,
            productCode: {
              value: data?.productCode ? [data?.productCode] : undefined,
              paramData: {
                url: `${environment.services.pbxCms}/v1/service-category?serviceStatus=1`,
                queryStringOnInit: `productCode=${data?.productCode || ''}`
              }
            },
            businessCode: data?.businessCode,
            startDate: data?.startDate,
            endDate: data?.endDate,
            departmentCode: data?.departmentCode,
            priorityLevel: data?.priorityLevel,
            callingScenario: data?.callingScenario,
            activeRate: data?.activeRate,
            maximumCallNumbers: data?.maximumCallNumbers,
            pickupRate: data?.pickupRate,
            implementType: data?.implementType
          }

          // Neu la overwrite thi no se lay data moi nhat
          const detail = {
            ...data,
            // profiles: data.profiles.map((x) => {
            //   return {
            //     profileSetId: x.profileSetId
            //   }
            // })
          }
          this.campaignBusinessLogicService.updateCampaignDetail(detail)
          this.campaignBusinessLogicService.updateCampaign(detail)
          // setTimeout(() => {
          //   if (Number(data.campaignStatus) === CampaignStatus.NEW) {
          //     dataEdit['campaignStatus'] = {
          //       value: data.campaignStatus,
          //       options: [
          //         {
          //           key: '0',
          //           value: 'Mới'
          //         },
          //         {
          //           key: '1',
          //           value: 'Triển khai'
          //         }
          //       ]
          //     }
          //   } else if (Number(data.campaignStatus) === CampaignStatus.RUNNING) {
          //     dataEdit['campaignStatus'] = {
          //       value: data.campaignStatus,
          //       options: [
          //         {
          //           key: '1',
          //           value: 'Triển khai'
          //         },
          //         {
          //           key: '2',
          //           value: 'Kết thúc'
          //         }
          //       ]
          //     }
          //   }
          //   this.logger(dataEdit, 'data Edit')
          //   this.campaignInfoComponent.patchValuesFormBuilder(this.transformObject(dataEdit))
          // }, 300)
        } else {
          this.showErrorGetData()
        }
      })
  }

  onClickButton(type?: string) {
    console.log('event CC', type)
    switch (type) {
      case 'ASSIGN':
        if (Number(this.campaignId || 0) === 0) {
          this.showDialogChooseMethod()
        } else {
          this.goTo(`${ROUTES_NAME_CAMPAIGN.ASSIGN}/${this.campaignId}`)
        }
        break
      case 'EDIT':
        this.goTo(`${ROUTES_NAME_CAMPAIGN.EDIT}/${this.campaignId}`)
        break
      case 'COPY':
        this.goTo(`${ROUTES_NAME_CAMPAIGN.COPY}/${this.campaignId}`, { action: type?.toLowerCase() })
        break
      case 'PIN':
        this.pinCampaign(type)
        break
      case 'UNPIN':
        this.pinCampaign(type)
        break
      case 'DELETE':
        this.dialogService.confirm(
          {
            title: this.translateService.instant('Bạn chắc chắn muốn xóa Campaign?'),
            textButtonRight: 'btn.delete'
          },
          (result) => {
            if (result) {
              this.deleteCampaign()
            }
          }
        )
      default:
        break
    }
  }
  deleteCampaign() {
    this._campaignService
      .delete(this.campaignId)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          return this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe({
        next: (result: any) => {
          if (result?.code === Status.SUCCESS) {
            this.dialogService.success(
              {
                title: `Xóa Campaign thành công`
              },
              () => {
                this.location.back()
              }
            )
          }
        },
        error: (response) => {
          const err = response.error
          this.showDialogErrorI18n(err?.message, `Xóa Campaign thất bại`)
        }
      })
  }
  pinCampaign(type: string) {
    const action = type === 'PIN' ? 'pin' : 'unpin'
    const pinStatusMessage = type === 'PIN' ? 'Ghim' : 'Bỏ ghim'
    this._campaignService
      .pinCampaign(Number(this.campaignId), action)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          return this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe({
        next: (result: any) => {
          if (result?.code === Status.SUCCESS) {
            this.pin = !this.pin
            this.toastr.showToastri18n(result?.data?.message ?? `${pinStatusMessage} campaign thành công`, '', MessageSeverity.success)
          }
        },
        error: (response) => {
          this.toastr.showToastri18n(response?.data?.message ?? `${pinStatusMessage} campaign thất bại`, '', MessageSeverity.error)
        }
      })
  }
  getDisplayCampaignStatus(campaignStatus: any, campaignType: any) {
    if (Number(campaignType) !== 0) {
      // Neu la 3 trang thai: moi/trien khai/ket thuc thi tra ra: 0/1/2
      return Number(campaignStatus)
    } else if (Number(campaignType) === 0) {
      // Neu la ban ghi nhap tra ra 3
      return 3
    } else {
      return
    }
  }
  conditionShowButton(type?: string) {
    const status = this.getDisplayCampaignStatus(this.campaignData?.campaignStatus, this.campaignData?.campaignType)
    switch (type) {
      case 'ASSIGN':
        return status === 1
      case 'EDIT':
        return status !== 2
      case 'COPY':
        return status !== 3
      case 'PIN':
        return !this.pin
      case 'UNPIN':
        return this.pin
      case 'DELETE':
        return status === 0 || status === 3
      default:
        break
    }
  }
  messageSuccessAction(type: string): string {
    switch (type) {
      case 'REOPEN':
        return 'Mở lại'
      case 'COMPLETE':
        return 'Kết thúc'
      default:
        return ''
    }
  }
}
