import { Component, Injector, Input, ViewChild } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { environment } from '@env/environment'

@Component({
  selector: 'app-customer-campaign-voice-blaster-list',
  templateUrl: './customer-list.component.html',
  standalone: true,
  imports: [
    AppTableBuilderComponent
  ],
  styleUrls: ['./customer-list.component.scss']
})
export class CustomerListComponent extends ComponentAbstract {
  @Input() campaignId: string
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent
  type: string = 'ALL'
  idComponent = 'management-customer-campaign-voice-blaster-list'

  constructor(protected override injector: Injector) {
    super(injector)
    this.campaignId = this.route.snapshot.paramMap.get('id')
    this.loadTable()
  }

  componentInit(): void {
  }

  loadTable() {
    this.initBuilderUIConfig(this.idComponent, true, true).then((r) => {
      setTimeout(() => {
        this.tableBuilderComponent.panelOpenState = true
        this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true
        this.tableBuilderComponent._appTableBodyComponent.configComponent.data.apiList = `${environment.services.pbxCms}/v1/voice-blaster-campaign/${this.campaignId}/call-stat?type=${this.type}`
        this.tableBuilderComponent.searchAdvanced(0)
      }, 1000)
    })
  }

  onTableActionClick($event: any) {
    console.log($event)

    switch ($event.type) {
      case 'cellClick': {
        // this.goTo(`${ROUTES_NAME_CUSTOMER.VIEW}/${$event?.row?.customerCode}`)
        break
      }
      default:
        break
    }
  }
  actionContextMenuClick($event) {
    switch ($event.type) {
      case 0: {
        // this.openRouterNewTab(`${ROUTES_NAME_CUSTOMER.VIEW}/${$event?.row?.customerCode}`)
        break
      }
      default:
        break
    }
  }
  onButtonClick($event: any) {
    console.log($event)
  }
}
