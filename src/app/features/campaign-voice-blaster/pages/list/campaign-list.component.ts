import { Component, Injector, ViewChild } from '@angular/core'
import { ComponentAbstract, Status } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { NgForOf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { finalize, takeUntil } from 'rxjs'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { ROUTES_NAME_CAMPAIGN } from '../../../../app.routes'
import { DialogCallMethodComponent } from '@features/campaign-voice-blaster/pages/detail/dialog-call-method/dialog-call-method.component'
import { CampaignStatus } from '@features/campaign-voice-blaster/constants'

@Component({
  selector: 'app-campaign-list',
  templateUrl: './campaign-list.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent
  ],
  styleUrls: ['./campaign-list.component.scss']
})
export class CampaignListComponent extends ComponentAbstract {
  @ViewChild('builderChild') builderChild: AppTableBuilderComponent

  constructor(
    protected override injector: Injector,
    private _campaignService: CampaignService
  ) {
    super(injector)
    this.initBuilderUIConfig('management-campaign-voice-blaster-list').then((r) => {})
    // this.initBuilderUIConfigLocal('management-product-list').then((r) => {
    //   this.config = r
    // })
  }

  componentInit(): void {}

  // onBuilderChanged($event: any) {
  //   this.logger($event, 'formChanged')
  //   if ($event.type == 'formData') {
  //     if ($event.event?.data && Array.isArray($event.event?.data) && $event.event?.data[0]?.data?.productCode) {
  //       const dataEdit = {
  //         businessCode: new TreeDropdownItem({
  //           value: undefined,
  //           required: false,
  //           paramData: {
  //             url: `${environment.services.pbxCms}/v1/user-category/${$event.event.data[0].data?.productCode}/used-businesses`
  //           }
  //         })
  //       }
  //       this.patchValuesFormBuilder(dataEdit)
  //     }
  //   }
  // }

  onTableActionClick($event: any) {
    console.log('$event$event$event', $event)
    const id = $event?.row?.campaignId
    const event = String($event?.type || '').toUpperCase()
    if (event === 'REOPEN' || $event?.type === 'COMPLETE') {
      this.dialogService.confirm(
        {
          title: this.translateService.instant('dialog.confirm'),
          message: this.translateService.instant(event === 'REOPEN' ? 'Bạn có muốn mở lại Campaign' : 'Bạn có muốn kết thúc Campaign')
        },
        (result) => {
          if (result) {
            this._campaignService.updateStatus({ status: CampaignStatus.COMPLETED }, id)
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => {
                  this.builderChild.searchAdvanced(this.builderChild.pageIndex)
                  return this.indicator.hideActivityIndicator(true)
                })
              )
              .subscribe(
                (res: any) => {
                  if (res?.code === Status.SUCCESS) {
                    this.showDialogSuccessI18n('', `${this.messageSuccessAction($event.type)} campaign thành công`)
                  }
                },
                (response) => {
                  this.showDialogErrorI18n(response?.error?.message, `${this.messageSuccessAction($event.type)} campaign thất bại`)
                }
              )
          }
        }
      )
    } else if (event === 'COPY') {
      this.goTo(`${ROUTES_NAME_CAMPAIGN.COPY}/${id}`, { action: $event.type })
      // this.goTo(`${ROUTES_NAME_CAMPAIGN.LIST}/preview/117`)
    } else if (event === 'RUN') {
      this.dialogService.confirm(
        {
          title: this.translateService.instant('dialog.confirm'),
          message: 'Bạn có muốn triển khai chiến dịch?'
        },
        (result) => {
          if (result) {
            this._campaignService
              .updateStatus({ status: CampaignStatus.READY }, id)
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => {
                  this.builderChild.searchAdvanced(this.builderChild.pageIndex)
                  return this.indicator.hideActivityIndicator(true)
                })
              )
              .subscribe(
                (res: any) => {
                  if (res?.code === Status.SUCCESS) {
                    this.showDialogSuccessI18n('', `Chiến dịch đang bắt đầu triển khai`)
                  }
                },
                (response) => {
                  this.showDialogErrorI18n(response?.error?.message, `Triển khai chiến dịch thất bại`)
                }
              )
          }
        }
      )

    } else if (event === 'CELLCLICK') {
      this.goTo(`${ROUTES_NAME_CAMPAIGN.LIST}/detail/${id}`)
    }
  }

  showDialogChooseMethod(campaignId: any) {
    this.dialogService.componentDialog(
      DialogCallMethodComponent,
      {
        minWidth: '374px',
        data: {}
      },
      (res) => {
        if (res?.type === 1) {
          this.goTo(`${ROUTES_NAME_CAMPAIGN.ASSIGN}/${campaignId}`)
        } else if (res?.type === 2) {
          //TODO
        }
      }
    )
  }

  onButtonClick($event: any) {
    console.log($event)
  }
  messageSuccessAction(type: string): string {
    switch (type) {
      case 'REOPEN':
        return 'Mở lại'
      case 'COMPLETE':
        return 'Kết thúc'
      default:
        return ''
    }
  }
  actionContextMenuClick($event) {
    const id = $event?.row?.campaignId
    switch ($event.type) {
      case 0: {
        this.openRouterNewTab(`${ROUTES_NAME_CAMPAIGN.LIST}/detail/${id}`)
        break
      }
      default:
        break
    }
  }
}
