.btn-toggle-menu {
  background-color: #E6E8EE;
  width: 24px!important;
  height: 24px!important;
  border-radius: 50%;
  padding: 0!important;
  border: 1px solid #e2e2e2;
  position: absolute;
  z-index: 9999;
  &:hover {
    background-color: #d8deef;
  }
  &.left {
    right: -18px;
    top: 27px;
    z-index: 9999;
  }
  &.right {
    left: 14px;
    top: 27px;
  }
}


.panel-toggle {
  transition: width 150ms ease-out;
  &.open {
    width: 100%;
    .panel {
      display: flex;
    }
  }
  &.closed {
    width: 0!important;
    .panel {
      display: none;
    }
  }
}
