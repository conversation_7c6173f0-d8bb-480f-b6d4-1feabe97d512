import { Component, EventEmitter, Injector, Output } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, TreeDropdownItem } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { CampaignEnumStatus, CampaignType } from '@features/campaign-voice-blaster/constants'
import { environment } from '@env/environment'
import { DetailCampaignModel } from '@features/campaign-voice-blaster/models/detail-campaign.model'
import { firstValueFrom, takeUntil } from 'rxjs'

@Component({
  selector: 'campaign-edit-info',
  templateUrl: './campaign-edit-info.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon],
  styleUrls: ['./campaign-edit-info.component.scss']
})
export class CampaignEditInfoComponent extends ComponentAbstract {
  @Output() switchTab = new EventEmitter<number>()
  id = 'voice-blaster-campaign-edit'
  action = ''
  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.initBuilderUIConfig(this.id, true).then((r) => {})
    //  this.configTable = configComponentCampaignInfo
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
    this.action = this.queryParams['action']
    this._campaignBusinessLogicService.campaignDetail$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((campaignData) => {
      console.log('this._campaignBusinessLogicService.campaignDetail$', campaignData)
      if (this.action !== 'copy') {
        // this.updateHiddenItemBuilder('campaignStatus', false)
        this.onLoadDetail(campaignData)
      } else if (this.action === 'copy') {
        // this.updateHiddenItemBuilder('campaignStatus', true)
      }
    })
  }

  onBuilderChanged({ type, event }) {
    this.logger(type, 'formChanged')
    if (type == 'formData') {
      this._campaignBusinessLogicService.updateCampaign(this._page.formRawValue)
      this.handleEvent(type, event)
    }
  }

  async handleEvent(type, event) {
    if (type === 'formData') {
      switch (event.item.key) {
        case 'implementType':
          if (event.data.key === '1') {
          }
          break
        case 'productCode':
          if (event?.data && Array.isArray(event?.data) && event?.data[0]?.data?.productCode) {
            const productCode = event?.data[0]?.data?.productCode
            const campaignData = await firstValueFrom(this._campaignBusinessLogicService.campaignDetail$)
            const defaultVal = campaignData.productCode == productCode ? campaignData.businessCode : undefined
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: defaultVal,
                required: true,
                readOnly: this.action !== 'copy' ? Number(campaignData.campaignStatus) === CampaignEnumStatus.RUNNING : false,
                paramData: {
                  url: `${environment.services.pbxCms}/v1/business-category/used-tree?productCode=${productCode}`
                }
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          } else {
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: undefined,
                required: false,
                readOnly: true
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          }
          break
      }
    }
  }

  validateForm() {
    this.validateAllFields(this._page.form)
  }

  initValidateDate() {
    this.elementService
      .checkFormControlsExist(this._page.form, ['startDate', 'endDate'])
      .then(() => {
        this.itemControl.validateStartDateMustBeSameOrBeforeEndDate(this._page.form, 'startDate', 'endDate')
      })
      .catch((error) => {
        console.error('Đã có lỗi xảy ra:', error)
      })
  }

  private onLoadDetail(campaignData: DetailCampaignModel) {
    if (campaignData) {
      // unlock all
      // const editFields = {
      //   campaignDescription: { readOnly: false },
      //   productCode: { readOnly: false },
      //   businessCode: { readOnly: campaignData?.productCode ? false : true },
      //   startDate: { readOnly: false },
      //   endDate: { readOnly: false },
      //   departmentCode: { readOnly: false },
      //   priorityLevel: { readOnly: false },
      //   callingScenario: { readOnly: false },
      //   activeRate: { readOnly: false },
      //   maximumCallNumbers: { readOnly: false },
      //   pickupRate: { readOnly: false },
      //   implementType: { readOnly: false }
      // }
      // this.updateConfigsFormBuilder(editFields)
      // this.updateHiddenItemBuilder('campaignStatus', false)
      //
      // if (Number(campaignData.campaignType) == CampaignType.DRAFT) {
      //   this.updateHiddenItemBuilder('campaignStatus', true)
      // } else {
      //   const readOnlyFields = {
      //     campaignDescription: { readOnly: true },
      //     productCode: { readOnly: true },
      //     businessCode: { readOnly: true },
      //     startDate: { readOnly: true },
      //     endDate: { readOnly: true },
      //     departmentCode: { readOnly: true },
      //     priorityLevel: { readOnly: true },
      //     callingScenario: { readOnly: true },
      //     activeRate: { readOnly: true },
      //     maximumCallNumbers: { readOnly: true },
      //     pickupRate: { readOnly: true },
      //     implementType: { readOnly: true }
      //   }
      //
      //   if (Number(campaignData.campaignStatus) === CampaignEnumStatus.RUNNING) {
      //     this.updateConfigsFormBuilder(readOnlyFields)
      //   }
      //   if (Number(campaignData.campaignStatus) === CampaignEnumStatus.COMPLETED) {
      //     this.updateConfigsFormBuilder({
      //       ...readOnlyFields,
      //       campaignStatus: { readOnly: true }
      //     })
      //   }
      // }
    }
  }
}
