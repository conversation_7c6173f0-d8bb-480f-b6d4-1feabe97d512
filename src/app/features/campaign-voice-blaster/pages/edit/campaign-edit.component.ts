import { Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core'
import { MatTabGroup, MatTabsModule } from '@angular/material/tabs'
import { BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, MessageSeverity, Status } from '@shared'
import { CampaignProfileComponent } from '@features/campaign-voice-blaster/pages'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { NgIf } from '@angular/common'
import { finalize, firstValueFrom, takeUntil } from 'rxjs'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { ROUTES_NAME_CAMPAIGN } from '../../../../app.routes'
import { ReqCreateCampaign } from '@features/campaign-voice-blaster/models/campaign.model'
import { CampaignEditInfoComponent } from '@features/campaign-voice-blaster/pages/edit/campaign-edit-info/campaign-edit-info.component'
import { environment } from '@env/environment'
import { ROUTES_NAME_VOICE_BLASTER_CAMPAIGN } from '@features/campaign-voice-blaster/campaign.routes'
import moment from 'moment'
import { SaleContentsEmbedComponent } from '@features/campaign/pages'

@Component({
  selector: 'campaign-edit',
  standalone: true,
  templateUrl: './campaign-edit.component.html',
  styleUrl: './campaign-edit.component.scss',
  imports: [MatTabsModule, CampaignEditInfoComponent, FooterComponent, NgIf],
  encapsulation: ViewEncapsulation.None
})
export class CampaignEditComponent extends ComponentAbstract {
  @ViewChild('tabGroup', { static: false }) tabGroup: MatTabGroup
  @ViewChild('campaignInfoComponent') campaignInfoComponent: CampaignEditInfoComponent
  @ViewChild('saleContentsEmbedComponent') saleContentsEmbedComponent: SaleContentsEmbedComponent
  @ViewChild('campaignProfileComponent') campaignProfileComponent: CampaignProfileComponent
  currentProfileTab = 'list' // list, import
  currentSaleContentTab = 'list' // list, create
  selectedTabIndex = 0
  isHideButton = false
  campaignId
  campaignData: ReqCreateCampaign
  action = ''
  isValidTabInfo: any
  isValidTabProfile: any
  isValidTabSaleContent: any
  isValidTabAgent: any
  constructor(
    protected override injector: Injector,
    private _service: CampaignService,
    private campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.campaignBusinessLogicService.updateCampaign(null)
    this.campaignBusinessLogicService.resetState()
  }

  componentInit(): void {
    this.updateLogicButton()
    this.campaignId = this.route.snapshot.paramMap.get('id')
    this.action = this.queryParams['action'] || 'update'
    if (this.campaignId) {
      this.getDetailCampaign()
    }
    // Đăng ký theo dõi Observable để nhận dữ liệu chiến dịch
    this.campaignBusinessLogicService.campaign$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((data) => {
      console.log('camp data edit', data)
      this.campaignData = data
      this.updateLogicButton()
    })

    // this.campaignBusinessLogicService.buttonClick$.subscribe((data) => {
    //   console.log('log button click', data)
    //   if (data) {
    //     if (data.type === 'profile') {
    //       this.currentProfileTab = data.value
    //     }
    //     if (data.type === 'sale-content') {
    //       this.currentSaleContentTab = data.value
    //     }
    //     this.checkIfChildRoute()
    //   }
    // })
  }

  override ngOnDestroy() {
    super.ngOnDestroy()
    this.campaignBusinessLogicService.resetState()
  }

  updateLogicButton() {
    const buttonUpdate = {
      ...BUTTON_UPDATE,
      title: this.campaignData?.campaignType === 0 || this.isActionCopy() ? 'Lưu' : 'Cập nhật'
    }
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, buttonUpdate)
  }
  checkIfChildRoute() {
    setTimeout(() => {
      if (this.tabGroup) {
        // Kiểm tra xem current route có phải là router phụ hay không
        //TODO: refactor
        this.isHideButton =
          (this.currentProfileTab === 'import' && this.tabGroup.selectedIndex === 1) ||
          (this.currentSaleContentTab === 'create' && this.tabGroup.selectedIndex === 2)
        // console.log('Current Tab Index:', this.selectedTabIndex)
      }
    })
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onCancel()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave(1)
        break
    }
  }

  onCancel() {
    this.goTo(ROUTES_NAME_CAMPAIGN.LIST)
  }

  onSaveDraft(number: number) {
    this.onSave(number)
  }

  validateAllTab(campaignData: ReqCreateCampaign) {
    this.campaignInfoComponent.validateForm()
    this.isValidTabInfo = this.campaignInfoComponent._page?.form.valid || false
    return this.isValidTabInfo
    // this.isValidTabProfile = !_.isEmpty(campaignData?.profiles || [])
    // this.isValidTabSaleContent = !_.isEmpty(campaignData?.saleCloseContents || [])
    // this.isValidTabAgent = !_.isEmpty(campaignData?.implementAgents || [])
    // if (!this.isValidTabInfo || !this.isValidTabProfile || !this.isValidTabSaleContent || !this.isValidTabAgent) {
    //   this.toastr.showToastri18n('Chưa nhập đủ thông tin', '', MessageSeverity.error)
    //   return false
    // }
    // return true
  }

  async onSave(campaignType: number) {
    const campaignData = await firstValueFrom(this.campaignBusinessLogicService.campaign$)
    const body = {
      ...campaignData,
      sources: [
        {
          customerSourceId: campaignData?.sourceId
        }
      ]
    }
    // save
    if (this.validateAllTab(campaignData)) {
      this.onUpdateData(body)
    }
  }

  onCreateData(body: ReqCreateCampaign) {
    this.indicator.showActivityIndicator()
    this._service
      .create({
        ...body,
        implementAgents: body.implementAgents.map((x) => {
          return {
            agentEmployeeId: x.agentEmployeeId
          }
        })
      })
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            const { campaignType, campaignId } = res?.data
            this.form.reset()
            if (Number(campaignType) === 1) {
              this.toastr.showToastri18n('Dữ liệu khai báo đã được kiểm tra thành công', '', MessageSeverity.success)
              this.goTo(`${ROUTES_NAME_CAMPAIGN.LIST}/detail/${campaignId}`)
            } else {
              this.goTo(ROUTES_NAME_CAMPAIGN.LIST)
              this.toastr.showToastri18n(`Thêm mới bản nháp ${res.data.campaignName} thành công!`, '', MessageSeverity.success)
            }
          }
        },
        (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Sao chép campaign <br/>thất bại')
        }
      )
  }

  onUpdateData(body: any) {
    // this.campaignProfileComponent.table.callApiDelete()
    this.indicator.showActivityIndicator()
    this._service
      .update({
        id: this.campaignId,
        body: body
      })
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.form.reset()
            this.goTo(ROUTES_NAME_VOICE_BLASTER_CAMPAIGN.LIST)
            this.showDialogSuccessI18n('', `Chỉnh sửa chiến dịch ${res.data?.name} <br/>thành công!`)
          }
        },
        (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Sửa campaign <br/>thất bại')
        }
      )
  }

  selectedIndexChange($event: number) {
    // const  {index } = $event
    if ($event === 1) {
    } else if ($event === 2) {
      this.saleContentsEmbedComponent.loadData()
    } else if ($event === 3) {
      // this.deploymentPersonalComponent.loadDataEdit(this.campaignId)
      // this.deploymentPersonalComponent.defaultFilter(this.campaignData.agentConfig)
    }
    // this.checkIfChildRoute()
  }

  pageMonitorEvent(type: number) {
    if (type === 0) {
      this.tabGroup.selectedIndex--
    } else {
      this.tabGroup.selectedIndex++
    }
  }

  isShowTabEmployee() {
    return Number(this.campaignData?.implementType || '0') !== 1
  }

  /**
   * get detail
   */
  getDetailCampaign() {
    this.indicator.showActivityIndicator(true)
    this._service
      .getDetail(this.campaignId)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe((res: any) => {
        if (res?.code === Status.SUCCESS) {
          if (!res?.data) {
            this.goTo404()
            return
          }
          const data = res.data

          const dataEdit = {
            name: data?.name,
            scriptId: data?.scriptId,
            // sourceId: data?.sources[0]?.customerSourceId,
            // campaignType: data?.campaignType,
            // campaignDescription: data?.campaignDescription,
            sourceId: {
              value: data?.sources[0]?.customerSourceId ? data?.sources[0]?.customerSourceId : undefined,
              paramData: {
                url: `${environment.services.pbxCms}/customer-source`,
                queryStringOnInit: `sourceId=${data?.sources[0]?.customerSourceId || ''}`,
                preLoad: true
              }
            },
            description: data?.description,
            startDate: data?.startDate ? moment(data?.startDate) : undefined,
            endDate: data?.endDate ? moment(data?.endDate) : undefined,
            maxRetryAttempts: data?.maxRetryAttempts
            // departmentCode: data?.departmentCode,
            // priorityLevel: data?.priorityLevel,
            // callingScenario: data?.callingScenario,
            // activeRate: data?.activeRate,
            // maximumCallNumbers: data?.maximumCallNumbers,
            // pickupRate: data?.pickupRate,
            // implementType: data?.implementType
          }
          // const profiles = (data?.profiles || []).map((x) => {
          //   return {
          //     profileSetId: x.profileSetId
          //   }
          // })
          const detail = {
            ...data
            // profiles: this.isActionCopy() ? [] : profiles
          }

          // this.campaignBusinessLogicService.updateLastProfile(this.getLastProfile(profiles))
          this.campaignBusinessLogicService.updateCampaignDetail(detail)
          this.campaignBusinessLogicService.updateCampaign(detail)

          setTimeout(() => {
            //   if (Number(data.campaignStatus) === CampaignEnumStatus.NEW) {
            //     dataEdit['campaignStatus'] = {
            //       value: data.campaignStatus,
            //       options: [
            //         {
            //           key: '0',
            //           value: 'Mới'
            //         },
            //         {
            //           key: '1',
            //           value: 'Triển khai'
            //         }
            //       ]
            //     }
            //   } else if (Number(data.campaignStatus) === CampaignEnumStatus.RUNNING) {
            //     dataEdit['campaignStatus'] = {
            //       value: data.campaignStatus,
            //       options: [
            //         {
            //           key: '1',
            //           value: 'Triển khai'
            //         },
            //         {
            //           key: '2',
            //           value: 'Kết thúc'
            //         }
            //       ]
            //     }
            //   }
            //   this.logger(dataEdit, 'data Edit')
            this.campaignInfoComponent.patchValuesFormBuilder(this.transformObject(dataEdit))
            this.campaignInfoComponent.initValidateDate()
          }, 300)
        } else {
          this.showErrorGetData()
        }
      })
  }

  getLastProfile(profiles = []) {
    if (profiles && profiles.length > 0) {
      return profiles[profiles.length - 1]
    }
    return null
  }
  isActionCopy() {
    return this.action?.toLowerCase() === 'copy'
  }
}
