import { Component, EventEmitter, Injector, Output } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, TreeDropdownItem } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { environment } from '@env/environment'

@Component({
  selector: 'campaign-info',
  templateUrl: './campaign-info.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon],
  styleUrls: ['./campaign-info.component.scss']
})
export class CampaignInfoComponent extends ComponentAbstract {
  @Output() switchTab = new EventEmitter<number>()
  id = 'voice-blaster-campaign-create'
  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.initBuilderUIConfig(this.id, true).then((r) => {
      this.initDataConfig()
    })
    //  this.configTable = configComponentCampaignInfo
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
  }

  onBuilderChanged({ type, event }) {
    this.logger(type, 'formChanged')
    if (type == 'formData') {
      this._campaignBusinessLogicService.updateCampaign(this._page.formRawValue)
      this.handleEvent(type, event)
    }
  }

  handleEvent(type, event) {
    console.log('***** DEBUG', type, event)
    if (type === 'formData') {
      switch (event.item.key) {
        case 'implementType':
          if (event.data.key === '1') {
          }
          break
        case 'productCode':
          if (event?.data && Array.isArray(event?.data) && event?.data[0]?.data?.productCode) {
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: undefined,
                required: true,
                readOnly: false,
                paramData: {
                  url: `${environment.services.pbxCms}/v1/business-category/used-tree?productCode=${event.data[0].data?.productCode}`
                }
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          } else {
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: undefined,
                required: false,
                readOnly: true
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          }
          break
      }
    }
  }

  validateForm() {
    this.validateAllFields(this._page.form)
  }

  initValidateDate() {
    this.elementService
      .checkFormControlsExist(this._page.form, ['startDate', 'endDate'])
      .then(() => {
        this.itemControl.validateStartDateMustBeSameOrBeforeEndDate(this._page.form, 'startDate', 'endDate')
      })
      .catch((error) => {
        console.error('Đã có lỗi xảy ra:', error)
      })
  }

  private initDataConfig() {
    const fields = {
      startDate: {
        value: new Date()
      }
    }
    this.patchValuesFormBuilder(fields)
    this.initValidateDate()
  }
}
