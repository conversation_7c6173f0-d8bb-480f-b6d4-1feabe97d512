import { Component, Injector, ViewChild, ViewEncapsulation } from '@angular/core'
import { MatTabGroup, MatTabsModule } from '@angular/material/tabs'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, MessageSeverity, Status } from '@shared'
import { CampaignInfoComponent } from '@features/campaign-voice-blaster/pages/create/info/campaign-info.component'
import { CampaignProfileComponent } from '@features/campaign-voice-blaster/pages'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { NgIf } from '@angular/common'
import { finalize, firstValueFrom, takeUntil } from 'rxjs'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { ReqCreateCampaign } from '@features/campaign-voice-blaster/models/campaign.model'
import { ROUTES_NAME_VOICE_BLASTER_CAMPAIGN } from '@features/campaign-voice-blaster/campaign.routes'
import { DeploymentPersonalComponent } from '@features/campaign/pages'

@Component({
  selector: 'campaign-create',
  standalone: true,
  templateUrl: './campaign-create.component.html',
  styleUrl: './campaign-create.component.scss',
  imports: [
    MatTabsModule,
    CampaignInfoComponent,
    FooterComponent,
    NgIf
  ],
  encapsulation: ViewEncapsulation.None
})
export class CampaignCreateComponent extends ComponentAbstract {
  @ViewChild('tabGroup', { static: false }) tabGroup: MatTabGroup
  @ViewChild('deploymentPerson') deploymentPerson: DeploymentPersonalComponent
  @ViewChild('campaignInfoComponent') campaignInfoComponent: CampaignInfoComponent
  @ViewChild('campaignProfile') campaignProfile: CampaignProfileComponent
  currentProfileTab = 'list' // list, import
  currentSaleContentTab = 'list' // list, create
  selectedTabIndex = 0
  isHideButton = false
  campaignId
  campaignData: ReqCreateCampaign
  isValidTabInfo: any
  isValidTabProfile: any
  isValidTabSaleContent: any
  isValidTabAgent: any
  constructor(
    protected override injector: Injector,
    private _service: CampaignService,
    private campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.campaignBusinessLogicService.updateCampaign(null)
    this.campaignBusinessLogicService.resetState()
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
    // Đăng ký theo dõi Observable để nhận dữ liệu chiến dịch
    this.campaignBusinessLogicService.campaign$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((data) => {
      console.log('camp data', data)
      this.campaignData = data
    })

    this.campaignBusinessLogicService.buttonClick$.subscribe((data) => {
      console.log('log button click', data)
      if (data) {
        if (data.type === 'profile') {
          this.currentProfileTab = data.value
        }
        if (data.type === 'sale-content') {
          this.currentSaleContentTab = data.value
        }
        this.checkIfChildRoute()
      }
    })
  }

  override ngOnDestroy() {
    super.ngOnDestroy()
    this.campaignBusinessLogicService.resetState()
  }

  checkIfChildRoute() {
    setTimeout(() => {
      if (this.tabGroup) {
        // Kiểm tra xem current route có phải là router phụ hay không
        //TODO: refactor
        this.isHideButton =
          (this.currentProfileTab === 'import' && this.tabGroup.selectedIndex === 1) ||
          (this.currentSaleContentTab === 'create' && this.tabGroup.selectedIndex === 2)
        // console.log('Current Tab Index:', this.selectedTabIndex)
      }
    })
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onCancel()
        break
      case BUTTON_SAVE.typeBtn:
        this.onSave(1)
        break
    }
  }

  onCancel() {
    this.goTo(ROUTES_NAME_VOICE_BLASTER_CAMPAIGN.LIST)
  }

  onSaveDraft(number: number) {
    this.onSave(number)
  }

  validateAllTab(campaignData: ReqCreateCampaign) {
    this.campaignInfoComponent.validateForm()
    this.isValidTabInfo = this.campaignInfoComponent._page?.form.valid || false
    return this.isValidTabInfo
    // this.isValidTabProfile = !_.isEmpty(campaignData?.profiles || [])
    // this.isValidTabSaleContent = !_.isEmpty(campaignData?.saleCloseContents || [])
    // this.isValidTabAgent = !_.isEmpty(campaignData?.implementAgents || [])
    // if (!this.isValidTabInfo || !this.isValidTabProfile || !this.isValidTabSaleContent || !this.isValidTabAgent) {
    //   this.toastr.showToastri18n('Chưa nhập đủ thông tin', '', MessageSeverity.error)
    //   return false
    // }
    // return true
  }

  async onSave(campaignType: number) {
    const campaignData = await firstValueFrom(this.campaignBusinessLogicService.campaign$)
    const body = {
      ...campaignData,
      "sources": [
        {
          "customerSourceId": campaignData?.sourceId
        }
      ]
    }
    // save
    if (this.validateAllTab(campaignData)) {
      this.onCreateData(body)
    }
  }

  onCreateData(body: any) {
    this.indicator.showActivityIndicator()
    this._service
      .create(body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.form.reset()
            this.goTo(ROUTES_NAME_VOICE_BLASTER_CAMPAIGN.LIST)
            this.showDialogSuccessI18n('', `Thêm mới chiến dịch ${res.data?.name} <br/>thành công!`)
          }
        },
        (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Thêm mới campaign <br/>thất bại')
        }
      )
  }

  selectedIndexChange($event: number) {
    // const  {index } = $event
    if ($event === 1) {
    } else if ($event === 2) {
    }
    this.checkIfChildRoute()
  }

  pageMonitorEvent(type: number) {
    if (type === 0) {
      this.tabGroup.selectedIndex--
    } else {
      this.tabGroup.selectedIndex++
    }
  }

  isShowTabEmployee() {
    return Number(this.campaignData?.implementType || '0') !== 1
  }
}
