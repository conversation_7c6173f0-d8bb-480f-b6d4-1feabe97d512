<campaign-info #campaignInfoComponent></campaign-info>
<!--<mat-tab-group #tabGroup animationDuration="0ms" [(selectedIndex)]="selectedTabIndex" (selectedIndexChange)="selectedIndexChange($event)" class="custom-tab-bar">-->
<!--  <mat-tab class="custom-tab">-->
<!--    <ng-template mat-tab-label>-->
<!--      Thông tin chung-->
<!--      <mat-icon *ngIf="isValidTabInfo === false" class="mbb-icon fc-error mrl-1 mrt-1">info</mat-icon>-->
<!--      <mat-icon *ngIf="isValidTabInfo === true" class="mbb-icon fc-success mrl-1 mrt-1">check</mat-icon>-->
<!--    </ng-template>-->
<!--    <div class="mrt-4">-->
<!--      <campaign-info #campaignInfoComponent></campaign-info>-->
<!--    </div>-->
<!--  </mat-tab>-->
<!--  <mat-tab class="custom-tab">-->
<!--    <ng-template mat-tab-label>-->
<!--      Hồ sơ-->
<!--      <mat-icon *ngIf="isValidTabProfile === false" class="mbb-icon fc-error mrl-1 mrt-1">info</mat-icon>-->
<!--      <mat-icon *ngIf="isValidTabProfile === true" class="mbb-icon fc-success mrl-1 mrt-1">check</mat-icon>-->
<!--    </ng-template>-->
<!--    <div class="mrt-4">-->
<!--      <campaign-profile #campaignProfile *ngIf="currentProfileTab === 'list'"></campaign-profile>-->
<!--      <app-profile-import type="create" *ngIf="currentProfileTab === 'import'"></app-profile-import>-->
<!--    </div>-->
<!--  </mat-tab>-->
<!--  <mat-tab class="custom-tab">-->
<!--    <ng-template mat-tab-label>-->
<!--      Nội dung chốt sale-->
<!--      <mat-icon *ngIf="isValidTabSaleContent === false" class="mbb-icon fc-error mrl-1 mrt-1">info</mat-icon>-->
<!--      <mat-icon *ngIf="isValidTabSaleContent === true" class="mbb-icon fc-success mrl-1 mrt-1">check</mat-icon>-->
<!--    </ng-template>-->
<!--    <div class="mrt-4">-->
<!--      <app-sale-content-embed #saleContentsEmbedComponent [hidden]="currentSaleContentTab !== 'list'" type="create"></app-sale-content-embed>-->
<!--      <sale-close-content-create *ngIf="currentSaleContentTab === 'create'" [isEmbed]="true"></sale-close-content-create>-->
<!--    </div>-->
<!--  </mat-tab>-->
<!--  <mat-tab class="custom-tab" *ngIf="isShowTabEmployee()">-->
<!--    <ng-template mat-tab-label>-->
<!--      Nhân sự triển khai-->
<!--      <mat-icon *ngIf="isValidTabAgent === false" class="mbb-icon fc-error mrl-1 mrt-1">info</mat-icon>-->
<!--      <mat-icon *ngIf="isValidTabAgent === true" class="mbb-icon fc-success mrl-1 mrt-1">check</mat-icon>-->
<!--    </ng-template>-->
<!--    <div class="mrt-4">-->
<!--      <app-deployment-personal></app-deployment-personal>-->
<!--    </div>-->
<!--  </mat-tab>-->
<!--</mat-tab-group>-->
<div class="d-flex footer-container align-items-center" *ngIf='!isHideButton'>
<!--  <mat-icon class="mbb-icon ic-angle_left fc-dark-blue" style='font-size: 40px' (click)="pageMonitorEvent(0)"></mat-icon>-->
  <app-form-footer class='mrb-14 mrl-4 mr-4' [isFixedBottom]="false" [listButton]="listButton" (eventClick)="onClickBtn($event)"></app-form-footer>
<!--  <mat-icon class="mbb-icon ic-angle_right fc-dark-blue" style='font-size: 40px' (click)="pageMonitorEvent(1)"></mat-icon>-->
</div>
