export const configComponent: any = [
  {
    type: 'formControl',
    id: 'form-method-assign',
    data: {
      countMaxLength: false,
      value: '1',
      key: 'state',
      label: '<PERSON><PERSON><PERSON><PERSON> thức phân giao hồ sơ',

      order: 1,
      controlType: 'ngselect',
      type: 'single',
      placeholder: '<PERSON><PERSON>ơng thức phân giao hồ sơ',
      focus: false,
      options: [
        {
          key: '1',
          value: 'Tự động (Tổng số HS/ Tổng số Agent)'
        },
        {
          key: '2',
          value: 'Tự động (Tổng số HS Max/Agent)'
        },
        {
          key: '3',
          value: 'Thủ công'
        }
      ],
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      paramData: {
        isTag: false,
        url: '',
        preLoad: true,
        defaultKeySearch: '',
        key: '',
        value: ['']
      },
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      minRow: '2',
      clearable: false,
      checkBoxKey: 'value'
    },
    classes: 'w-40'
  },
  {
    type: 'formControl',
    id: 'form-quantity',
    data: {
      countMaxLength: true,
      value: '',
      key: 'quantity',
      label: 'Số lượng HS chỉ định',
      required: true,
      maxLength: 200,
      order: 1,
      controlType: 'textbox',
      type: 'number',
      placeholder: 'Nhập số lượng',
      focus: false,
      options: [],
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      paramData: {
        isTag: false,
        url: '',
        preLoad: true,
        defaultKeySearch: '',
        key: '',
        value: ['']
      },
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      clearable: false,
      checkBoxKey: 'value',
      min: '1',
    },
    classes: 'w-33'
  },
  {
    type: 'table',
    id: 'table-employee-deploy',
    data: {
      apiList: 'dcmcore-administration/v1/employee',
      apiCreate: '',
      displayedColumns: [
        {
          name: 'STT',
          field: 'field_1717236966389',
          path: 'no',
          show: true
        },
        {
          name: 'Họ và tên',
          field: 'agentFullName',
          path: 'agentFullName',
          show: true,
          type: 'textbox'
        },
        {
          name: 'User',
          field: 'agentAdUser',
          path: 'agentAdUser',
          show: true
        },
        {
          name: 'Nhóm',
          field: 'teams',
          path: 'teams',
          show: true,
          type: 'map',
          mapField: 'teamName'
        },
        {
          name: 'Level',
          field: 'employeeLevel',
          path: 'employeeLevel',
          show: true,
          type: 'textbox',
          stringFormat: 'Level {0}'
        },
        {
          name: 'Phân giao',
          field: 'agentEmployeeId',
          path: 'agentEmployeeId',
          show: true,
          type: 'checkbox-selection'
        },
        {
          name: 'Tổng HS sau phân giao',
          field: 'sumProfile',
          path: 'sumProfile',
          show: true,
          type: 'input-number',

        }
      ],
      isTreeData: false,
      isStaticTable: true
    },
    classes: ''
  },
  {
    type: 'table',
    id: 'table-assigned',
    data: {
      displayedColumns: [
        {
          name: 'STT',
          field: 'field_1717236966389',
          path: 'no',
          show: true
        },
        {
          name: 'Nhân sự',
          field: 'agentFullName',
          path: 'agentFullName',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Đã phân giao',
          field: 'assignedProfiles',
          path: 'assignedProfiles',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Chưa thực hiện',
          field: 'uncompletedProfiles',
          path: 'uncompletedProfiles',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Thu hồi',
          field: 'select',
          path: 'agentId',
          show: true,
          type: 'checkbox-selection-all'
        }
      ],
      pageSize: 10,
      isTreeData: false,
      isStaticTable: true
    },
    classes: ''
  }
]
