import { Component, EventEmitter, Input, OnChanges, On<PERSON><PERSON>roy, OnInit, Output, SimpleChanges } from '@angular/core'
import { MatTooltip } from '@angular/material/tooltip'
import _, { isEqual } from 'lodash'
import { <PERSON>sonPipe, NgClass, NgForOf, NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault } from '@angular/common'
import { MatCheckbox } from '@angular/material/checkbox'
import { FormsModule } from '@angular/forms'
import { FunctionCallerPipe } from '../../../../../../shared/pipe'
import { TextControlComponent } from '@shared/components/data-input'
import { getElementByPath } from 'src/app/shared/utils/table.utils'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { Subject, takeUntil } from 'rxjs'
import { distinctUntilChanged } from 'rxjs/operators'
import { TranslateModule } from '@ngx-translate/core'

@Component({
  selector: 'app-table-agent-row',
  standalone: true,
  imports: [
    MatTooltip,
    FunctionCallerPipe,
    JsonPipe,
    NgSwitch,
    NgSwitchDefault,
    NgSwitchCase,
    MatCheckbox,
    FormsModule,
    NgClass,
    NgForOf,
    NgIf,
    FunctionCallerPipe,
    TextControlComponent,
    TranslateModule
  ],
  templateUrl: './app-table-agent-row.component.html',
  styleUrl: './app-table-agent-row.component.scss'
})
export class AppTableAgentRowComponent implements OnInit, OnDestroy, OnChanges {
  @Input() element: any
  @Input() column: any
  @Input() checked!: boolean
  @Input() pathProperty = 'path'
  @Output() elementChange = new EventEmitter<any>()
  value: any
  @Input() rootEle: any
  count: number = 4 // Number of members initially displayed
  protected readonly getElementByPath = getElementByPath
  protected readonly _ = _
  public ngUnsubscribe = new Subject()
  unassignedProfiles = 0
  totalInput = 0
  isFocused = false
  elementUpdate: any = {}
  constructor(private _campaignBusinessLogicService: CampaignBusinessLogicService) {}

  // Function to check if more than 4 members
  hasMoreThanFourMembers(): boolean {
    return this.getArrayElement()?.length > this.count
  }

  getArrayElement(): any[] {
    const { type, mapField, enumText, maxLength } = this.column
    return (_.get(this.element, this.column[this.pathProperty]) || [])
      .map((x) => x && x[mapField]) // Ensure x[mapField] is truthy
      .filter((x) => x !== undefined && x !== null && x !== '')
  }

  ngOnInit(): void {
    if (this.elementUpdate[this.column.path] === undefined || this.elementUpdate[this.column.path] === null) {
      this.elementUpdate[this.column.path] = 0
    }
    this.value = _.get(this.element, this.column[this.pathProperty])
    this._campaignBusinessLogicService.campaignDetail$
      .pipe(
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        takeUntil(this.ngUnsubscribe)
      )
      .subscribe((detailCamp) => {
        this.unassignedProfiles = detailCamp?.unassignedProfiles || 0
        this.totalInput = detailCamp?.inputManualProfiles || 0
      })
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('agent', changes)
    this.element['selected'] = changes?.checked.currentValue
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next(true)
    this.ngUnsubscribe.complete()
  }

  protected readonly Array = Array

  onElementChange() {
    this.elementChange.emit({ type: 'change', row: this.element, rowEdit: this.elementUpdate }) // Emit the changed element
  }

  onChangeCheckBox() {
    this.element['selected'] = !this.element['selected']
    this.elementChange.emit({ type: 'selection', row: this.element, rowEdit: this.elementUpdate }) // Emit the changed element
  }

  onFocus(value: boolean) {
    this.isFocused = value
  }
  disableTooltip(element) {
    return element?.offsetWidth < this.rootEle?.offsetWidth
  }
}
