<div #container [ngSwitch]="column.type">
  <ng-container *ngSwitchCase="'checkbox-selection'">
    <mat-checkbox [checked]="element?.selected" (change)="onChangeCheckBox()"></mat-checkbox>
  </ng-container>
  <ng-container *ngSwitchCase="'input-number'">
    <div class="form-group" [class.error]="totalInput > unassignedProfiles && isFocused">
      <input
        placeholder="Nhập"
        [disabled]="!element.selected"
        class="form-control"
        type="number"
        [(ngModel)]="elementUpdate[column.path]"
        (ngModelChange)="onElementChange()"
        (focus)="onFocus(true)" />
      <div *ngIf="totalInput > unassignedProfiles && isFocused" class="form-remark">
        {{ 'validations.limitProfile' | translate }}
      </div>
    </div>
  </ng-container>
  <ng-container *ngSwitchCase="'map'">
    <ng-container>
      <span #ele
            class="text-ellipsis"
            matTooltipPosition='above'
            [matTooltipDisabled]="disableTooltip(ele)"
            matTooltip="{{ getArrayElement().join(';') }}">{{ getArrayElement().join(';') }}</span>
    </ng-container>
  </ng-container>
  <span
    #ele
    class="text-ellipsis"
    matTooltipPosition='above'
    [matTooltipDisabled]="disableTooltip(ele)"
    matTooltip="{{ value }}"
    *ngSwitchDefault
    [innerHTML]="getElementByPath | functionCaller: element : column : pathProperty"
    [ngClass]="{ no: column.path === 'no' }"></span>
</div>
