import {
  AfterViewChecked,
  Component,
  EventEmitter,
  Injector,
  Input,
  isDevMode,
  OnChanges,
  Output,
  SimpleChanges
} from '@angular/core'
import { MatTableModule } from '@angular/material/table'
import { AppPaginationComponent, ComponentAbstract, extractValueFromColon, HttpOptions, MessageSeverity, Verbs } from '@shared'
import { debounceTime, finalize, Subject, Subscription, takeUntil } from 'rxjs'
import { PageEvent } from '@angular/material/paginator'
import { environment } from '@env/environment'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { <PERSON>sonPip<PERSON>, <PERSON>F<PERSON>, NgIf } from '@angular/common'
import { SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import _ from 'lodash'
import { MatMenu, MatMenuTrigger } from '@angular/material/menu'
import { ActionButtonMenuComponent } from '@shared/components/data-display/action-button-menu/action-button-menu.component'
import { MatCheckbox } from '@angular/material/checkbox'
import { SelectionModel } from '@angular/cdk/collections'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { FunctionCallerPipe, ToggleColumnsPipe, TruncatePipe } from '../../../../../shared/pipe'
import { DisplayedColumn, TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree'
import { FlatTreeControl } from '@angular/cdk/tree'
import { getPathFromUrl, getQueryParams } from '../../../../../shared/utils'
import { AppTableAgentRowComponent } from '@features/campaign-voice-blaster/pages/assign/app-table-agent/app-table-agent-row/app-table-agent-row.component'
import { getElementByPath, getTooltipByPath } from 'src/app/shared/utils/table.utils'
import { removeProperties } from '../../../../../shared/utils'
import { CheckboxSelection, NavigationType } from '@features/campaign-voice-blaster/pages/assign/app-table-agent/app-table-agent.enum'
import { ResizeHandleDirective } from '../../../../../shared/directives/resize-handle.directive'
import {
  SharedTableSortHeaderComponent
} from '@shared/components/data-display/shared-table-sort-header/shared-table-sort-header.component'

@Component({
  selector: 'app-table-agent',
  templateUrl: './app-table-agent.component.html',
  styleUrls: ['./app-table-agent.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    TextControlComponent,
    SelectControlComponent,
    MatIconModule,
    NgFor,
    MatExpansionModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    NoDataComponent,
    AppPaginationComponent,
    ToggleColumnsPipe,
    ZoneComponent,
    JsonPipe,
    FunctionCallerPipe,
    TruncatePipe,
    MatMenu,
    MatMenuTrigger,
    ActionButtonMenuComponent,
    MatCheckbox,
    AppTableAgentRowComponent,
    ResizeHandleDirective,
    SharedTableSortHeaderComponent
  ]
})
export class AppTableAgentComponent extends ComponentAbstract implements AfterViewChecked, OnChanges {
  @Input() configComponent: TableComponentConfig
  @Input() isSearchAdvanced = false
  @Input() isSearchByKeyword = false
  @Input() method: Verbs = Verbs.GET
  @Input() noDataMessage: string
  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onTableActionClick = new EventEmitter<any>()
  @Output() onDataRowChanged = new EventEmitter<any>()
  visibleColumns: DisplayedColumn[] = []
  dataTable = []
  datasourceTree: MatTreeFlatDataSource<any, any, any>
  searchString: string = ''
  selection: SelectionModel<any>
  selectionUnCheck: SelectionModel<any>
  isCheckAll = false
  selectedCount = 0
  private selectionChangeSubscription: Subscription
  private unsubscribe$: Subject<void> = new Subject<void>()

  sortColumn: string = ''; // Cột hiện tại đang sort
  sortDirection: string = ''; // Hướng sort 'asc' hoặc 'desc'

  private transformer = (node: any, level: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      ...node,
      level: level
    }
  }

  treeControl = new FlatTreeControl<any>(
    (node) => node.level,
    (node) => node.expandable
  )

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node.children
  )

  constructor(protected override injector: Injector) {
    super(injector)
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.dataTable = []
  }

  initQuickSearchForm() {}

  get columns() {
    return this.visibleColumns
  }

  get isStaticTable() {
    return this.configComponent?.data.isStaticTable || false
  }

  get columnActionLists() {
    return this.configComponent?.data?.columnActionLists || []
  }

  get advancedSearchFields() {
    return this.configComponent?.data.displayedColumns || []
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {
    this.loadDataTable()
  }
  ngAfterViewChecked() {
    this.cdRef.detectChanges()
  }
  override ngOnDestroy() {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
    if (this.selectionChangeSubscription) {
      this.selectionChangeSubscription.unsubscribe()
    }
  }

  /**
   * do not remove
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    isDevMode() && console.log('app-table', this.configComponent)
    if (_.isEqual(changes.configComponent.currentValue, changes.configComponent.previousValue)) return
    if (!this.isStaticTable && this.configComponent?.data?.apiList && this.configComponent.data.disableAutoCallOnChange !== true) {
      this.search().then((r) => {})
    }
    this.setVisibleColumns()
    this.initializeSelectionModel()
  }

  setVisibleColumns() {
    if (this.configComponent?.data?.displayedColumns) {
      this.visibleColumns = [] // trigger re-render live edit ux builder
      setTimeout(() => {
        const setDefaultColumnCell = this.configComponent?.data?.displayedColumns.map((e) => {
          if (e.type) {
            return e
          } else {
            return {
              ...e,
              type: 'textbox'
            }
          }
        })
        this.visibleColumns = _.uniqBy([...this.visibleColumns, ...setDefaultColumnCell] || [], 'field')
        console.log('this.visibleColumns', this.visibleColumns)
        console.log('this.displayedColumns', this.displayedColumns)
      }, 100)
    }
  }

  handlePageEvent(e: PageEvent) {
    console.log('check all', this.isCheckAll)
    this.pageEvent = e
    this.pageSize = e.pageSize
    this.pageIndex = e.pageIndex
    if (!this.isStaticTable) {
      this.search(this.filterQuery).then((r) => {
        this.handlerCheckBox()
      })
    } else {
      this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.getStaticRecords(this.pageIndex, this.pageSize))
      this.handlerCheckBox()
    }
  }

  /**
   * call API get list
   * @param filter
   */
  search(filter = {}) {
    if (this.isStaticTable) {
      return Promise.resolve()
    }

    this.indicator.showActivityIndicator(true)

    return new Promise<void>((resolve, reject) => {
      const filterQuery = {...filter,
        sortColumn: this.sortDirection ? this.sortColumn : '',
        sortOrder: this.sortDirection ? (this.sortDirection === 'desc' ? 2 : 1) : '',
      }
      this.getList(this.pageIndex, this.pageSize, filterQuery)
        .pipe(
          takeUntil(this.unsubscribe$),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe(
          (res: any) => {
            if (res) {
              this.totalItem = res.data.totalElements
              this.pageSize = res.data.size
              const page = this.pageIndex * this.pageSize
              this.dataTable = res.data.content.map((x, index) => {
                return { ...x, no: page + index + 1 }
              })
              this.setDataSource()
              resolve(res) // Resolve the Promise on success
            } else {
              this.showErrorGetData()
              reject(new Error('Failed to fetch data')) // Reject the Promise on failure
            }
          },
          (error) => {
            this.showErrorGetData()
            reject(new Error('Failed to fetch data')) // Reject the Promise on failure
          }
        )
    })
  }

  loadDataTable() {
    this.totalItem = this.dataTable.length
    this.pageSize = this.configComponent.data.pageSize ? Number(this.configComponent.data.pageSize) : 10
    this.setDataSource()
  }

  setDataSource() {
    this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.dataTable)
  }

  expandNode(index: number) {
    if (this.treeControl.dataNodes?.length) {
      this.treeControl.expand(this.treeControl.dataNodes[index])
    }
  }

  /**
   * set source static
   * required call set .dataTable first
   */
  loadDataStaticTable(reloadColumn = true, data = [], keepPaging = false) {
    if (reloadColumn) {
      this.setVisibleColumns()
    }
    this.dataTable = data.map((x, index) => {
      return { ...x, no: index + 1 }
    })
    this.totalItem = this.dataTable.length

    if (!keepPaging) {
      this.pageIndex = 0
      if (this.queryParams?.page) {
        this.pageIndex = parseInt(this.queryParams.page) - 1
      }
      this.pageSize = this.configComponent.data.pageSize ? Number(this.configComponent.data.pageSize) : 10
      if (this.configComponent.data.noPaging) {
        this.pageSize = 999999
      }
    }
    this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.getStaticRecords(this.pageIndex, this.pageSize))
  }

  getStaticRecords(pageIndex: number, pageSize: number) {
    const startIndex = pageIndex * pageSize
    const endIndex = startIndex + pageSize
    return this.dataTable.slice(startIndex, endIndex)
  }

  /**
   * call API DELETE
   * @param element
   */
  onDelete(key: string) {
    if (!this.configComponent?.data?.apiDelete) {
      this.toastr.showToastri18n('dialog.delete-data-error', 'dialog.not-config-delete', MessageSeverity.error)
    } else {
      this.dialogService.confirm(
        {
          title: `${this.translateService.instant('dialog.r-u-sure-delete')} <br/>${this.configComponent?.data?.tableTitle}?`,
          message: '',
          textButtonRight: 'btn.delete'
        },
        (result) => {
          if (result) {
            this.delete(key)
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => this.indicator.hideActivityIndicator())
              )
              .subscribe(
                (res: any) => {
                  if (res) {
                    this.search(this.filterQuery)
                    this.showDialogSuccessI18n('', `Xóa ${this.configComponent?.data?.tableTitle} thành công`)
                  }
                },
                (error) => {
                  this.showDialogErrorI18n(error?.error?.message, error?.error?.error)
                }
              )
          }
        }
      )
    }
  }

  /**
   * call api delete
   * @param id
   */
  delete(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.configComponent?.data.apiDelete}/${id}`
    }
    return this.httpClient.delete(options)
  }

  getList(page, size, filter) {
    if (!this.configComponent?.data?.apiList) return
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.configComponent?.data.apiList}`
    }
    if (String(this.configComponent.data?.apiListMethod).toUpperCase() === Verbs.POST) {
      options.body = {
        ...filter,
        page,
        size
      }
      return this.httpClient.post(options)
    } else {
      options.params = {
        ...filter,
        page,
        size
      }
      return this.httpClient.get(options)
    }
  }

  /**
   *
   * @param params
   */
  download(url, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${url}`,
      params: params
    }
    return this.httpClient.download(Verbs.GET, options)
  }

  /**
   * thanhnx
   * Bấm vào button ở table column action
   * @param button
   */
  onActionColumnClick({ button, element }) {
    const url = getPathFromUrl(button?.routerName || '')
    if (button?.navigationType === 'nav' && button?.routerName) {
      const queryObject = getQueryParams(button?.routerName)
      const code = extractValueFromColon(url)
      if (code) {
        this.goTo(`${environment.base_path}${url.replace(`:${code}`, element[code])}`)
      } else {
        this.goTo(`${environment.base_path}${url}`, queryObject)
      }
    } else if (button?.navigationType === NavigationType.POPUP && button?.routerName) {
      //TODO: call popup từ string
    } else if (button?.navigationType === NavigationType.EMIT) {
      this.onTableActionClick.emit({ ...button, row: element })
    } else if (button?.navigationType === NavigationType.DELETE) {
      const propertyId = extractValueFromColon(button.routerName)
      if (propertyId) {
        this.onDelete(element[propertyId])
      } else {
        this.onDelete(element['id'])
      }
    } else {
      this.toastr.showToastri18n('', 'dialog.event-has-not-assign-yet', MessageSeverity.warning)
    }
  }

  protected readonly getElementByPath = getElementByPath
  protected readonly _ = _

  filterLeafNode(node: any, column: any): boolean {
    console.log(node[column.path])
    if (!node[column.path]) return false
    if (!this.searchString) {
      return false
    }
    return node[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) === -1
  }

  filterParentNode(node: any, column: any): boolean {
    if (!node[column.path]) return false
    if (!node.expandable) return false

    if (!this.searchString || node[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) !== -1) {
      return false
    }
    const descendants = this.treeControl.getDescendants(node)

    if (descendants.some((descendantNode) => descendantNode[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) !== -1)) {
      return false
    }

    return true
  }

  getFieldForCheckboxSelection(): string {
    const checkboxColumn = this.advancedSearchFields.find(
      (column) => column.type === CheckboxSelection.SINGLE || column.type === CheckboxSelection.ALL
    )
    return checkboxColumn ? checkboxColumn?.path : ''
  }

  initializeSelectionModel(): void {
    const field = this.getFieldForCheckboxSelection()
    if (field) {
      this.selection = new SelectionModel<any>(
        true, // multi-select
        [], // Initial selections
        true, // emit an event on selection change
        (otherValue, value) => {
          if (otherValue[field] === undefined && value[field] === undefined) {
            return otherValue === value
          } else {
            return otherValue[field] === value[field]
          }
        }
      )

      this.selectionUnCheck = new SelectionModel<any>(
        true, // multi-select
        [], // Initial selections
        true, // emit an event on selection change
        (otherValue, value) => {
          if (otherValue[field] === undefined && value[field] === undefined) {
            return otherValue === value
          } else {
            return otherValue[field] === value[field]
          }
        }
      )

      // Hủy bỏ subscription trước đó nếu tồn tại
      if (this.selectionChangeSubscription) {
        this.selectionChangeSubscription.unsubscribe()
      }

      this.selectionChangeSubscription = this.selection.changed.pipe(debounceTime(200)).subscribe((change) => {
        if (change.added.length || change.removed.length) {
          this.onTableActionClick.emit({
            type: 'selection',
            data: this.selection.selected,
            isCheckAll: this.isCheckAll,
            totalItem: this.selectedCount
          })
        }
      })
    }
  }

  isAllSelected() {
    const numSelected = this.selectedCount
    const numRows = this.totalItem
    return numSelected === numRows
  }

  masterToggle() {
    !this.isCheckAll
      ? this.selection?.clear()
      : this.datasourceTree.data.forEach((row) => {
          this.selection?.select(row)
        })

    if (this.isCheckAll) {
      this.selectedCount = this.totalItem
    } else {
      this.selectedCount = this.selection?.selected.length
      this.selectionUnCheck.clear()
    }
  }

  elementChange($event: any, element: any) {
    if ($event.type === 'selection') {
      this.toggle(element)
    }
    this.onDataRowChanged.emit($event)
  }

  isSelected(element) {
    return this.selection?.isSelected(element)
  }

  toggle(element) {
    removeProperties(element, ['expanded', 'level', 'no'])
    if (this.selection.isSelected(element)) {
      this.selectedCount--
      this.selectionUnCheck.select(element)
    } else {
      this.selectedCount++
      this.selectionUnCheck.deselect(element)
    }
    console.log('this.selectionUnCheck', this.selectionUnCheck)
    const toggle = this.selection?.toggle(element)
    return toggle
  }

  handlerCheckBox() {
    if (this.isCheckAll) {
      this.datasourceTree.data.forEach((row) => {
        if (!this.selectionUnCheck.isSelected(row)) {
          this.selection?.select(row)
        }
      })
    } else {
      // this.datasourceTree.data.forEach((row) => {
      //   this.selection?.deselect(row)
      // })
    }
  }

  onSortChanged($event: { field: string; direction: string }) {
    // Gửi yêu cầu lên server để lấy dữ liệu mới với sort đã cập nhật
    this.sortDirection = $event.direction
    this.sortColumn = $event.field
    this.search(this.filterQuery);
  }
}
