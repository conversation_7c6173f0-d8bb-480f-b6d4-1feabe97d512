import { Component, Injector, ViewChild } from '@angular/core'
import { BUTTON_ASSIGN, BUTTON_TYPE_CANCEL, ComponentAbstract, MessageSeverity, Status, TextboxItem, TreeDropdownItem } from '@shared'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import { configComponent } from '@features/campaign-voice-blaster/pages/assign/config'
import { JsonPipe, NgClass, NgIf } from '@angular/common'
import { SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { MatIcon } from '@angular/material/icon'
import { AppTableTreeComponent } from '@shared/components/data-display'
import { DonutChartComponent } from '@features/campaign-voice-blaster/shared/pie-chart/donut-chart.component'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { finalize, takeUntil } from 'rxjs'
import { RevokeAssignModel } from '@features/campaign-voice-blaster/models/revoke-assign.model'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { AppTableAgentComponent } from '@features/campaign-voice-blaster/pages/assign/app-table-agent/app-table-agent.component'
import { AppPanelComponent } from '@shared/components/element/app-panel/app-panel.component'
import { FormMappingDataComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/form-mapping-data/form-mapping-data.component'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { ROUTES_NAME_CAMPAIGN } from '../../../../app.routes'
import { TypeSelectedEnum } from '@features/campaign-voice-blaster/pages/assign/assign.enum'

@Component({
  selector: 'app-assign',
  standalone: true,
  imports: [
    NgIf,
    TreeSelectControlComponent,
    SelectControlComponent,
    TextControlComponent,
    MatIcon,
    AppTableTreeComponent,
    DonutChartComponent,
    FooterComponent,
    JsonPipe,
    AppTableAgentComponent,
    AppPanelComponent,
    FormMappingDataComponent,
    NgClass
  ],
  templateUrl: './assign.component.html',
  styleUrl: './assign.component.scss'
})
export class AssignComponent extends ComponentAbstract {
  // phuong thuc phan giao ho so
  @ViewChild('selectMethod') selectMethodComponent: SelectControlComponent
  selectMethodConfig = configComponent.find((e) => e.id === 'form-method-assign')
  $selectMethodGroup = new TreeDropdownItem(this.selectMethodConfig.data)
  typeSelected: TypeSelectedEnum = TypeSelectedEnum.AUTO_HS_MAX // Mac dinh la option dau tien
  // So luong chi dinh
  @ViewChild('inputQuantity') inputQuantityComponent: TextControlComponent
  inputQuantityConfig = configComponent.find((e) => e.id === 'form-quantity')
  $inputQuantityGroup = new TextboxItem(this.inputQuantityConfig.data)

  // Bang danh sach ns trien khai
  @ViewChild('employeeTable') employeeDeployTable: AppTableAgentComponent
  employeeTableConfig = configComponent.find((e) => e.id === 'table-employee-deploy')

  // Bang ho so da phan giao
  @ViewChild('assignedTable') assignedTable: AppTableTreeComponent
  assignedTableConfig = configComponent.find((e) => e.id === 'table-assigned')

  // Chart
  @ViewChild('chart') chart: DonutChartComponent

  // campaignId
  campaignId = ''

  // title
  campaignName = ''

  //danh sach nhan su trien khai
  listEmployeeDeploy = []
  //danh sach thu hoi
  listRevokeId = []
  selectedCountRevoke = 0
  //so ho so chua phan giao
  unAssignProfile = 0

  // hint text
  hintText = 'Số lượng HS chỉ định =< Tổng số HS chưa phân giao'
  isShowHintText = true

  inputAssign = 0
  agentsSelected = []

  // tat nut tam chia
  disableTempSeperate = true
  //contructor
  constructor(
    protected override injector: Injector,
    private _service: CampaignService,
    private _campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.configService.init(configComponent)
    this.campaignId = this.route.snapshot.paramMap.get('id')
    this.form = this.itemControl.toFormGroup([this.$selectMethodGroup, this.$inputQuantityGroup])
    const newObject = {
      ...this.employeeTableConfig,
      data: {
        ...this.employeeTableConfig.data,
        displayedColumns: this.employeeTableConfig.data.displayedColumns.map((column) =>
          column.field === 'sumProfile' ? { ...column, type: 'textbox' } : column
        )
      }
    }
    this.employeeTableConfig = newObject
  }

  // business
  protected componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_ASSIGN)
    this.indicator.showActivityIndicator(true)
    //params form parent
    try {
      this.getDetailCampaign()
      this.getListEmployee().then((r) => {
        this.defaultCheckAllEmployee()
      })
    } catch (e) {
      this.showErrorGetData()
    } finally {
      this.indicator.hideActivityIndicator(true)
    }
  }
  getDetailCampaign() {
    this._service
      .getDetail(this.campaignId)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe((res: any) => {
        if (res) {
          this.chart.chartData1 = [
            {
              label: 'Hồ sơ đã phân giao',
              color: '#45d39d',
              value: res.data.assignedProfiles
            },
            {
              label: 'Hồ sơ chưa phân giao',
              color: '#FF7B2C',
              value: res.data.unassignedProfiles
            },
            {
              label: 'Hạn chế liên hệ',
              color: '#9aa1bc',
              value: res.data.blacklistProfiles
            }
          ]
          this.chart.createChart()
          this.campaignName = res.data.campaignName
          this.unAssignProfile = res.data.unassignedProfiles
          this._campaignBusinessLogicService.updateCampaignDetail(res.data)
          // this.setDataSource()
        } else {
          this.showErrorGetData()
        }
      })
  }
  getListEmployee(reloadEmployeeTable = true, reloadAssignedTable = true): Promise<void> {
    return new Promise((resolve, reject) => {
      this._service
        .getListEmployee({ page: 0, size: 99999, filter: { campaignId: this.campaignId }, fetchAllCampaignStatistic: false })
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe(
          (res: any) => {
            if (res) {
              // this.listEmployeeDeploy = res.data.content
              this.totalItem = res.data.totalElements
              this.pageSize = res.data.size
              const page = this.pageIndex * this.pageSize
              if (res.data.content) {
                const employeeDeployData = res.data.content.map((x, index) => {
                  return { ...x, no: page + index + 1, assign: '1', sumProfile: 0, teams: x.teams ?? [] }
                })

                const assignedTable = res.data.content
                  .filter((e) => e.assignedProfiles > 0)
                  .map((x, index) => {
                    return { ...x, no: page + index + 1, assign: '1', sumProfile: 0, teams: x.teams ?? [] }
                  })
                this.listEmployeeDeploy = employeeDeployData

                if (reloadEmployeeTable) {
                  this.employeeDeployTable.loadDataStaticTable(false, employeeDeployData)
                }
                if (reloadAssignedTable) {
                  this.assignedTable.loadDataStaticTable(false, assignedTable)
                }
              }
              resolve()
              // this.setDataSource()
            } else {
              resolve()
            }
          },
          (error: any) => {
            resolve()
          }
        )
    })
  }

  treeSelectGroupChanged($event: any) {
    this.typeSelected = $event?.key
    this.employeeDeployTable.selection.clear(true)
    this.assignedTable.selection.clear(true)
    this.inputQuantityComponent.form.reset()
    this.inputAssign = 0

    if ($event?.key == 1) {
      this.inputQuantityComponent.item = {
        ...this.inputQuantityComponent.item,
        label: 'Số lượng HS chỉ định',
        required: true
      }
      this.hintText = 'Số lượng HS chỉ định =< Tổng số HS chưa phân giao'

      const newObject = {
        ...this.employeeTableConfig,
        data: {
          ...this.employeeTableConfig.data,
          displayedColumns: this.employeeTableConfig.data.displayedColumns.map((column) =>
            column.field === 'sumProfile' ? { ...column, type: 'textbox' } : column
          )
        }
      }
      this.employeeTableConfig = newObject
    } else if ($event?.key == 2) {
      this.inputQuantityComponent.item = {
        ...this.inputQuantityComponent.item,
        label: 'Số lượng HS Max/Agent',
        required: true
      }
      this.hintText = 'Số lượng HS chỉ định =< Tổng số HS chưa phân giao'

      const newObject = {
        ...this.employeeTableConfig,
        data: {
          ...this.employeeTableConfig.data,
          displayedColumns: this.employeeTableConfig.data.displayedColumns.map((column) =>
            column.field === 'sumProfile' ? { ...column, type: 'textbox' } : column
          )
        }
      }
      this.employeeTableConfig = newObject
    } else {
      this.hintText = ''

      const newObject = {
        ...this.employeeTableConfig,
        data: {
          ...this.employeeTableConfig.data,
          displayedColumns: this.employeeTableConfig.data.displayedColumns.map((column) =>
            column.field === 'sumProfile' ? { ...column, type: 'input-number' } : column
          )
        }
      }
      this.employeeTableConfig = newObject
    }

    this.listEmployeeDeploy = this.listEmployeeDeploy.map((e) => ({
      ...e,
      sumProfile: 0
    }))
    this.employeeDeployTable.loadDataStaticTable(false, this.listEmployeeDeploy)
    this.defaultCheckAllEmployee()
  }

  get quantity() {
    return this.inputQuantityComponent.form.get(this.$inputQuantityGroup.key).value
  }

  tempSeprate() {
    if (!this.quantity && Number(this.quantity) < 1) {
      this.validateAllFields(this.inputQuantityComponent.form)
      return
    } else if (!this.inputQuantityComponent.form.valid) {
      return
    } else if (!this.agentsSelected?.length) {
      return
    }
    this.listEmployeeDeploy = this.listEmployeeDeploy.map((e) => ({
      ...e,
      sumProfile: 0
    }))
    const employeeSelectedAuto = this.agentsSelected.map((e) => e.agentEmployeeId)

    const body = {
      campaignId: this.campaignId,
      assignMethod: this.typeSelected,
      employeeIds: this.typeSelected != TypeSelectedEnum.MANUAL ? employeeSelectedAuto : undefined,
      thresholdForEachAgent: this.typeSelected == TypeSelectedEnum.AUTO_MAX_HS ? this.quantity : undefined,
      maximumProfilesToAssign: this.typeSelected == TypeSelectedEnum.AUTO_HS_MAX ? this.quantity : undefined
    }
    this._service
      .assign(true, body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe((res: any) => {
        if (res.code === Status.SUCCESS) {
          this.campaignName = res.data.campaignName
          this.getDetailCampaign()
          //Tự động (Tổng số HS/ Tổng số Agent)
          if (this.typeSelected == TypeSelectedEnum.AUTO_HS_MAX) {
            const totalProfiles = this.quantity ?? 0
            const numEmployees = this.agentsSelected.length

            if (numEmployees > 0) {
              const profileAssign = Math.floor(totalProfiles / numEmployees)
              const remainder = totalProfiles % numEmployees

              const updateSelected = new Map(
                this.agentsSelected.map((e, index) => [e.agentEmployeeId, { ...e, sumProfile: profileAssign + (index < remainder ? 1 : 0) }])
              )

              this.listEmployeeDeploy = this.listEmployeeDeploy.map((e) =>
                updateSelected.has(e.agentEmployeeId) ? updateSelected.get(e.agentEmployeeId) : e
              )
            }
            this.employeeDeployTable.loadDataStaticTable(false, this.listEmployeeDeploy, true)
          }
          // Tự động ( Số lượng KH Max/Agent)
          else if (this.typeSelected == TypeSelectedEnum.AUTO_MAX_HS) {
            const selected = this.agentsSelected
            const numEmployees = selected.length

            if (numEmployees * Number(this.quantity) < this.unAssignProfile) {
              if (numEmployees > 0) {
                const updateSelected = new Map(selected.map((e) => [e.agentEmployeeId, { ...e, sumProfile: Number(this.quantity) }]))

                this.listEmployeeDeploy = this.listEmployeeDeploy.map((e) =>
                  updateSelected.has(e.agentEmployeeId) ? updateSelected.get(e.agentEmployeeId) : e
                )
              }
            } else {
              if (numEmployees > 0) {
                const profileAssign = Math.floor(this.unAssignProfile / numEmployees)
                const remainder = this.unAssignProfile % numEmployees

                const updateSelected = new Map(
                  selected.map((e, index) => [e.agentEmployeeId, { ...e, sumProfile: profileAssign + (index < remainder ? 1 : 0) }])
                )

                this.listEmployeeDeploy = this.listEmployeeDeploy.map((e) =>
                  updateSelected.has(e.agentEmployeeId) ? updateSelected.get(e.agentEmployeeId) : e
                )
              }
            }
            this.employeeDeployTable.loadDataStaticTable(false, this.listEmployeeDeploy)
          } else {
            // Thu cong
          }
          // this.setDataSource()
        } else {
          this.toastr.showToastr(this.translateService.instant(res.message), '', MessageSeverity.error)
        }
      })
  }

  revoke() {
    const listRevokes = [...this.listRevokeId]
    const arrEmployeeId = this.listRevokeId.map((e) => e.agentEmployeeId)
    const body: RevokeAssignModel = {
      revokeAll: this.assignedTable.isCheckAll,
      campaignId: this.campaignId,
      employeeIds: arrEmployeeId
    }
    this._service
      .revoke(body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this.toastr.showToastr('Thu hồi thành công', '', MessageSeverity.success)
          this.getDetailCampaign()
          this.getListEmployee(false, true)
          this.listRevokeId = []

          listRevokes.forEach((x) => {
            this.assignedTable.toggle(x)
          })
          // this.assignedTable.loadDataTable()
        } else {
          this.toastr.showToastr(this.translateService.instant(res.message), '', MessageSeverity.error) // this.showErrorGetData()
        }
      })
  }
  onTableAction($event) {
    if ($event.type === 'selection') {
      this.selectedCountRevoke = $event?.totalItem || 0
      this.listRevokeId = $event.data
    }
  }
  onClickBtn($event) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.goTo(`${ROUTES_NAME_CAMPAIGN.LIST}`)
        break
      case BUTTON_ASSIGN.typeBtn:
        this.assign()
        break
    }
  }

  assign() {
    this.dialogService.confirm(
      {
        title: this.translateService.instant('dialog.confirm'),
        message: this.translateService.instant('Bạn chắc chắn muốn thực hiện phân giao?')
      },
      (result) => {
        if (result) {
          this.callAPIAssign()
          // this.location.back()
        }
      }
    )
  }

  get maxProfileAssign() {
    if (this.typeSelected == TypeSelectedEnum.MANUAL) {
      return this.inputAssign
    } else if (this.typeSelected == TypeSelectedEnum.AUTO_HS_MAX) {
      return this.quantity.length > 0 ? this.quantity : undefined
    } else if (this.typeSelected == TypeSelectedEnum.AUTO_MAX_HS) {
      const numEmployees = this.agentsSelected.length
      if (numEmployees * Number(this.quantity) < this.unAssignProfile) {
        return numEmployees * Number(this.quantity)
      } else {
        return this.unAssignProfile
      }
    } else {
      return null
    }
  }

  callAPIAssign() {
    const employeeSelectedAuto = this.agentsSelected.map((e) => e.agentEmployeeId) // Chon chia tu dong
    const employeeSelectedManual = this.agentsSelected.reduce((acc, agent) => {
      acc[agent.agentEmployeeId] = agent.sumProfile
      return acc
    }, {})

    const body = {
      assignMethod: this.typeSelected,
      campaignId: this.campaignId,
      employeeIds: employeeSelectedAuto,
      maximumProfilesToAssign: this.maxProfileAssign,
      employeeIdsWithNumberOfProfiles: employeeSelectedManual
    }

    this._service
      .assign(false, body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === 200) {
            this.goTo(`${ROUTES_NAME_CAMPAIGN.LIST}`)
            this.showDialogSuccessI18n('', 'Phân giao thành công')
          }
        },
        (response) => {
          this.toastr.showToastr(this.translateService.instant(response?.error.message), '', MessageSeverity.error)
        }
      )
  }
  onChangeQuantities($event) {
    const control = this.inputQuantityComponent?.form.get(this.$inputQuantityGroup.key)
    if (control) {
      this.isShowHintText = control?.valid ?? true

      if (Number($event.form.getRawValue()[this.$inputQuantityGroup.key]) > this.unAssignProfile) {
        this.addCustomErrorToControl(control, 'Vui lòng nhập số lượng HS chỉ định =< Tổng số hồ sơ chưa phân giao')
      } else {
        control.clearValidators()
        this.addRequiredToControl(control)
      }
    }
  }
  focusOutFunc($event) {
    this.isShowHintText = this.inputQuantityComponent.form.valid
  }

  onDataRowChanged($event: any) {
    const { type, row, rowEdit } = $event
    if (type === 'change') {
      const index = this.agentsSelected.findIndex((x) => x.agentEmployeeId === row.agentEmployeeId)
      if (index !== -1) {
        this.agentsSelected.splice(index, 1, { ...row, ...rowEdit })
      }
      this.inputAssign = this.agentsSelected.reduce((sum, profile) => sum + profile.sumProfile, 0)
      this._campaignBusinessLogicService.updateCampaignDetail({ inputManualProfiles: this.inputAssign }, false)
    }
  }

  onTableActionClick($event: any) {
    if ($event?.type === 'selection') {
      // Tạo một bản đồ để lưu trữ sumProfile theo agentEmployeeId
      const sumProfileMap = new Map(this.agentsSelected.map((agent) => [agent.agentEmployeeId, agent.sumProfile]))

      // Cập nhật this.agentsSelected từ $event.data và giữ lại sumProfile nếu tồn tại
      this.agentsSelected = ($event?.data || []).map((row) => ({
        ...row,
        sumProfile: sumProfileMap.get(row.agentEmployeeId) || 0
      }))
      this.agentsSelected.sort((a, b) => {
        const indexA = this.getOriginalIndex(this.employeeDeployTable.dataTable, a.agentEmployeeId)
        const indexB = this.getOriginalIndex(this.employeeDeployTable.dataTable, b.agentEmployeeId)
        return indexA - indexB
      })
    }
  }

  getOriginalIndex(dataTable, agentEmployeeId) {
    return dataTable.findIndex((item) => item.agentEmployeeId === agentEmployeeId)
  }

  protected readonly TypeSelectedEnum = TypeSelectedEnum

  private defaultCheckAllEmployee() {
    setTimeout(() => {
      this.employeeDeployTable.dataTable.forEach((x) => {
        this.employeeDeployTable.toggle(x)
      })
    }, 200)
  }
}
