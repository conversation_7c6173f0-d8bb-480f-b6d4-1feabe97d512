<!--chart comp-->
<div class="panel chart-panel">
  <div class="panel-body">
    <div class="d-flex flex-center-row justify-content-between pdl-8 pdr-8">
      <div class='title'>
        <p size="12px">Tên Campaign</p>
        <label class="title-page">{{ campaignName }}</label>
      </div>
      <app-donut-chart #chart></app-donut-chart>
    </div>
  </div>
</div>
<!--division comp-->
<div class="panel mrt-8">
  <div class="panel-heading d-flex position-relative">
    <div class="d-flex flex-center-row justify-content-between">
      <label class="panel-heading-label">Thông tin phân giao</label>
    </div>
  </div>
  <div class="panel-body mrt-5 pdl-2 mrb-10">
    <div class="d-flex align-items-start">
      <app-select-control class='selected w-33' #selectMethod [item]="$selectMethodGroup" (onSelected)='treeSelectGroupChanged($event)'></app-select-control>
      <div class='w-25' [hidden]='typeSelected == TypeSelectedEnum.MANUAL'>

        <app-text-control #inputQuantity [item]="$inputQuantityGroup" (focusOutEvent)='focusOutFunc($event)' (onChanged)='onChangeQuantities($event)'></app-text-control>
        <span *ngIf="isShowHintText" class='hint-text'>{{ hintText }}</span>
      </div>
      <button *ngIf='typeSelected != TypeSelectedEnum.MANUAL' class="btn btn-md btn-primary mrt-2 mrt-8" (click)="tempSeprate()">
        <span>Tạm chia</span>
      </button>
      <span [hidden]='typeSelected != TypeSelectedEnum.MANUAL' class='hint-text-type3 mrt-12'>Lưu ý: Tổng số lượng Hồ sơ nhập < Tổng số lượng Hồ sơ chưa phân giao</span>
    </div>
  </div>
</div>
<!--table deploy-->
<div>
  <div class="mrt-4">
    <app-panel heading="Danh sách nhân sự triển khai">
      <ng-container heading>
        <div class="flex-fill"></div>
        <div class="input-profile" *ngIf='typeSelected == TypeSelectedEnum.MANUAL' [ngClass]="{'fc-error': inputAssign > unAssignProfile}">Số hồ sơ đã nhập: <span [ngClass]="{'fw-bold': inputAssign > unAssignProfile}">{{ inputAssign }}</span>/ {{unAssignProfile}} HS</div>
      </ng-container>
      <div content>
        <app-table-agent #employeeTable [configComponent]="employeeTableConfig" (onDataRowChanged)="onDataRowChanged($event)" (onTableActionClick)="onTableActionClick($event)"></app-table-agent>
      </div>
    </app-panel>
  </div>
  <div>
    <div class="panel mrt-8">
      <app-panel heading="Bảng hồ sơ đã phân giao">
        <ng-container heading>
          <div class="flex-fill"></div>
          <p class="mr-5">Đã chọn {{ selectedCountRevoke }}</p>
          <button type="button" class="btn btn-gray-200 btn-border fc-dark-blue pds-4 mrr-4" (click)="revoke()">
            <mat-icon class="mbb-icon ic-undo fc-dark-blue"></mat-icon>
            <span class="fc-dark-blue">Thu hồi</span>
          </button>
        </ng-container>
        <div content>
          <app-table-tree #assignedTable [configComponent]="assignedTableConfig" [isSaveStatePaging]="false" (onTableActionClick)="onTableAction($event)" [noDataMessage]="'Chưa có danh sách phân giao, vui lòng chọn phương thức phân giao'"></app-table-tree>
        </div>
      </app-panel>
    </div>
  </div>
  <app-form-footer [listButton]="listButton" (eventClick)="onClickBtn($event)" ></app-form-footer>
</div>
