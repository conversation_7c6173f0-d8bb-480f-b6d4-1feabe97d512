import { Component, Injector, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core'
import { ComponentAbstract, TreeDropdownItem } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { TableSaleContentComponent } from '@features/campaign-voice-blaster/shared/table-sale-content/table-sale-content.component'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { SelectControlComponent } from '@shared/components/data-input'
import { configComponent, tableConfigSaleContentDetail } from '@features/campaign-voice-blaster/pages/config'
import { NgClass, NgIf, NgSwitchCase } from '@angular/common'
import { TooltipDirective } from '@mb/ngx-ui-builder'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import { SaleCloseContent } from '@features/campaign-voice-blaster/models/campaign.model'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { firstValueFrom, takeUntil } from 'rxjs'
import { CampaignEnumStatus } from '@features/campaign-voice-blaster/constants'
import _, { isEmpty } from 'lodash'

@Component({
  selector: 'app-sale-content-embed',
  templateUrl: './sale-contents-embed.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    FooterComponent,
    MatIcon,
    TableSaleContentComponent,
    FormControlRendererComponent,
    SelectControlComponent,
    NgIf,
    TooltipDirective,
    NgSwitchCase,
    TreeSelectControlComponent,
    NgClass
  ],
  styleUrls: ['./sale-contents-embed.component.scss']
})
export class SaleContentsEmbedComponent extends ComponentAbstract implements OnChanges {
  @ViewChild('treeSelectControlComponent') treeSelectControlComponent: TreeSelectControlComponent
  @ViewChild('table') table: TableSaleContentComponent
  @Input() type = '' // edit - view
  config = configComponent
  selectedLists = [] // Data tam thoi luu thong tin NDCS
  dataTable = [] // data bang
  formConfig = this.config.find((e) => e.id === 'sale-close-content-form')
  tableConfig = this.config.find((e) => e.id === 'sale-close-content-table')
  $treeCheckBoxSaleContent = new TreeDropdownItem(this.formConfig.data)
  isHiddenAddSaleContent = false
  isDisabledAdd = true
  action: string
  @Input() isDetail = false
  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    console.log('*******', this.type)
  }

  protected componentInit(): void {
    this.action = this.queryParams['action']
    if (this.type !== 'view') {
      this._campaignBusinessLogicService.saleContentCreate$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((data) => {
        if (data) {
          const newData = [
            {
              ...data,
              data: data
            }
          ]
          this.addList(newData)
          this._campaignBusinessLogicService.saleContentCreate.next(null)
        }
      })
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('changes SaleContentsEmbedComponent', changes?.type.currentValue)
    if (changes?.type.firstChange && changes?.type.currentValue === 'view') {
      this.tableConfig = tableConfigSaleContentDetail
    }
  }

  async loadData() {
    if (this.type === 'edit' || this.type === 'view') {
      const campaignData = await firstValueFrom(this._campaignBusinessLogicService.campaign$)
      if (this.action !== 'copy') {
        if (Number(campaignData.campaignStatus) === CampaignEnumStatus.RUNNING) {
          this.isHiddenAddSaleContent = true
          this.table.enabledDragAndDrop = false
          this.tableConfig = tableConfigSaleContentDetail
        }
      }
      if (campaignData?.saleCloseContents?.length) {
        const filteredData = campaignData?.saleCloseContents.map((question) => {
          return {
            ...question,
            key: `${question?.originalQuestionId}`,
            data: question
          }
        })

        if (this.type === 'view') {
          this.isHiddenAddSaleContent = true
          this.table.enabledDragAndDrop = false
          this.addListDetail(filteredData)
        } else {
          this.addList(filteredData)
        }
        console.log('data?.saleCloseContents', campaignData?.saleCloseContents)
      }
    }
  }

  optimizeAndLoadData(selectedList: any[]) {
    const updatedData = this.isDetail ? [
      ...this.table.dataTable,
      ...selectedList.map((x) => {
        return { ...x.data, origin: x }
      })
    ].map((x, index) => {
      return { ...x, answers: x.answers.map((e, index) => ({
        ...e,
        answerContent: `${index + 1}. ${e.answerContent}`
      })), no: index + 1 }
    }) : [
      ...this.table.dataTable,
      ...selectedList.map((x) => {
        return { ...x.data, origin: x }
      })
    ].map((x, index) => {
      return { ...x, no: index + 1 }
    })

    this.table.dataTable = _.uniqBy(updatedData, 'questionId')

    this.table.loadDataTable()
    setTimeout(() => {
      this.table.expandNode(0)
    }, 500)
  }

  addList(selectedList: any[]) {
    this.optimizeAndLoadData(selectedList)

    const keys = selectedList.map((x) => x.data.key)
    this.treeSelectControlComponent.item.options = this.treeSelectControlComponent.item.options.filter((x) => !keys.includes(x.key))
    this.treeSelectControlComponent.form.reset()
    this.isDisabledAdd = true
    this._campaignBusinessLogicService.updateSaleCloseContents(this.mapJsonToSaleCloseContents(this.table.dataTable))
  }

  addListDetail(selectedList: any[]) {
    this.optimizeAndLoadData(selectedList)
  }

  onButtonClick($event: any) {}

  addQuestion() {
    this._campaignBusinessLogicService.buttonClick.next({ type: 'sale-content', value: 'create' })
  }

  onDeleteQuestion($event: any) {
    this.treeSelectControlComponent.item.options = [$event.origin.data, ...this.treeSelectControlComponent.item.options]
    this._campaignBusinessLogicService.updateSaleCloseContents(this.mapJsonToSaleCloseContents(this.table.dataTable))
  }

  formChanged($event: any) {
    if (Array.isArray($event.data)) {
      this.selectedLists = $event.data
    }
    this.isDisabledAdd = isEmpty(this.selectedLists)
  }

  mapJsonToSaleCloseContents(inputJson: any[]): SaleCloseContent[] {
    //TODO: ban cho truong full content
    return inputJson.map((item) => ({
      questionId: item.questionId,
      originalQuestionId: item.questionId,
      isDecisive: item.isDecisive,
      questionContent: item?.questionContent || '',
      answerType: item?.answerType || '',
      answers: item.answers.map((answer, index) => ({
        originalAnswerId: answer.answerId,
        answerContent: answer.answerContent,
        isDecisive: answer.isDecisive
      }))
    }))
  }
  onDeleteAnswer($event: any) {}
}
