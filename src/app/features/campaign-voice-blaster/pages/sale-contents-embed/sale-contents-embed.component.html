<div class='panel'>
  <div class='panel-body'>
    <div [ngClass]="{ 'hidden': isHiddenAddSaleContent}" class='flex-center-row justify-content-between'>
      <div class='flex-center-row align-items-center w-50'>
        <app-tree-select-control formGroupClass='mrb-2' class='w-90 mrb-1' #treeSelectControlComponent [item]="$treeCheckBoxSaleContent" (onChanged)="formChanged($event)"></app-tree-select-control>
        <!--    <app-form-control-renderer class='w-75' [config]='config[0]' (formChanged)="formChanged($event)" ></app-form-control-renderer>-->
        <button [disabled]="isDisabledAdd" class="btn btn-md btn-primary btn-add-list " (click)="addList(selectedLists)">
          <mat-icon class="mbb-icon ic-add_circle_outline mrl-2"></mat-icon>
          <span>Thêm vào danh sách</span>
        </button>
      </div>
      <button type="button" class="btn btn-white btn-border btn-md fc-dark-blue  mr-2" (click)="addQuestion()">
        <mat-icon class="mbb-icon ic-plus fc-dark-blue"></mat-icon>
        <span class="fc-dark-blue">Thêm mới</span>
      </button>
    </div>
    <table-sale-content #table [configComponent]='tableConfig' (onButtonClick)="onButtonClick($event)" (onDeleteQuestion)="onDeleteQuestion($event)" (onDeleteAnswer)="onDeleteAnswer($event)"></table-sale-content>
  </div>
</div>
