import { Component, Injector, Input, isDevMode, ViewChild } from '@angular/core'
import { BUTTON_TYPE_CANCEL, BUTTON_TYPE_PREVIEW, BUTTON_UPDATE, ComponentAbstract, MessageSeverity } from '@shared'
import { MatIcon } from '@angular/material/icon'

import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { DragDropUploadFileComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/drag-drop-upload-file/drag-drop-upload-file.component'
import { AppPanelComponent } from '@shared/components/element/app-panel/app-panel.component'
import { FormSaleContentComponent } from '@features/sale-close-content/pages/create/form-sale-content/form-sale-content.component'
import { FormMappingDataComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/form-mapping-data/form-mapping-data.component'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import { MetaDataSaveProfile, ProfileUploadData, SelectedColumn } from '@features/campaign-voice-blaster/models/profile-import.model'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { ProfileImportPreviewComponent } from '@features/campaign-voice-blaster/pages/profile/profile-import/profile-import-preview/profile-import-preview.component'
import { finalize, firstValueFrom, takeUntil } from 'rxjs'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import _ from 'lodash'
import { PATH_API } from '@features/campaign-voice-blaster/constants'
import FileSaver from 'file-saver'
import dayjs from 'dayjs/esm'

@Component({
  selector: 'app-profile-import',
  templateUrl: './profile-import.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    FooterComponent,
    MatIcon,
    DragDropUploadFileComponent,
    DragDropUploadFileComponent,
    AppPanelComponent,
    FormSaleContentComponent,
    FormMappingDataComponent,
    TreeSelectControlComponent
  ],
  styleUrls: ['./profile-import.component.scss']
})
export class ProfileImportComponent extends ComponentAbstract {
  @Input() type = ''
  @ViewChild('uploadFileComponent') uploadFileComponent: DragDropUploadFileComponent
  @ViewChild('formMappingDataComponent') formMappingDataComponent: FormMappingDataComponent
  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService,
    private _campaignService: CampaignService
  ) {
    super(injector)
  }

  urlUpload = PATH_API.UPLOAD_PROFILE
  override ngAfterViewInit() {
    super.ngAfterViewInit()
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_TYPE_PREVIEW, BUTTON_UPDATE)
  }

  onSave() {
    this.onSaveProfile()
  }

  onCancel() {
    this.dialogService.confirm(
      {
        title: this.translateService.instant('dialog.confirm'),
        message: this.translateService.instant('dialog.confirm-cancel-import')
      },
      (result) => {
        if (result) {
          // this.location.back()
          this._campaignBusinessLogicService.buttonClick.next({ type: 'profile', value: 'list' })
        }
      }
    )
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onCancel()
        break
      case BUTTON_TYPE_PREVIEW.typeBtn:
        this.onPreview()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave()
        break
    }
  }

  fileChanged($event: any[]) {
    console.log($event)
    if (_.isEmpty($event)) {
      this.formMappingDataComponent?.resetMappingFields()
    }
  }

  onChangedImport($event: ProfileUploadData) {
    this._campaignBusinessLogicService.updateProfileUploadData($event)
  }

  /**
   *
   * @private
   */
  private async buildDataBody() {
    try {
      const { mappings } = this.formMappingDataComponent.formMapping.getRawValue()
      const dataTable = this.formMappingDataComponent.dataTable
      const profileUpload = await firstValueFrom(this._campaignBusinessLogicService.profileUploadData$)
      const campaign = await firstValueFrom(this._campaignBusinessLogicService.campaign$)

      console.log('mappings', mappings)
      console.log('profileUpload', profileUpload)

      const selectedColumn = this.buildSelectedColumns(mappings, profileUpload, dataTable)

      return {
        fileHash: profileUpload?.fileHash,
        fileId: profileUpload?.fileId,
        selectedColumns: selectedColumn,
        deletedRows: [],
        previousProfileSets: (campaign?.profiles || []).map(x => x?.profileSetId) // current profile set
      }
    } catch (error) {
      this.indicator.hideActivityIndicator(true)
      isDevMode() && console.log(error)
      return null
    }
  }

  /**
   *
   * @private
   */
  private async onPreview() {
    if (this.formMappingDataComponent.formMapping.invalid) {
      this.formMappingDataComponent.validateForm()
      return
    }

    const { mappings } = this.formMappingDataComponent.formMapping.getRawValue()
    const dataTable = this.formMappingDataComponent.dataTable

    if (Array.isArray(mappings) && mappings.length > 0) {
      try {
        const profileUpload = await firstValueFrom(this._campaignBusinessLogicService.profileUploadData$)
        if (!profileUpload?.fileHash) {
          return
        }

        const selectedColumn = this.buildSelectedColumns(mappings, profileUpload, dataTable)

        this.dialogService.componentDialog(
          ProfileImportPreviewComponent,
          {
            width: '86%',
            height: '85%',
            data: {
              fileHash: profileUpload?.fileHash,
              fileId: profileUpload?.fileId,
              selectedColumns: selectedColumn
            }
          },
          (data: any) => {
            if (data) {
              this.onSaveProfile()
            }
          }
        )
      } catch (error) {
        console.error('Error fetching profile upload data', error)
      }
    } else {
      this.toastr.showToastri18n('Chưa chọn trường mapping dữ liệu', '', MessageSeverity.error)
    }
  }

  private buildSelectedColumns(mappings: any[], profileUpload: any, dataTable: any[]): SelectedColumn[] {
    return mappings
      .map((x) => {
        const profileAttr = profileUpload.profileAttributes.find((attr) => attr.profileAttributeKey === x.profileAttributeKey)
        return {
          excelColumnIndex: profileAttr?.excelColumnIndex,
          profileAttributeKey: x.profileAttributeKey,
          profileAttributeName: x.profileAttributeName,
          duplicateCheck: x.duplicateCheck
        }
      })
      .sort((a, b) => {
        const indexA = dataTable.findIndex((item) => item.key === a.profileAttributeName)
        const indexB = dataTable.findIndex((item) => item.key === b.profileAttributeName)
        return indexA - indexB
      })
  }

  validateMapping() {
    if (this.formMappingDataComponent.formMapping.invalid) {
      this.formMappingDataComponent.validateForm()
      return false
    }
    if (!this.uploadFileComponent.fileUpload?.length) {
      // this.showDialogErrorI18n('', 'validations.required-import')
      this.toastr.showToastri18n('validations.required-import', '', MessageSeverity.error)
      return false
    }
    const { mappings } = this.formMappingDataComponent.formMapping.getRawValue()
    if (Array.isArray(mappings) && mappings?.length) {
      return true
    } else {
      this.toastr.showToastri18n('Chưa chọn trường mapping dữ liệu', '', MessageSeverity.error)
      return false
    }
  }

  /**
   * save profile
   * @param body
   */
  async onSaveProfile() {
    if (this.validateMapping()) {
      this.indicator.showActivityIndicator(true)
      const body = await this.buildDataBody()
      if (body !== null) {
        this._campaignService
          .saveProfile(body)
          .pipe(
            takeUntil(this.ngUnsubscribe),
            finalize(() => this.indicator.hideActivityIndicator(true))
          )
          .subscribe(
            (res: any) => {
              const metaData: MetaDataSaveProfile = JSON.parse(res.headers.get('metadata'))
              if (res?.body?.type === 'application/octet-stream') {
                this.formMappingDataComponent.lockMapping()
                this._campaignBusinessLogicService.addProfileId({ profileSetId: metaData.profileSetId })
                this._campaignBusinessLogicService.updateLastProfile({ profileSetId: metaData.profileSetId })
                if (metaData.numberOfSuccessRows > 0) {
                  this._campaignBusinessLogicService.buttonClick.next({ type: 'profile', value: 'list' })
                  this.dialogService.success(
                    {
                      title: 'Import hồ sơ thành công',
                      message: `Số bản ghi thành công ${metaData.numberOfSuccessRows}/${metaData.total}`
                    },
                    (result) => {}
                  )
                  // this.showDialogSuccessI18n(`Số bản ghi thành công ${metaData.numberOfSuccessRows}/${metaData.total}`, 'Import hồ sơ thành công')
                  if (Number(metaData.numberOfSuccessRows) !== Number(metaData.total)) {
                    FileSaver.saveAs(res.body, `ket-qua-import-thanh-cong${dayjs().format('YYYYMMDD')}.${metaData.fileExtension}`)
                  }
                } else {
                  this.showDialogErrorI18n(`Số bản ghi thất bại ${metaData.numberOfFailRows}/${metaData.total}`, 'Import hồ sơ thất bại')
                  FileSaver.saveAs(res.body, `ket-qua-import-that-bai-${dayjs().format('YYYYMMDD')}.${metaData.fileExtension}`)
                }
              } else {
                // truong hợp ko tra ve file
                // this.toastr.showToastri18n('Cập nhật thất bại', '', MessageSeverity.error)
              }
            },
            (response) => {
              this.toastr.showToastri18n(response?.error?.message, '', MessageSeverity.error)
            }
          )
      }
    }
  }

  goToProfileOutlet() {
    this._campaignBusinessLogicService.buttonClick.next({ type: 'profile', value: 'list' })
  }
}
