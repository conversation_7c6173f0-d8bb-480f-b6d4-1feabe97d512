import { Component, ElementRef, EventEmitter, Injector, Input, Output, ViewChild } from '@angular/core'
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { AppPaginationComponent, ComponentAbstract, HttpOptions, MessageSeverity } from '@shared'

import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { environment } from '@env/environment'

import { CdkColumnDef } from '@angular/cdk/table'
import { JsonPipe, NgForOf, NgIf } from '@angular/common'
import { MatButtonModule } from '@angular/material/button'
import { MatIconModule } from '@angular/material/icon'
import { MatSort } from '@angular/material/sort'
import {
  MatCell,
  MatCellDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable
} from '@angular/material/table'
import { MatTooltip } from '@angular/material/tooltip'
import { TranslateModule } from '@ngx-translate/core'
import { AppTableTreeComponent } from '@shared/components/data-display'
import { finalize, takeUntil } from 'rxjs'
import { NgxFileDragDropComponent } from '@shared/components/data-input/ngx-file-drag-drop/ngx-file-drag-drop.component'
import { BytePipe, ToggleColumnsPipe } from '../../../../../../shared/pipe'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { FileValidators } from '../../../../../../shared/validators'
import { ProfileUploadData } from '@features/campaign-voice-blaster/models/profile-import.model'

@Component({
  selector: 'app-drag-drop-upload-file',
  templateUrl: './drag-drop-upload-file.component.html',
  styleUrls: ['./drag-drop-upload-file.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    NgxFileDragDropComponent,
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    TranslateModule,
    BytePipe,
    AppPaginationComponent,
    MatCell,
    MatCellDef,
    MatHeaderCell,
    MatHeaderRow,
    MatHeaderRowDef,
    MatRow,
    MatRowDef,
    MatSort,
    MatTable,
    MatTooltip,
    NgForOf,
    NoDataComponent,
    ToggleColumnsPipe,
    CdkColumnDef,
    MatHeaderCellDef,
    JsonPipe,
    AppTableTreeComponent
  ]
})
export class DragDropUploadFileComponent extends ComponentAbstract {
  textButtonLeft = 'btn.cancel'
  textButtonRight = 'btn.accept'
  @Input() urlImport = ''
  @Input() fileNameResult = ''
  @Output() fileChanged = new EventEmitter<any[]>()
  @Output() onUploadError = new EventEmitter<any>()
  @Output() onChanged = new EventEmitter<ProfileUploadData>()
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>
  @ViewChild('fileDragDropComponent') fileDragDropComponent: NgxFileDragDropComponent
  accept: string = '.xls, .xlsx'
  multiple: boolean
  fileUpload: File[]
  maxSize = 1048576 * 10
  fileControl = new FormControl(
    [],
    [FileValidators.required, FileValidators.maxFileSize(this.maxSize), FileValidators.fileExtension(['xls', 'xlsx'])]
  )
  onValueChange(files: File[]) {
    this.fileUpload = files
    this.fileChanged.emit(files)
    this.uploadPreview()
  }

  constructor(
    protected override injector: Injector,
    public dialogRef: MatDialogRef<DragDropUploadFileComponent>
  ) {
    super(injector)
    this.dialogRef.disableClose = true
  }

  closeDialog() {
    if (this.dialogRef.close) {
      this.dialogRef.close({ status: 0 })
    }
  }

  /**
   *
   * @param formData
   */
  importExcel(formData: FormData, preview: boolean) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.urlImport}`,
      params: {}
    }
    return this.httpClient.uploadFormData(options, formData)
  }

  onImportFile() {
    if (this.multiple) {
      this.uploadExcelImport(this.fileUpload)
    } else {
      this.uploadExcelImport(this.fileUpload[0])
    }
  }

  /**
   * import excel
   * @param $event
   */
  uploadExcelImport($event: any) {
    this.indicator.showActivityIndicator(true)
    const formData: FormData = new FormData()
    formData.append('file', $event)
    this.importExcel(formData, false)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res) => {
          const data: ProfileUploadData = res.body.data
        },
        (response) => {
          // this.fileDragDropComponent.clear()
          this.toastr.showToastri18n(response?.error?.message, '', MessageSeverity.error)
        }
      )
  }

  uploadPreview() {
    if (!this.fileUpload?.length) {
      return
    }
    const formData: FormData = new FormData()
    formData.append('file', this.fileUpload[0])
    this.importExcel(formData, true).subscribe(
      {
        next: (res) => {
          if (res?.body?.type === 'application/json') {
            this.toastr.showToastri18n('Tải file lên thành công', '', MessageSeverity.success)
            this.blobToJson(res.body)
              .then((jsonObject) => {
                const data: ProfileUploadData = jsonObject.data
                this.onChanged.emit(data)
              })
              .catch((error) => {
                console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
              })
          } else {
          }
        },
        error: (res) => {
          this.fileDragDropComponent.clear()
          if (res?.error?.type === 'application/json') {
            this.blobToJson(res?.error)
              .then((jsonObject) => {
                console.log('BUS', jsonObject)
                // TAT POPUP KHI TRA VE 500
                if(jsonObject?.code === 400) {
                  this.toastr.showToastri18n(jsonObject?.message, '', MessageSeverity.error)
                }
              })
              .catch((error) => {
                console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
              })
          }
        }
      }
    )
  }
  blobToJson(blob: Blob): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        try {
          const jsonString = reader.result as string
          const jsonObject = JSON.parse(jsonString)
          resolve(jsonObject)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsText(blob)
    })
  }
  onCloseCancel() {
    this.dialogRef.close(true)
  }

  protected componentInit(): void {}

  protected readonly Object = Object
}
