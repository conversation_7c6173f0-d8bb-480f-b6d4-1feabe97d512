import { Component, Injector, ViewChild } from '@angular/core'
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { AppPaginationComponent } from '@shared'

import { FormsModule, ReactiveFormsModule } from '@angular/forms'

import { CdkColumnDef } from '@angular/cdk/table'
import { <PERSON>sonPipe, NgForOf, NgIf } from '@angular/common'
import { MatButtonModule } from '@angular/material/button'
import { MatIconModule } from '@angular/material/icon'
import { MatSort } from '@angular/material/sort'
import {
  MatCell,
  MatCellDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable
} from '@angular/material/table'
import { MatTooltip } from '@angular/material/tooltip'
import { TranslateModule } from '@ngx-translate/core'
import { AppTableTreeComponent } from '@shared/components/data-display'
import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { ComponentDialogAbstract } from '../../../../../../shared/abstract/component-dialog.abstract'
import { PATH_API } from '@features/campaign-voice-blaster/constants'

@Component({
  selector: 'app-drag-drop-upload-file',
  templateUrl: './profile-import-preview.component.html',
  styleUrls: ['./profile-import-preview.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    TranslateModule,
    AppPaginationComponent,
    MatCell,
    MatCellDef,
    MatHeaderCell,
    MatHeaderRow,
    MatHeaderRowDef,
    MatRow,
    MatRowDef,
    MatSort,
    MatTable,
    MatTooltip,
    NgForOf,
    CdkColumnDef,
    MatHeaderCellDef,
    JsonPipe,
    AppTableTreeComponent
  ]
})
export class ProfileImportPreviewComponent extends ComponentDialogAbstract {
  @ViewChild('tableComponent') tableComponent: AppTableTreeComponent

  tableComponentConfig: TableComponentConfig = {
    id: 'table-preview',
    type: '_container',
    classes: '',
    data: {
      apiListMethod: 'POST',
      isStaticTable: false,
      displayedColumns: []
    }
  }
  constructor(
    protected override injector: Injector,
    public dialogRef: MatDialogRef<ProfileImportPreviewComponent>
  ) {
    super(injector)
    this.dialogRef.disableClose = true
  }

  protected override initData() {
    super.initData()
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()
    this.elementService.checkViewChildExists('tableComponent', this).then((res) => {
      this.postPreviewExcel()
    })
  }

  /**
   * import excel
   * @param $event
   */
  postPreviewExcel() {
    const urlPreview = PATH_API.PREVIEW_PROFILE
    const { fileHash, fileId, selectedColumns } = this.dialogData

    this.tableComponent.filterQuery = {
      fileHash,
      fileId,
      selectedColumns,
      page: 0,
      size: 10
    }
    this.tableComponentConfig.data.displayedColumns = []
    this.tableComponentConfig.data.displayedColumns.push({
      name: 'STT',
      field: 'STT',
      path: 'no',
      show: true
    })
    this.dialogData.selectedColumns.forEach((x) => {
      this.tableComponentConfig.data.displayedColumns.push({
        name: x.profileAttributeName,
        field: x.profileAttributeName,
        path: x.profileAttributeName,
        show: true
      })
    })

    this.tableComponentConfig.data.apiList = urlPreview
    this.tableComponentConfig = { ...this.tableComponentConfig }

    this.tableComponent.pageIndex = 0
    this.tableComponent.search(this.tableComponent.filterQuery).then((r) => {})
    this.tableComponent.setVisibleColumns()
  }

  onCloseCancel() {
    this.dialogRef.close(false)
  }

  onUpdate() {
    this.dialogRef.close(true)
  }
}
