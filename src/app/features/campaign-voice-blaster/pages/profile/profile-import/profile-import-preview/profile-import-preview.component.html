<div class='panel modal-dialog-scrollable'>
  <div class='modal-heading modal-heading bdt'>
    <div class="d-flex flex-center-row justify-content-between">
      <label class='form-label'>Xem trước</label>
      <button (click)="onCloseCancel()" class="button-right" mat-icon-button>
        <mat-icon class="mbb-icon mr-0 ic-close_blue"></mat-icon>
      </button>
    </div>
  </div>
  <div class="modal-content">
<!--    <div class="flex-center-row mrb-3">-->
<!--      <strong class="mrr-3">Tên mẫu </strong>-->
<!--      <div>{{ this.formDetail?.formName }}</div>-->
<!--    </div>-->
    <app-table-tree #tableComponent [configComponent]="tableComponentConfig" [isSaveStatePaging]="false"></app-table-tree>
  </div>
  <div class="dialog-fixed-bottom d-flex justify-content-center mrt-4">
    <button class="btn btn-border btn-white btn-md fc-dark-blue mrl-4" (click)="onCloseCancel()">
      {{ 'btn.close' | translate }}
    </button>
    <button class="btn btn-md btn-primary mrl-4" (click)="onUpdate()">
      {{ 'btn.update' | translate }}
    </button>
  </div>
</div>
