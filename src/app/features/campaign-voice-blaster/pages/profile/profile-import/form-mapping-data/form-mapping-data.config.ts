import { CheckboxItem, TextboxItem, TreeDropdownItem } from '@shared'
import { environment } from '@env/environment'

export const cbCheckDuplicate = () =>
  new CheckboxItem({
    key: 'duplicateCheck',
    label: '',
    layout: 'row wrap',
    checkBoxKey: 'key',
    hideValueCheckBox: true,
    options: [
      {
        key: '1',
        value: '1'
      }
    ],
    value: undefined,
    required: true
  })

export const cbbCommonField = () =>
  new TreeDropdownItem({
    key: 'commonField',
    value: undefined,
    required: false,
    placeholder: 'Tìm kiếm trường dữ liệu tĩnh',
    type: 'multiple',
    paramData: {
      url: `${environment.services.dcmsCoreCampaign}/v1/common-fields?state=1`,
      preLoad: true,
      key: 'fieldKey',
      value: ['fieldKey'],
      typeheadKey: 'fieldKey',
      pageSize: 9999
    },
    selectAll: true,
  })

export const cbbTargetField = () =>
  new TreeDropdownItem({
    key: 'profileAttributeKey',
    value: undefined,
    required: true,
    placeholder: 'Chọn trường dữ liệu trong file data',
    type: 'single',
    options: [],
    paramData: {
      isTag: false,
      url: '',
      preLoad: false,
      key: 'profileAttributeKey',
      value: ['profileAttributeName'],
      clientFilter: true
    }
  })

export const txtField = () =>
  new TextboxItem({
    key: 'profileAttributeName',
    label: '',
    placeholder: `Nhập tên trường`,
    value: '',
    required: true,
    // countMaxLength: true,
    // maxLength: 200,
    // regex: VALIDATOR_REGEX_VALIDATE_SPECIAL_CHARS,
    upperCase: true,
    removeVNAccent: true,
    customValidate: 'customMessageError'
  })
