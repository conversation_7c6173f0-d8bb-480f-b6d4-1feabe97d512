import { ChangeDetectionStrategy, Component, EventEmitter, Injector, Input, Output, QueryList, ViewChild, ViewChildren } from '@angular/core'
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { MatTable, MatTableModule } from '@angular/material/table'
import { ComponentAbstract, getSelectOptions, TextboxItem } from '@shared'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { MatIcon } from '@angular/material/icon'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { MatCheckbox } from '@angular/material/checkbox'
import { JsonPipe, NgClass, NgIf } from '@angular/common'
import { CdkDrag, CdkDragDrop, CdkDragPreview, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop'
import { MatIconButton } from '@angular/material/button'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import {
  cbbCommonField,
  cbbTargetField,
  cbCheckDuplicate,
  txtField
} from '@features/campaign-voice-blaster/pages/profile/profile-import/form-mapping-data/form-mapping-data.config'
import { SelectedColumn } from '@features/campaign-voice-blaster/models/profile-import.model'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { MatTooltip } from '@angular/material/tooltip'

@Component({
  selector: 'app-form-mapping-data',
  templateUrl: './form-mapping-data.component.html',
  styleUrls: ['./form-mapping-data.component.scss'],
  imports: [
    TextareaControlComponent,
    MatIcon,
    ReactiveFormsModule,
    MatTableModule,
    CheckboxControlComponent,
    MatCheckbox,
    NgIf,
    CdkDrag,
    CdkDropList,
    MatIconButton,
    TreeSelectControlComponent,
    TextControlComponent,
    SelectControlComponent,
    NgClass,
    JsonPipe,
    CdkDragPreview,
    MatTooltip
  ],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FormMappingDataComponent extends ComponentAbstract {
  @Input() type = ''
  @Output() onChanged = new EventEmitter<any>()
  @ViewChild('table', { static: true }) table: MatTable<any>
  @ViewChild('treeCommonField') treeCommonField: TreeSelectControlComponent
  @ViewChildren('treeSelectControl') treeSelectControls!: QueryList<TreeSelectControlComponent>
  @ViewChildren('fieldInput') fieldInputControls!: QueryList<TextControlComponent>
  $cbCheckDuplicate = cbCheckDuplicate()
  dataTable = []
  displayedColumns: string[] = ['mapping', 'check', 'action']
  arrMappingControls = []
  commonFields = []
  selectedColumns: SelectedColumn[] = []
  formMapping: FormGroup = new FormGroup({})
  cachedMappings: FormArray
  dragDisabled = false
  $cbbCommonField = cbbCommonField()
  $ccbTargetField = cbbTargetField()
  $txtField = txtField()
  maxSelectedCheckboxes: number = 2
  lockControl = false
  action = ''
  defaultCommonSelect = [
    'TEN KH',
    'MA KH',
    'NGAY SINH',
    'GIOI TINH',
    'GTTT',
    'NGAY CAP GTTT',
    'MST',
    'DIA CHI',
    'SDT',
    'Email',
    'SECTOR',
    'LOAI KH',
    'CN CAP 1',
    'MA CN CAP 1'
  ]
  constructor(
    protected override injector: Injector,
    private _businessLogicService: CampaignBusinessLogicService,
    private _campaignService: CampaignService
  ) {
    super(injector)
    this.formMapping = this.itemControl.toFormGroup([])
    this.itemControl.addFormArrayGroup('mappings', this.formMapping)
    this.cachedMappings = this.formMapping.get('mappings') as FormArray
  }

  componentInit() {
    this.action = this.queryParams['action']
    // Lấy profile cuối cùng và xử lý nếu cần
    const lastProfile = this._businessLogicService.getLastProfile()
    if (lastProfile) {
      this._campaignService.getProfile({ profileSetIds: lastProfile.profileSetId }).subscribe(
        (res: any) => {
          if (res.data.content[0]) {
            if (this.action !== 'copy') {
              this.lockMapping()
            }
            this._businessLogicService.updateProfileUploadData(null)
            res.data.content[0].profileAttributes.forEach((x) => {
              this.onAddItem(x.profileAttributeName, x.duplicateCheck, true)
              this.treeCommonField.trashOption(x.profileAttributeName)
            })
          }
        },
        (error) => {}
      )
    }

    this._businessLogicService.profileUploadData$.subscribe((profileUpload) => {
      this.$ccbTargetField.options = profileUpload?.profileAttributes
        ? getSelectOptions(profileUpload.profileAttributes, 'profileAttributeKey', 'profileAttributeName')
        : []

      const data = this.mappings
      data.controls.forEach((x, index) => {
        if (this.treeSelectControls.get(index)) {
          this.treeSelectControls.get(index).item.options = profileUpload?.profileAttributes
            ? getSelectOptions(profileUpload.profileAttributes, 'profileAttributeKey', 'profileAttributeName')
            : []
        }
      })
      setTimeout(() => {
        this.autoMapping()
      }, 500)
    })
  }

  lockMapping(isLock = true) {
    this.$cbbCommonField.readOnly = isLock
    this.lockControl = isLock
    const data = this.mappings
    data.controls.forEach((x, index) => {
      if (this.fieldInputControls.get(index)) {
        this.fieldInputControls.get(index).item.readOnly = true
      }
    })
  }

  drop(event: CdkDragDrop<any>) {
    // Return the drag container to disabled.
    this.dragDisabled = true

    const previousIndex = this.dataTable.findIndex((d) => d === event.item.data)

    // Sắp xếp lại dataTable
    moveItemInArray(this.dataTable, previousIndex, event.currentIndex)
    this.table.renderRows()

    // Cập nhật lại mappings để đồng bộ với dataTable
    const movedControl = this.mappings.at(previousIndex)
    this.mappings.removeAt(previousIndex)
    this.mappings.insert(event.currentIndex, movedControl)

    // Cập nhật lại arrMappingControls để đồng bộ với dataTable
    const movedMappingControl = this.arrMappingControls.splice(previousIndex, 1)[0]
    this.arrMappingControls.splice(event.currentIndex, 0, movedMappingControl)

    // Cập nhật lại treeSelectControls để đồng bộ với dataTable
    let treeSelectControlsArray = this.treeSelectControls.toArray()
    moveItemInArray(treeSelectControlsArray, previousIndex, event.currentIndex)
    this.treeSelectControls.reset(treeSelectControlsArray)
    this.treeSelectControls.notifyOnChanges()
    // Cập nhật lại chỉ số trong dataTable
    this.dataTable.forEach((item, index) => {
      item.no = index + 1
      item.index = index
    })
  }

  onDisabled(b: boolean) {
    this.dragDisabled = b
  }

  get mappings(): any {
    return this.cachedMappings
  }

  // Hàm tiện ích để lấy FormGroup tại một index cụ thể
  getMappingGroup(index: number): FormGroup {
    return this.mappings.at(index) as FormGroup
  }
  getArrayTargetControl(index: number) {
    let control = this.formMapping.get('mappings') as FormArray
    return control.at(index) as FormGroup
  }

  getListControls(arr: any, controlKey: string) {
    let control = arr?.find((x) => x.key == controlKey)
    return control
  }

  onAddItem(field = '', checkDuplicate = false, commonField = false) {
    let textField: TextboxItem = { ...this.$txtField, value: field, readOnly: commonField }
    const ccbTargetField = { ...this.$ccbTargetField }
    const cbCheckDuplicate = { ...this.$cbCheckDuplicate }
    const controls = [textField, ccbTargetField, cbCheckDuplicate]

    this.arrMappingControls.push(controls)
    const formGroup = this.itemControl.toFormGroup(controls)

    formGroup.get(this.$cbCheckDuplicate.key).patchValue(checkDuplicate)
    formGroup.get(textField.key).patchValue(field)
    this.mappings.push(formGroup)
    let lastIndex = this.dataTable.length

    this.dataTable = [
      ...this.dataTable,
      {
        no: lastIndex + 1,
        mapping: '',
        check: '',
        index: lastIndex,
        key: field
      }
    ]
  }

  onDelete(element) {
    this.onRemoveControl(element.index)
    this.treeCommonField.restoreOption(element.key)
    const table = this.dataTable.map((x, index) => {
      return { ...x, no: index + 1, index: index }
    })
    this.dataTable = table
    setTimeout(() => {
      this.restoreOnDelete()
    }, 200)
  }

  onRemoveControl(i: number) {
    this.mappings.removeAt(i)
    this.arrMappingControls.splice(i, 1)
    this.dataTable = this.dataTable
      .filter((x) => x.index !== i)
      .map((y, index) => {
        return { ...y, no: index + 1, index: index }
      })
  }

  /**
   * call view ref
   * DO NOT REMOVE
   */
  validateForm() {
    this.validateAllFields(this.formMapping)
  }

  formChanged($event: any, index: number) {
    // this.treeSelectControls.get(index).item.value = $event.form.getRawValue().profileAttributeName
  }

  addList() {
    this.treeCommonField.form.reset()
    this.commonFields.forEach((x) => {
      if (x && x.key) {
        this.onAddItem(x.key, false, true)
        this.treeCommonField.trashOption(x.key)
      }
    })
    setTimeout(() => {
      this.autoMapping()
    }, 500)
  }

  /**
   * auto mapping field with fields from excel file
   */
  autoMapping() {
    const data = this.mappings
    data.controls.forEach((x, index) => {
      if (this.treeSelectControls.get(index)) {
        this.treeSelectControls.get(index).onDefaultSelected([x.get(this.$txtField.key).value])
        // this.treeSelectControls.get(index).onDefaultSelected(['MA KH'])
      }
    })
  }

  resetMappingFields() {
    const data = this.mappings
    data.controls.forEach((x, index) => {
      if (this.treeSelectControls.get(index)) {
        this.itemControl.resetValueFormControlUntouched(x, this.$ccbTargetField.key)
      }
    })
  }

  restoreOnDelete() {
    const data = this.mappings
    data.controls.forEach((x, index) => {
      if (this.treeSelectControls.get(index)) {
        this.treeSelectControls.get(index).onDefaultSelected([x.get(this.$ccbTargetField.key).value])
      }
    })
  }

  /**
   * default static field
   * case 1: first load
   * case 2: merge with profileMapping upload exist
   * @param $event
   * @param isFirst
   */
  onCommonFieldSelected($event: any, isFirst: boolean) {
    const commonFields = getSelectOptions($event, 'fieldKey', 'fieldName')
    if (isFirst) {
      const result = this.defaultCommonSelect.map((item2) => {
        return commonFields.find((item1) => item1.fieldKey === item2)
      })
      this.commonFields = result
    } else {
      this.commonFields = commonFields
    }
  }

  updateCheckboxState() {
    const selectedCount = this.mappings.controls.filter((control) => control.get(this.$cbCheckDuplicate.key).value).length
    this.mappings.controls.forEach((control) => {
      if (selectedCount >= this.maxSelectedCheckboxes && !control.get(this.$cbCheckDuplicate.key).value) {
        control.get(this.$cbCheckDuplicate.key).disable()
      } else {
        control.get(this.$cbCheckDuplicate.key).enable()
      }
    })
  }

  updateCheckboxByStatus() {
    this.mappings.controls.forEach((control) => {
      control.get(this.$cbCheckDuplicate.key).disable()
    })
  }

  onChangeMapping($event: any, index: number) {
    try {
      this.dataTable[index].key = $event.form.getRawValue().profileAttributeName
    } catch (e) {}
  }

  setDefaultData($event: any) {
    const lastProfile = this._businessLogicService.getLastProfile()
    if (!lastProfile) {
      this.onCommonFieldSelected($event, true)
      this.addList()
    }
  }

  onOpenMenuDropdown($event: any) {
    try {
      const { profileAttributeKey } = $event?.form
      const data = this.mappings
      const valuesSelected = this.mappings.value.map(x => x.profileAttributeKey)
      data.controls.forEach((x, index) => {
        if (this.treeSelectControls.get(index).form.value?.profileAttributeKey == profileAttributeKey) {
          this.treeSelectControls.get(index).item.options = this.$ccbTargetField.options.filter(x => !valuesSelected.includes(x.key))
        }
      })
    } catch (e) {

    }
  }
}
