<form [formGroup]="formMapping">
  <table
    #table
    cdkDropList
    (cdkDropListDropped)="drop($event)"
    cdkDropListData="dataSource"
    [cdkDropListDisabled]="dragDisabled || lockControl"
    formArrayName="mappings"
    class="app-table table-info w-full"
    element.htmlForm
    mat-table
    [dataSource]="dataTable"
    [@.disabled]="true">
<!--    <ng-container matColumnDef="no">-->
<!--      <th mat-header-cell *matHeaderCellDef></th>-->
<!--      <td mat-cell *matCellDef="let element" style="width: 100px">-->
<!--        <div class="d-flex flex-center-row">-->
<!--          <mat-icon fontIcon="more_vert" class="fc-gray-500 dragCursor"></mat-icon>-->
<!--          <div>{{ element.no }}</div>-->
<!--        </div>-->
<!--      </td>-->
<!--    </ng-container>-->
    <ng-container matColumnDef="mapping">
      <th mat-header-cell *matHeaderCellDef>
        <div class='flex-center-row align-items-center w-70 mrb-2 m mrt-2'>
          <app-tree-select-control #treeCommonField formGroupClass='mrb-0 mrt-0' class='w-100' [item]="$cbbCommonField" (onSelected)="onCommonFieldSelected($event, false)" (callBackAfterGetData)="setDefaultData($event)"></app-tree-select-control>
          <button [disabled]="lockControl" class="btn btn-md btn-primary btn-add-list" (click)="addList()">
            <mat-icon class="mbb-icon ic-add_circle_outline mrl-2"></mat-icon>
            <span>Thêm vào danh sách</span>
          </button>
<!--          <button class="btn btn-md btn-primary btn-add-list" (click)="autoMapping()">-->
<!--            <span>Map</span>-->
<!--          </button>-->
        </div>
      </th>
      <td mat-cell *matCellDef="let element" class="pd-0">
        <div class="d-flex align-items-center justify-content-between h-100">
          <button *ngIf="!lockControl" class="dragCursor" (mousedown)="onDisabled(false)">
            <mat-icon matTooltipPosition='above' matTooltip="Kéo thả để thay đổi vị trí" class="mbb-icon ic-drag fc-gray-500 dragCursor"></mat-icon>
          </button>
          <app-text-control
            #fieldInput
            formGroupClass="mrb-0 mrt-0"
            [item]="getListControls(arrMappingControls[element.index], $txtField.key)"
            [form]="getMappingGroup(element.index)"
            (onChanged)="onChangeMapping($event, element.index)"
            (mousedown)="onDisabled(true)"
            class="flex-100"></app-text-control>
          <div>
            <mat-icon class="mbb-icon fc-gray-500 mr-0" fontIcon="link"></mat-icon>
          </div>
<!--          {{ getArrayTargetControl(element.index).get($ccbTargetField.key).value | json}}-->
          <app-tree-select-control #treeSelectControl formGroupClass='mrb-0 mrt-0' class='w-100'
                                   [index]="element.index"
                                   [item]="getListControls(arrMappingControls[element.index], $ccbTargetField.key)"
                                   [form]="getArrayTargetControl(element.index)" (onChanged)="formChanged($event, element.index)" (onOpenMenu)="onOpenMenuDropdown($event)"></app-tree-select-control>
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="check">
      <th mat-header-cell *matHeaderCellDef style="width: 100px">Check trùng</th>
      <td mat-cell *matCellDef="let element" class="pd-0 d-flex justify-content-center align-items-center">
        <mat-checkbox *ngIf="!lockControl" [formControl]="mappings.controls[element.index].get($cbCheckDuplicate.key)" (change)="updateCheckboxState()"></mat-checkbox>
        <mat-checkbox *ngIf="lockControl" [disabled]="true" [checked]="mappings.controls[element.index].get($cbCheckDuplicate.key).value"></mat-checkbox>
      </td>
    </ng-container>
    <ng-container matColumnDef="action">
      <th mat-header-cell *matHeaderCellDef="let element" style="width: 100px">Hành động</th>
      <td mat-cell *matCellDef="let element" class="text-center" style="width: 100px">
        <button mat-icon-button [ngClass]="{ 'hidden': lockControl }" (click)="onDelete(element)" matTooltip="Xóa">
          <mat-icon class="mbb-icon ic-delete"></mat-icon>
        </button>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns" cdkDrag [cdkDragData]="row">
      <table *cdkDragPreview class="app-table table-info drag-preview">
        <tbody>
        <tr>
          <td class="pd-0">
            <div class="d-flex align-items-center justify-content-between h-100">
              <div>
                <mat-icon class="mbb-icon ic-drag fc-gray-500 dragCursor"></mat-icon>
              </div>
              <app-text-control
                formGroupClass="mrb-0 mrt-0"
                [item]="getListControls(arrMappingControls[row.index], $txtField.key)"
                [form]="getMappingGroup(row.index)"
                class="flex-100"></app-text-control>
              <div>
                <mat-icon class="mbb-icon fc-gray-500 mr-0" fontIcon="link"></mat-icon>
              </div>
              <app-tree-select-control #treeSelectControl formGroupClass='mrb-0 mrt-0' class='w-100'
                                       [item]="getListControls(arrMappingControls[row.index], $ccbTargetField.key)"
                                       [form]="getArrayTargetControl(row.index)" (onChanged)="formChanged($event, row.index)" ></app-tree-select-control>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </tr>
  </table>
  <button [disabled]="lockControl" type="button" class="btn btn-white btn-border fc-dark-blue mrt-4 pds-4" (click)="onAddItem()">
    <mat-icon class="mbb-icon ic-plus fc-dark-blue"></mat-icon>
    <span class="fc-dark-blue">Thêm trường</span>
  </button>
</form>
