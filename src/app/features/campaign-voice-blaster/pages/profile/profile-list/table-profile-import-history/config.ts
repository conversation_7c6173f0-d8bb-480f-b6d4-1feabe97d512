import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'

export const tableImportProfileHistoryConfig: TableComponentConfig = {
  type: 'table',
  id: 'table-1',
  data: {
    quickSearchFields: [],
    apiList: '',
    tableTitle: '',
    apiCreate: '',
    displayedColumns: [
      {
        name: 'STT',
        field: 'field_1717236966389',
        path: 'no',
        show: true
      },
      {
        name: 'Tên file',
        field: 'dataFileName',
        path: 'dataFileName',
        show: true
      },
      {
        name: 'Người import',
        field: 'createdBy',
        path: 'createdBy',
        show: true
      },
      {
        name: 'Thời gian import',
        field: 'createdAt',
        path: 'createdAt',
        type: 'datetime',
        show: true
      },
      {
        name: 'Trạng thái',
        field: 'status',
        path: 'status',
        show: true,
        type: 'enumText',
        enumText: [
          {
            key: '0',
            text: 'Thất bại',
            class: 'app-status-reject'
          },
          {
            key: '1',
            text: 'Th<PERSON>nh công',
            class: 'app-status-approved'
          }
        ]
      },
      {
        name: '<PERSON><PERSON><PERSON> quả',
        field: 'result',
        path: 'result',
        show: true
      }
    ],
    pageSize: '10',
    buttonLists: [],
    columnActionLists: [],
    apiDelete: ''
  },
  classes: ''
}
