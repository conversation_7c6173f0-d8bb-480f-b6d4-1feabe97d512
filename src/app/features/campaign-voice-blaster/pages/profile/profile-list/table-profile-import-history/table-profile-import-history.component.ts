import { Component, EventEmitter, Injector, Input, Output, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core'
import { MatTableModule } from '@angular/material/table'
import { AppPaginationComponent, ComponentAbstract, fields, FORM_CONTROL_TYPE, search } from '@shared'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { JsonPipe, NgFor, NgIf, NgSwitch, NgSwitchCase } from '@angular/common'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { TemplateNameDirective, ZoneComponent } from '@mb/ngx-ui-builder'
import { AppTableTreeComponent } from '@shared/components/data-display'

import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { ToggleColumnsPipe } from '../../../../../../shared/pipe/toggle-columns.pipe'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { keydownEvent } from '@shared/components/data-input/directives/keydown.directive'
import { AppTableExpandedComponent } from '@shared/components/data-display/app-table-expand/app-table-expand.component'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { tableImportProfileHistoryConfig } from '@features/campaign-voice-blaster/pages/profile/profile-list/table-profile-import-history/config'

@Component({
  selector: 'app-table-profile-import-history',
  templateUrl: './table-profile-import-history.component.html',
  styleUrls: ['./table-profile-import-history.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    TextControlComponent,
    SelectControlComponent,
    MatIconModule,
    NgFor,
    MatExpansionModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    NoDataComponent,
    AppPaginationComponent,
    ToggleColumnsPipe,
    ZoneComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    TemplateNameDirective,
    TextareaControlComponent,
    AppTableTreeComponent,
    JsonPipe,
    NgSwitchCase,
    NgSwitch,
    FormControlRendererComponent,
    keydownEvent,
    AppTableExpandedComponent
  ]
})
export class TableProfileImportHistoryComponent extends ComponentAbstract {
  @Input() prefix = ''
  @Input() currentQueryFilter = {} // use in case default external query

  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onTableActionClick = new EventEmitter<any>()
  @Output() onChanged = new EventEmitter<any>()
  @Output() onFormInit = new EventEmitter<any>()
  @ViewChild('appTableBodyComponent') _appTableBodyComponent: AppTableTreeComponent
  @ViewChild('outlet', { read: ViewContainerRef }) outletRef: ViewContainerRef
  @ViewChild('content', { read: TemplateRef }) contentRef: TemplateRef<any>

  visibleColumns = []
  dataTable = []
  isSearchByKeyword = false
  formQuickSearch: FormGroup
  $keyword = search()
  $fields = fields()
  tableConfig: TableComponentConfig = tableImportProfileHistoryConfig

  constructor(
    protected override injector: Injector,
    private campaignService: CampaignService,
    private campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.formQuickSearch.get(this.$fields.key).valueChanges.subscribe((f) => {
      this.onChangeFields(f)
    })
  }

  initQuickSearchForm() {
    this.formQuickSearch = this.itemControl.toFormGroup([this.$keyword, this.$fields])
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {}

  protected override afterView() {
    super.afterView()
    this.handlerLoadData()
  }

  handlerLoadData() {
    if (this.tableConfig?.data?.quickSearchFields?.length) {
      this.isSearchByKeyword = false
      this.$fields.options = this.tableConfig?.data.quickSearchFields.map((x) => ({
        key: x.key,
        value: x.text
      }))
      if (this.$fields.options?.length > 0) {
        this.$fields.value = this.$fields.options[0].key
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.value)
      }
    } else {
      this.isSearchByKeyword = true
      this.$fields.options = [
        {
          key: 'keyword',
          value: 'Từ khóa'
        }
      ]
      if (this.$fields.options?.length > 0) {
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.options[0].key)
      }
    }
  }

  /**
   * call API get list
   * @param filter
   */
  search(filter: any) {
    this.filterQuery = filter
    this.indicator.showActivityIndicator(true)
    this._appTableBodyComponent.filterQuery = this.filterQuery
    this._appTableBodyComponent.pageIndex = this.pageIndex
    this._appTableBodyComponent.search(this.filterQuery).then((r: any) => {
      if (r.data?.content && Array.isArray(r.data.content) && r.data.content?.length) {
        // this._appTableBodyComponent.configComponent.data.displayedColumns = this.tableConfig.data.displayedColumns
        this._appTableBodyComponent.setVisibleColumns()
      }
    })
  }

  protected readonly FORM_CONTROL_TYPE = FORM_CONTROL_TYPE

  onChangeFields($event: any) {
    const item = this.$fields.options.find((x) => x.key === $event)
    if (item) {
      this.$keyword.placeholder = `Nhập ${(item?.value || '')}`
      this.cdRef.detectChanges()
    }
  }

  onTableActionHandler($event: any) {}
}
