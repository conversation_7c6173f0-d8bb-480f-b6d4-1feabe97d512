.button-option {
  border: 1px solid #E6E8EE;
  border-radius: 8px;
  display: flex;
  height: 40px;
  justify-content: center;
  align-items: center;
  padding: 10px;
  margin-right: 10px;
}
.search-title {
  font-size: 16px;
}

.button-search-advanced {
  height: 40px;
  background-color: #DADDFA;
  color: #4F5B89;
  border: 1px solid #B2B8CC;
  margin-bottom: 15px;
  border-radius: 8px;
  width: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

::ng-deep .mat-expansion-panel-body {
  padding: 15px!important;
}

.search-box {
  padding: 15px 10px;
}
.button-group {
  justify-self: flex-end;
}
.countSelected {
  margin-right: 10px;
  font-size: 16px;
  letter-spacing: 2%;
  font-weight: 600;
}
