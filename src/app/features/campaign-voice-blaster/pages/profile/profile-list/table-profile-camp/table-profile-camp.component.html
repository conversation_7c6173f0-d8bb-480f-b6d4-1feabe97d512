<div class="panel mrt-4">
  <div class="d-flex justify-content-between search-box">
    <form class="flex-start-row flex-row" [formGroup]="formQuickSearch" style="width: fit-content">
      <ng-container *ngIf="!panelOpenState">
        <div style="min-width: 600px">
          <app-text-control rootClass="pdr-2" [item]="$keyword" [form]="formQuickSearch" (onChanged)="onChangeDebounced($event)"></app-text-control>
        </div>
        <div [hidden]="isSearchByKeyword || tableConfig?.data?.quickSearchFields?.length === 1" style="width: 120px">
          <app-select-control [item]="$fields" [form]="formQuickSearch"></app-select-control>
        </div>
        <button type="submit" class="btn btn-primary mrl-2 pds-4" (click)="quickSearch(0)">
          <mat-icon class="mbb-icon ic-search btn-md mrl-2"></mat-icon>
          <span>Tìm kiếm</span>
        </button>
      </ng-container>
    </form>
    <div *ngIf="type !== 'view' && !isHiddenDeleteProfile" class="flex-center-row flex-row" style="width: fit-content">
      <div class="d-flex countSelected">Đã chọn {{ selectedCount }}</div>
      <div class="d-flex flex-row">
        <button type="button" class="btn btn-white btn-border btn-md fc-dark-blue" (click)="onDeleteProfile()">
          <mat-icon class="mbb-icon ic-delete fc-dark-blue"></mat-icon>
          <span class="fc-dark-blue">Xóa hồ sơ</span>
        </button>
        <button type="button" class="btn btn-white btn-border btn-md fc-dark-blue mrl-2" (click)="onImportProfile()">
          <mat-icon class="mbb-icon ic-plus fc-dark-blue"></mat-icon>
          <span class="fc-dark-blue">Import hồ sơ</span>
        </button>
      </div>
    </div>
  </div>
  <app-table-tree [class.hidden]="!dataTable?.length" #appTableBodyComponent [configComponent]="tableConfig" [isSaveStatePaging]="false" (onTableActionClick)="onTableActionHandler($event)"></app-table-tree>
  <app-no-data *ngIf="!dataTable?.length" message="Chưa có Hồ sơ. Vui lòng Import hồ sơ"></app-no-data>
</div>
