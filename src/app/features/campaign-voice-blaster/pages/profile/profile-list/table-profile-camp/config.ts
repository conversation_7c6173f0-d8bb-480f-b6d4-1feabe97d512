import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { PATH_API } from '@features/campaign-voice-blaster/constants'

export const tableImportProfileConfig: TableComponentConfig = {
  type: 'table',
  id: 'table-tableImportProfileConfig',
  data: {
    disableAutoCallOnChange: true,
    quickSearchFields: [
      {
        key: 'customerCode',
        text: 'Mã KH'
      },
      {
        key: 'customerEmail',
        text: 'Email'
      },
      {
        key: 'customerPhoneNumber',
        text: 'Số điện thoại'
      }
    ],
    apiList: PATH_API.PROFILE_CONTENT_V2,
    apiListMethod: 'POST',
    tableTitle: '',
    apiCreate: '',
    displayedColumns: [
      {
        name: '',
        field: 'select',
        path: 'profileContentId',
        show: true,
        type: 'checkbox-selection-all'
      },
      {
        name: 'STT',
        field: 'STT',
        path: 'no',
        show: true
      }
    ],
    pageSize: '10',
    buttonLists: [],
    columnActionLists: [],
    apiDelete: ''
  },
  classes: ''
}

export const tableImportProfileConfigView: TableComponentConfig = {
  type: 'table',
  id: 'table-1',
  data: {
    disableAutoCallOnChange: true,
    quickSearchFields: [
      {
        key: 'customerCode',
        text: 'Mã KH'
      },
      {
        key: 'customerEmail',
        text: 'Email'
      },
      {
        key: 'customerPhoneNumber',
        text: 'SĐT'
      }
    ],
    apiList: PATH_API.PROFILE_CONTENT_V2,
    apiListMethod: 'POST',
    tableTitle: '',
    apiCreate: '',
    displayedColumns: [{
      name: 'STT',
      field: 'STT',
      path: 'no',
      show: true
    }],
    pageSize: '10',
    buttonLists: [],
    columnActionLists: [],
    apiDelete: ''
  },
  classes: ''
}
