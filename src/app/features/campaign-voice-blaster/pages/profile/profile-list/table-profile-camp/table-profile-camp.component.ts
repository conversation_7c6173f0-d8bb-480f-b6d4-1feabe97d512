import { Component, EventEmitter, Injector, Input, OnChanges, Output, SimpleChanges, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core'
import { MatTableModule } from '@angular/material/table'
import { AppPaginationComponent, ComponentAbstract, fields, FORM_CONTROL_TYPE, MessageSeverity, search } from '@shared'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { JsonPipe, NgF<PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON>witchCase } from '@angular/common'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { TemplateNameDirective, ZoneComponent } from '@mb/ngx-ui-builder'
import { AppTableTreeComponent } from '@shared/components/data-display'

import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { ToggleColumnsPipe } from '../../../../../../shared/pipe'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { keydownEvent } from '@shared/components/data-input/directives/keydown.directive'
import { AppTableExpandedComponent } from '@shared/components/data-display/app-table-expand/app-table-expand.component'
import { tableImportProfileConfig, tableImportProfileConfigView } from '@features/campaign-voice-blaster/pages/profile/profile-list/table-profile-camp/config'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { CampaignEnumStatus } from '@features/campaign-voice-blaster/constants'
import { firstValueFrom } from 'rxjs'

@Component({
  selector: 'table-profile-camp',
  templateUrl: './table-profile-camp.component.html',
  styleUrls: ['./table-profile-camp.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    TextControlComponent,
    SelectControlComponent,
    MatIconModule,
    NgFor,
    MatExpansionModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    NoDataComponent,
    AppPaginationComponent,
    ToggleColumnsPipe,
    ZoneComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    TemplateNameDirective,
    TextareaControlComponent,
    AppTableTreeComponent,
    JsonPipe,
    NgSwitchCase,
    NgSwitch,
    FormControlRendererComponent,
    keydownEvent,
    AppTableExpandedComponent
  ]
})
export class TableProfileImportComponent extends ComponentAbstract implements OnChanges {
  @Input() prefix = ''
  @Input() currentQueryFilter = {} // use in case default external query
  @Input() type = ''
  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onTableActionClick = new EventEmitter<any>()
  @Output() onChanged = new EventEmitter<any>()
  @Output() onFormInit = new EventEmitter<any>()
  @ViewChild('appTableBodyComponent') _appTableBodyComponent: AppTableTreeComponent
  @ViewChild('outlet', { read: ViewContainerRef }) outletRef: ViewContainerRef
  @ViewChild('content', { read: TemplateRef }) contentRef: TemplateRef<any>

  visibleColumns = []
  dataTable = []
  panelOpenState = false
  isSearchAdvanced = false
  isSearchByKeyword = false
  formQuickSearch: FormGroup
  $keyword = search()
  $fields = fields()
  tableConfig: TableComponentConfig = tableImportProfileConfig
  selectedCount = 0
  selectedData = []
  isHiddenDeleteProfile = false
  action = ''

  constructor(
    protected override injector: Injector,
    private campaignService: CampaignService,
    private campaignBusinessLogicService: CampaignBusinessLogicService
  ) {
    super(injector)
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.formQuickSearch.get(this.$fields.key).valueChanges.subscribe((f) => {
      this.onChangeFields(f)
    })
  }

  initQuickSearchForm() {
    this.formQuickSearch = this.itemControl.toFormGroup([this.$keyword, this.$fields])
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {
    this.action = this.queryParams['action']
  }

  protected override afterView() {
    super.afterView()
    this.handlerLoadData()
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('changes', changes?.type.currentValue)
    if (changes?.type.firstChange && changes?.type.currentValue === 'view') {
      this.tableConfig = tableImportProfileConfigView
    }
  }

  handlerLoadData() {
    if (this.tableConfig?.data?.quickSearchFields?.length) {
      this.isSearchByKeyword = false
      this.$fields.options = this.tableConfig?.data.quickSearchFields.map((x) => ({
        key: x.key,
        value: x.text
      }))
      if (this.$fields.options?.length > 0) {
        this.$fields.value = this.$fields.options[0].key
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.value)
      }
    } else {
      this.isSearchByKeyword = true
      this.$fields.options = [
        {
          key: 'keyword',
          value: 'Từ khóa'
        }
      ]
      if (this.$fields.options?.length > 0) {
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.options[0].key)
      }
    }
  }
  /**
   * handler quick search
   */
  quickSearch(pageIndex: number) {
    if (this.formQuickSearch.invalid) return
    this.pageIndex = pageIndex
    this.isSearchAdvanced = false
    this.filterQuery = { profileSetIds: this.filterQuery?.profileSetIds, ...this.itemControl.getQueryQuickSearchForm(this.formQuickSearch) }
    this.search({ ...this.filterQuery, ...this.currentQueryFilter })
  }

  /**
   * call API get list
   * @param filter
   */
  search(filter: any) {
    if (!filter?.profileSetIds) return
    this.filterQuery = filter
    this.indicator.showActivityIndicator(true)
    this._appTableBodyComponent.filterQuery = this.filterQuery
    this._appTableBodyComponent.pageIndex = this.pageIndex
    this._appTableBodyComponent.search(this.filterQuery).then((r: any) => {
      this.dataTable = r?.data?.content || []
      if (r.data?.content && Array.isArray(r.data.content) && r.data.content?.length) {
        const row = r.data.content[0]
        Object.keys(row)
          .filter((x) => x !== 'profileContentId')
          .forEach((k) => {
            this.tableConfig.data.displayedColumns.push({
              name: k,
              field: k,
              path: `${k}`,
              show: true
            })
          })
        // this._appTableBodyComponent.configComponent.data.displayedColumns = this.tableConfig.data.displayedColumns
        this._appTableBodyComponent.setVisibleColumns()
      }
    })
  }

  onTableActionHandler($event) {
    // this.onTableActionClick.emit($event)
    console.log($event)
    if ($event?.type === 'selection') {
      this.selectedData = $event?.data || []
      if ($event.isCheckAll) {
        this.selectedCount = $event?.totalItem
      } else {
        this.selectedCount = this.selectedData.length
      }
    }
  }

  protected readonly FORM_CONTROL_TYPE = FORM_CONTROL_TYPE

  onChangeFields($event: any) {
    const item = this.$fields.options.find((x) => x.key === $event)
    if (item) {
      this.$keyword.placeholder = `Nhập ${item?.value || ''}`
      this.cdRef.detectChanges()
    }
  }

  onChangeDebounced($event: any) {}

  onDeleteProfile() {
    if (!this.selectedData?.length) {
      return
    }
    const profileContentIds = this.selectedData.map((x) => x.profileContentId).filter((x) => x)
    this.campaignBusinessLogicService.profileContentIsSoftDelete.next(profileContentIds)

    if (this.type === 'edit') {
      this.toastr.showToastr(`Xóa ${this.selectedCount} bản ghi thành công`, '', MessageSeverity.success)
      const selectionUnCheckSelected = this._appTableBodyComponent.selectionUnCheck.selected
      const profileContentIdsExclude = selectionUnCheckSelected.map((x) => x.profileContentId).filter((x) => x)
      this.filterQuery = { ...this.filterQuery,
        softDeletedProfileContentIds: profileContentIds, // id muon xoa
        profileContentIds: profileContentIdsExclude, // giữ lại
        softDeletedProfileSetIds: profileContentIdsExclude // set muon xoa
      }
      this.selectedData = []
      this.selectedCount = 0
      if (this._appTableBodyComponent.isCheckAll) {
        this._appTableBodyComponent.isCheckAll = false
        this._appTableBodyComponent.selectedCount = 0
      }
      this._appTableBodyComponent.selection.clear(true)
      this.search(this.filterQuery)
    } else {
      //case CREATE
      this.callApiDelete()
    }
  }

  callApiDelete() {
    const deletedProfileContentIds = this.campaignBusinessLogicService.profileContentIsSoftDelete.value || []
    if (!deletedProfileContentIds?.length) {
      return
    }

    const selectionUnCheckSelected = this._appTableBodyComponent.selectionUnCheck.selected
    const profileContentIdsExclude = selectionUnCheckSelected.map((x) => x.profileContentId).filter((x) => x)
    const profileSetIds = this.filterQuery?.profileSetIds

    this.campaignService.deleteProfileContentIds({
        profileSetIds: this._appTableBodyComponent.isCheckAll ? profileSetIds : [],
        profileContentIds: this._appTableBodyComponent.isCheckAll ? [] : deletedProfileContentIds,
        excludeProfiles: profileContentIdsExclude
    }
    ).subscribe((res) => {
      this.toastr.showToastr(`Xóa ${this.selectedCount} bản ghi thành công`, '', MessageSeverity.success)
      this.campaignBusinessLogicService.profileContentIsSoftDelete.next([])
      this.selectedData = []
      this.selectedCount = 0
      this._appTableBodyComponent.selectedCount = 0
      if (this._appTableBodyComponent.isCheckAll)
      {
        this._appTableBodyComponent.isCheckAll = false
        this._appTableBodyComponent.selectionUnCheck.clear(true)
      }
      this._appTableBodyComponent.selection.clear(true)
      this.search(this.filterQuery)
    })
  }

  onImportProfile() {
    this.campaignBusinessLogicService.buttonClick.next({ type: 'profile', value: 'import' })
  }

  async handlerStatusCamp() {
    const campaignData = await firstValueFrom(this.campaignBusinessLogicService.campaign$)
    if (campaignData && Number(campaignData?.campaignStatus) === CampaignEnumStatus.RUNNING && this.action !== 'copy') {
      this.tableConfig = tableImportProfileConfigView
    }
  }
}
