import { Component, Injector, Input, ViewChild, ViewEncapsulation } from '@angular/core'
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs'
import { ComponentAbstract } from '@shared'
import { AppPanelComponent } from '@shared/components/element/app-panel/app-panel.component'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { CampaignService } from '@features/campaign-voice-blaster/services/campaign.service'
import { firstValueFrom, map, takeUntil } from 'rxjs'
import { PATH_API } from '@features/campaign-voice-blaster/constants'
import { TableProfileImportHistoryComponent } from '@features/campaign-voice-blaster/pages/profile/profile-list/table-profile-import-history/table-profile-import-history.component'
import { CustomerListComponent } from '@features/campaign-voice-blaster/pages/detail/customer-list/customer-list.component'
import {
  CustomerCalledListComponent
} from '@features/campaign-voice-blaster/pages/detail/customer-called-list/customer-called-list.component'

@Component({
  selector: 'campaign-profile',
  standalone: true,
  templateUrl: './campaign-profile.component.html',
  styleUrl: './campaign-profile.component.scss',
  imports: [
    MatTabsModule,
    AppPanelComponent,
    CustomerListComponent,
    CustomerCalledListComponent
  ],
  encapsulation: ViewEncapsulation.None
})
export class CampaignProfileComponent extends ComponentAbstract {

  @ViewChild('customerListComponent') customerListComponent: CustomerListComponent
  @ViewChild('customerCalledListComponent') customerCalledListComponent: CustomerCalledListComponent

  @ViewChild('tableProfileImportHistoryComponent') tableProfileImportHistoryComponent: TableProfileImportHistoryComponent

  @Input() type = 'create'
  action = ''
  selectedTabIndex = 0
  private previousProfile: any[] = []

  constructor(
    protected override injector: Injector,
    private _campaignBusinessLogicService: CampaignBusinessLogicService,
    private _campaignService: CampaignService
  ) {
    super(injector)
  }

  componentInit(): void {
    this.action = this.queryParams['action']
    // this.updateProfile()
    // this._campaignBusinessLogicService.campaign$
    //   .pipe(
    //     map((data) => data?.profiles),
    //     distinctUntilChanged(),
    //     takeUntil(this.ngUnsubscribe)
    //   )
    //   .subscribe((newProfile: any[]) => {
    //     // this.updateProfile()
    //   })
  }


  onButtonClick($event: any) {}

  onTableActionClick($event: any) {
    console.log($event)
    if ($event?.type === 'selection') {
    }
  }

  onSelectedTab($event: MatTabChangeEvent) {
    console.log('tab profile change', $event)
    const { index } = $event
    if (index === 0) {
      // this.customerListComponent.loadTable()
    } else if (index === 1) {
      // this.customerCalledListComponent.loadTable()
    }
  }

  async loadImportHistory() {
    try {
      const campaignData = await firstValueFrom(this._campaignBusinessLogicService.campaign$)
      if (campaignData && campaignData?.profiles?.length) {
        const profileSetIds = campaignData.profiles.map((profile) => profile.profileSetId).join(',')
        this.tableProfileImportHistoryComponent.tableConfig.data.apiList = PATH_API.PROFILE
        this.tableProfileImportHistoryComponent.tableConfig.pageIndex = 0
        this.tableProfileImportHistoryComponent.search({ profileSetIds })
      } else {
        this.tableProfileImportHistoryComponent._appTableBodyComponent.dataTable = []
        this.tableProfileImportHistoryComponent._appTableBodyComponent.loadDataTable()
      }
    } catch (error) {
      console.error('Error saving profile subject:', error)
    }
  }
}
