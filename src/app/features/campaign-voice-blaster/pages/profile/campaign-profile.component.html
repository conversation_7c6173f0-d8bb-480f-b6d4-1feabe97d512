<app-panel>
  <div content>
    <mat-tab-group
      animationDuration="0ms"
      [(selectedIndex)]="selectedTabIndex"
      (selectedTabChange)="onSelectedTab($event)"
      mat-tab-group
      mat-stretch-tabs="false"
      mat-align-tabs="start"
      class="custom-tab-bar-profile"
    >
      <mat-tab label="Tất cả" class="custom-tab">
        <app-customer-campaign-voice-blaster-list #customerListComponent></app-customer-campaign-voice-blaster-list>
<!--        <table-profile-camp #table prefix='management-list-profile' (onButtonClick)="onButtonClick($event)" (onTableActionClick)="onTableActionClick($event)"></table-profile-camp>-->
      </mat-tab>
      <mat-tab label="Đã gọi" class="custom-tab">
        <app-customer-called-campaign-voice-blaster-list #customerCalledListComponent/>
        <!--        <table-profile-camp #table prefix='management-list-profile' (onButtonClick)="onButtonClick($event)" (onTableActionClick)="onTableActionClick($event)"></table-profile-camp>-->
      </mat-tab>
<!--      <mat-tab label="Lịch sử import" class="custom-tab">-->
<!--        <app-table-profile-import-history #tableProfileImportHistoryComponent></app-table-profile-import-history>-->
<!--      </mat-tab>-->
    </mat-tab-group>
  </div>
</app-panel>
