export const configComponent: any = [
  // form sale content
  {
    type: 'formControl',
    id: 'sale-close-content-form',
    data: {
      countMaxLength: false,
      value: '',
      key: 'questionId',
      required: false,
      order: 1,
      controlType: 'treeselect',
      type: 'multiple',
      placeholder: 'Chọn nội dung chốt sale',
      focus: false,
      options: [],
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      paramData: {
        isTag: false,
        url: 'dcmcore-campaign/v1/sale-close-content',
        preLoad: true,
        defaultKeySearch: '',
        key: 'questionId',
        value: '["questionContent"]',
        typeheadKey: 'questionContent',
        clientFilter: true,
        pageSize: 999999,
      },
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      clearable: true
    },
    classes: 'w-50'
  },
  //table sale content
  {
    type: 'table-expand',
    id: 'sale-close-content-table',
    data: {
      quickSearchFields: [
        {
          key: 'questionContent',
          text: 'Nội dung chốt sale'
        }
      ],
      apiList: 'dcmcore-campaign/v1/sale-close-content',
      displayedColumns: [
        {
          name: 'STT',
          field: 'field_1717985042586',
          path: 'no',
          show: true
        },
        {
          name: 'Nội dung chốt sale',
          field: 'questionContent',
          path: 'questionContent',
          show: true,
          type: 'textbox',
          isQuickSearch: true,
          isAdvancedSearch: true,
          maxLength: '60',
          pathExpand: 'answerContent',
          showColumnExpand: true
        },
        {
          name: 'Mang tính quyết định?',
          field: 'isDecisive',
          path: 'isDecisive',
          show: true,
          type: 'checkbox',
          pathExpand: 'isDecisive',
          showColumnExpand: true
        },
        {
          name: 'Hành động',
          field: 'action',
          path: '',
          show: true
        }
      ],
      pageSize: 10,
      buttonLists: [],
      columnActionLists: [
        {
          title: 'Xóa',
          type: 'DELETE',
          class: '',
          scope: '03_DELETE',
          icon: 'ic-delete',
          navigationType: 'delete',
          routerName: ':questionId'
        }
      ],
      apiDelete: '',
      isTreeData: true,
      isStaticTable: false,
      isExpandTable: true,
      childrenAttr: 'answers',
      expandColumns: [],
      hideSearchAdvanced: true
    },
    classes: ''
  },
  {
    type: 'formControl',
    id: 'form-type',
    data: {
      countMaxLength: false,
      value: '0',
      key: 'type',
      required: false,
      order: 1,
      controlType: 'ngselect',
      type: 'single',
      placeholder: 'Trạng thái',
      focus: false,
      options: [
        {
          key: '0',
          value: 'Nhóm nhân sự'
        },
        {
          key: '1',
          value: 'Level nhân sự'
        }
      ],
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      paramData: {
        isTag: false,
        url: '',
        preLoad: true,
        defaultKeySearch: '',
        key: '',
        value: ['']
      },
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      minRow: '2',
      checkBoxKey: 'value',
      defaultValue: '0'
    }
  },
  {
    type: 'formControl',
    id: 'groups-form',
    data: {
      countMaxLength: false,
      value: '',
      key: 'teamIds',
      required: false,
      order: 1,
      controlType: 'treeselect',
      type: 'multiple',
      placeholder: 'Chọn nhóm nhân sự',
      focus: false,
      options: [],
      paramData: {
        isTag: false,
        url: 'dcmcore-administration/v1/team',
        preLoad: true,
        defaultKeySearch: '',
        key: 'teamId',
        value: '["teamName"]',
        typeheadKey: 'teamName'
      },
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      clearable: true,
      maxLength: 40
    }
  },
  {
    type: 'formControl',
    id: 'level-form',
    data: {
      countMaxLength: false,
      value: '',
      key: 'employeeLevels',
      required: false,
      order: 1,
      controlType: 'treeselect',
      type: 'multiple',
      placeholder: 'Chọn level nhân sự',
      focus: false,
      options: [
        {
          key: '1',
          value: 'Level 1'
        },
        {
          key: '2',
          value: 'Level 2'
        },
        {
          key: '3',
          value: 'Level 3'
        },
        {
          key: '4',
          value: 'Level 4'
        },
        {
          key: '5',
          value: 'Level 5'
        }
      ],
      paramData: {
        key: 'key',
        value: ['value'],
        clientFilter: true,
      },
      checkBoxKey: 'key',
      layout: '100',
      directives: '',
      customDirectives: '',
      customValidate: ['customMessageError'],
      updateOn: '',
      template: '',
      reset: false,
      title: false,
      pattern: '',
      checked: false,
      readOnly: false,
      clearable: true,
      maxLength: 40
    }
  },
  //table sale content
  {
    type: 'table',
    id: 'employee-table',
    data: {
      apiList: '',
      tableTitle: 'nhóm người dùng',
      apiCreate: '',
      displayedColumns: [
        {
          name: 'STT',
          field: 'field_1717236966389',
          path: 'no',
          show: true
        },
        {
          name: 'Họ và tên',
          field: 'agentFullName',
          path: 'agentFullName',
          show: true,
          type: 'textbox'
        },
        {
          name: 'User',
          field: 'agentAdUser',
          path: 'agentAdUser',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Campaign đang thực hiện',
          field: 'runningCampaignParticipated',
          path: 'runningCampaignParticipated',
          show: true,
          type: 'decimal'
        },
        {
          name: 'Hồ sơ được phân giao',
          field: 'assignedProfiles',
          path: 'assignedProfiles',
          show: true,
          type: 'decimal',
        },
        {
          name: 'Hồ sơ chưa thực hiện',
          field: 'uncompletedProfiles',
          path: 'uncompletedProfiles',
          show: true,
          type: 'decimal',
        },
        {
          name: 'Hồ sơ đã thực hiện',
          field: 'completedProfiles',
          path: 'completedProfiles',
          show: true,
          type: 'decimal',
        },
        {
          name: 'Nhóm',
          field: 'teams',
          path: 'teams',
          show: true,
          type: 'mapAll',
          maxLength: '40',
          mapField: 'teamName'
        },
        {
          name: 'Level',
          field: 'employeeLevel',
          path: 'employeeLevel',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Hành động',
          field: 'action',
          path: '',
          show: true
        }
      ],
      pageSize: 10,
      columnActionLists: [
        {
          title: 'Xóa',
          type: '',
          class: '',
          scope: '06_SYNC',
          icon: 'ic-delete',
          navigationType: 'emit',
          routerName: 'delete'
        }
      ],
      apiDelete: 'dcmcore-administration/v1/team',
      isTreeData: false,
      isStaticTable: true
    },
    classes: ''
  }
]

export const tableConfigSaleContentDetail: any = {
  type: 'table-expand',
  id: 'sale-close-content-table-detail',
  data: {
    quickSearchFields: [
      {
        key: 'questionContent',
        text: 'Nội dung chốt sale'
      }
    ],
    apiList: 'dcmcore-campaign/v1/sale-close-content',
    displayedColumns: [
      {
        name: 'STT',
        field: 'field_1717985042586',
        path: 'no',
        show: true
      },
      {
        name: 'Nội dung chốt sale',
        field: 'questionContent',
        path: 'questionContent',
        show: true,
        type: 'textbox',
        isQuickSearch: true,
        isAdvancedSearch: true,
        maxLength: '60',
        pathExpand: 'answerContent',
        showColumnExpand: true
      },
      {
        name: 'Mang tính quyết định?',
        field: 'isDecisive',
        path: 'isDecisive',
        show: true,
        type: 'checkbox',
        pathExpand: 'isDecisive',
        showColumnExpand: true
      }
    ],
    pageSize: 10,
    buttonLists: [],
    columnActionLists: [],
    apiDelete: '',
    isTreeData: true,
    isStaticTable: false,
    isExpandTable: true,
    childrenAttr: 'answers',
    expandColumns: [],
    hideSearchAdvanced: true
  },
  classes: ''
}

export const tableConfigEmployeeDetail: any = {
  type: 'table',
  id: 'employee-table',
  data: {
    apiList: '',
    tableTitle: 'nhóm người dùng',
    apiCreate: '',
    displayedColumns: [
      {
        name: 'STT',
        field: 'field_1717236966389',
        path: 'no',
        show: true
      },
      {
        name: 'Họ và tên',
        field: 'agentFullName',
        path: 'agentFullName',
        show: true,
        type: 'textbox'
      },
      {
        name: 'User',
        field: 'agentAdUser',
        path: 'agentAdUser',
        show: true,
        type: 'textbox'
      },
      {
        name: 'Hồ sơ được phân giao',
        field: 'assignedProfiles',
        path: 'assignedProfiles',
        show: true,
        type: 'decimal',
        sort: true,
        clientSort: true,
      },
      {
        name: 'Hồ sơ chưa thực hiện',
        field: 'uncompletedProfiles',
        path: 'uncompletedProfiles',
        show: true,
        type: 'decimal',
        sort: true,
        clientSort: true,
      },
      {
        name: 'Hồ sơ đã thực hiện',
        field: 'completedProfiles',
        path: 'completedProfiles',
        show: true,
        type: 'decimal',
        sort: true,
        clientSort: true,
      },
      {
        name: 'Nhóm',
        field: 'teams',
        path: 'teams',
        show: true,
        type: 'mapAll',
        maxLength: '60',
        mapField: 'teamName'
      },
      {
        name: 'Level',
        field: 'employeeLevel',
        path: 'employeeLevel',
        show: true,
        type: 'textbox'
      }
    ],
    pageSize: 10,
    columnActionLists: [],
    apiDelete: 'dcmcore-administration/v1/team',
    isTreeData: false,
    isStaticTable: true
  },
  classes: ''
}
