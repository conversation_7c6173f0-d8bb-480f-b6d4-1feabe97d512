import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { PATH_API } from '../constants'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'

@Injectable({
  providedIn: 'root'
})
export class PhoneNumberHistoryService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * tạo
   * @param body
   */
  create(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.CREATE_UPDATE,
      body
    }
    return this.httpClient.post(options)
  }

  /**
   * sửa
   * @param body
   * @param id
   */
  update(body: any, id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`,
      body
    }
    return this.httpClient.put(options)
  }

  /**
   * chi tiết
   * @param id
   */
  getDetail(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`
    }
    return this.httpClient.get(options)
  }

  /**
   * xóa
   * @param id
   */
  delete(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE_UPDATE}/${id}`
    }
    return this.httpClient.delete(options)
  }
}
