import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: 'Lịch sử đầu số',
    loadComponent: async () => (await import('./pages/list/phone-number-history-list.component')).PhoneNumberHistoryListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  }
  // {
  //   path: 'create',
  //   title: 'Thêm mới đầu số',
  //   loadComponent: async () => (await import('./pages/create/address-create.component')).AddressCreateComponent,
  //   data: { breadcrumb: 'Thêm mới đầu số' }
  // },
  // {
  //   path: 'edit/:id',
  //   title: 'Chỉnh sửa đầu số',
  //   loadComponent: async () => (await import('./pages/edit/address-edit.component')).AddressEditComponent,
  //   data: { breadcrumb: 'Chỉnh sửa đầu số' }
  // }
]
