import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: 'Danh mục nguồn gọi',
    loadComponent: async () => (await import('./pages/list/customer-source.component')).CustomerSourceComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
    // canActivate: [RoleGuard],
  },
  {
    path: 'create',
    title: 'Thêm mới nguồn gọi',
    loadComponent: async () => (await import('./pages/create/customer-source.component')).CustomerSourceComponent,
    data: { breadcrumb: 'Thêm mới nguồn gọi'}
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa nguồn ngọi',
    loadComponent: async () => (await import('./pages/edit/customer-source.component')).Customer_sourceComponent,
    // canActivate: [RoleGuard],
    data: { breadcrumb: 'Chỉnh sửa nguồn gọi'}
  }
]
