import { Component, ElementRef, Injector, isDevMode, ViewContainerRef } from '@angular/core'
import { ComponentAbstract, RESOURCE_CODE } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { NgForOf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { ROUTES_NAME_SERVICE } from '../../../../app.routes'

@Component({
  selector: 'app-user-list',
  templateUrl: './voice-brand-name-list.component.html',
  styleUrls: ['./voice-brand-name-list.component.scss'],
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent
  ]
})
export class VoiceBrandNameListComponent extends ComponentAbstract {
  constructor(protected override injector: Injector) {
    super(injector)
    this.initBuilderUIConfig('pbx_management-voice-brand-name-list').then((r) => {})
    // this.initBuilderUIConfigLocal('management-product-list').then((r) => {
    //   this.config = r
    // })
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    if ($event.type === 'copy') {
      this.goTo(ROUTES_NAME_SERVICE.CREATE, { action: $event.type, id: $event?.row?.serviceCode })
    }
  }

  onButtonClick($event: any) {}
}
