import { Component, Injector } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, Status, TreeDropdownItem } from '@shared'
import { finalize, takeUntil } from 'rxjs'
import { PageBuilderComponent } from '../../../../shared/components/page-builder/page-builder.component'
import { FooterComponent } from '../../../../shared/components/section/footer/footer.component'
import {
  ROUTES_NAME_SYSTEM_MANAGEMENT_VOICE_BRAND_NAME
} from '../../../../app.routes'
import { MatIcon } from '@angular/material/icon'
import { UserService } from '@features/user/services/user.service'
import { environment } from '@env/environment'
import { VoiceBrandNameService } from '@features/voice-brand-name/services/voice-brand-name.service'

@Component({
  selector: 'app-user-create',
  templateUrl: './voice-brand-name-create.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon],
  styleUrls: ['./voice-brand-name-create.component.scss']
})
export class VoiceBrandNameCreateComponent extends ComponentAbstract {
  id: any
  firstCall = true
  constructor(
    protected override injector: Injector,
    private _service: VoiceBrandNameService
  ) {
    super(injector)
    this.initBuilderUIConfig('pbx_management-voice-brand-name-create').then((r) => {
    })
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
  }

  onSave() {
    if (this._page.form.valid) {
      const formData = this._page.formRawValue
      this.logger(formData, 'formDataCreate')

      this.indicator.showActivityIndicator()
      const body = {
        ...formData,
        "telcoStatus": Number(formData?.telcoStatus)
      }
      this.onCreateData(body)
    } else {
      this._page.validateForm()
    }
    return this._page.form.valid
  }

  onCreateData(body: any) {
    this._service
      .create(body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this._page.form.reset()
            this.goTo(ROUTES_NAME_SYSTEM_MANAGEMENT_VOICE_BRAND_NAME.LIST)
            this.showDialogSuccessI18n('', 'Thêm mới nhà mạng <br/>thành công')
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Thêm mới nhà mạng <br/>thất bại')
        }
      )
  }

  onBuilderChanged($event: any) {
    this.logger($event, 'formChanged')
    if ($event.type == 'formData') {
      // if ($event.event.item.key === 'productCode') {
      //   if (!this.id) {
      //     this.firstCall = false
      //   }
      //   if (!this.firstCall) {
      //     if ($event.event?.data && Array.isArray($event.event?.data) && $event.event?.data[0]?.data?.productCode) {
      //       const dataEdit = {
      //         businessCode: new TreeDropdownItem({
      //           value: undefined,
      //           required: true,
      //           placeholder: 'Chọn nghiệp vụ',
      //           readOnly: false,
      //           paramData: {
      //             url: `${environment.services.pbxCms}/v1/service-category/${$event.event.data[0].data?.productCode}/unused-businesses`
      //           }
      //         })
      //       }
      //       this.patchValuesFormBuilder(dataEdit)
      //     } else {
      //       const dataEdit = {
      //         businessCode: new TreeDropdownItem({
      //           value: undefined,
      //           required: true,
      //           placeholder: 'Chọn nghiệp vụ',
      //           readOnly: true,
      //           paramData: {}
      //         })
      //       }
      //       this.patchValuesFormBuilder(dataEdit)
      //     }
      //   }
      //   this.firstCall = false
      // }
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.location.back()
        break
      case BUTTON_SAVE.typeBtn:
        this.onSave()
        break
    }
  }
}
