import { Component, Injector, Input } from '@angular/core'
import { BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, Status, TreeDropdownItem } from '@shared'
import { finalize, takeUntil } from 'rxjs'
import { ROUTES_NAME_SYSTEM_MANAGEMENT_VOICE_BRAND_NAME } from '../../../../app.routes'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { environment } from '@env/environment'
import { VoiceBrandNameService } from '@features/voice-brand-name/services/voice-brand-name.service'

@Component({
  selector: 'app-user-edit',
  templateUrl: './voice-brand-name-edit.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon],
  styleUrls: ['./voice-brand-name-edit.component.scss']
})
export class VoiceBrandNameEditComponent extends ComponentAbstract {
  @Input() public idEdit: string
  dataDetail
  firstCall = true
  constructor(
    protected override injector: Injector,
    private _service: VoiceBrandNameService
  ) {
    super(injector)
    this.idEdit = this.route.snapshot.paramMap.get('id')
    this.initBuilderUIConfig('pbx_management-voice-brand-name-edit').then((r) => {
      if (this.idEdit) {
        this.form = this.itemControl.toFormGroup([])
        setTimeout(() => {
          this.getDetailById()
        }, 500)
      }
    })
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_UPDATE)
  }

  getDetailById() {
    this.indicator.showActivityIndicator(true)
    this._service
      .getDetail(this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS && res?.data) {
            const { data } = res
            this.dataDetail = data
            console.log(this._page.form)
            const dataEdit = {
              ...data
            }

            this.logger(dataEdit, 'data Edit')
            this.patchValuesFormBuilder(this.transformObject(dataEdit))
          } else {
            this.goTo404()
          }
        },
        () => {}
      )
  }

  onSave() {
    if (this._page.form.valid) {
      const formData = this._page.formRawValue
      this.logger(formData, 'formDataUpdate')
      this.indicator.showActivityIndicator()
      const body = {
        ...formData
      }
      this.onUpdateData(body)
    } else {
      this._page.validateForm()
    }
    return this._page.form.valid
  }

  onUpdateData(body: any) {
    this._service
      .update(body, this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.goTo(ROUTES_NAME_SYSTEM_MANAGEMENT_VOICE_BRAND_NAME.LIST)
            this.showDialogSuccessI18n('', 'Chỉnh sửa nhà mạng <br/>thành công')
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Chỉnh sửa nhà mạng <br/>thất bại')
        }
      )
  }

  onBuilderChanged($event: any) {
    this.logger($event, 'formChanged')
    if ($event.type == 'formData') {
      if ($event.event.item.key === 'productCode') {
        if (!this.firstCall) {
          if ($event.event?.data && Array.isArray($event.event?.data) && $event.event?.data[0]?.data?.productCode) {
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: undefined,
                required: true,
                readOnly: false,
                paramData: {
                  url: `${environment.services.pbxCms}/v1/service-category/${$event.event.data[0].data?.productCode}/unused-businesses`,
                  queryStringOnInit: ''
                }
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          } else {
            const dataEdit = {
              businessCode: new TreeDropdownItem({
                value: undefined,
                required: true,
                readOnly: true,
                paramData: {
                  url: ``,
                  queryStringOnInit: ''
                }
              })
            }
            this.patchValuesFormBuilder(dataEdit)
          }
        }
        this.firstCall = false
      }
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.location.back()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave()
        break
    }
  }
}
