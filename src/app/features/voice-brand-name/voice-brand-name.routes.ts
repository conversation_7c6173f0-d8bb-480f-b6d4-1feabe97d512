import { Routes } from '@angular/router'
import { RESOURCE_CODE, Scopes } from '@shared'

export const routes: Routes = [
  {
    path: '',
    title: 'Quản lý nhà mạng',
    loadComponent: async () => (await import('./pages/list/voice-brand-name-list.component')).VoiceBrandNameListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  },
  {
    path: 'create',
    title: 'Thêm mới nhà mạng',
    loadComponent: async () => (await import('./pages/create/voice-brand-name-create.component')).VoiceBrandNameCreateComponent,
    data: { breadcrumb: 'Thêm mới nhà mạng'}
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa nhà mạng',
    loadComponent: async () => (await import('./pages/edit/voice-brand-name-edit.component')).VoiceBrandNameEditComponent,
    data: { breadcrumb: 'Chỉnh sửa nhà mạng'}
  }
]
