/**
 * API PATH
 */
import { environment } from '@env/environment'

export const PATH = {
  URL_DOWNLOAD_TEMPLATE_CONFIG_BUILDER: `${environment.services.pbxCms}/v1/config-builder/download-template`,
  URL_UPLOAD_CONFIG_BUILDER: `${environment.services.pbxCms}/v1/config-builder/import`,
  URL_SYNC_ALL_ORG: `${environment.services.dcmsCoreAdmin}/v1/hcm-sync/org`,
  URL_SYNC_ALL_TITLE: `${environment.services.dcmsCoreAdmin}/v1/hcm-sync/job`,
  URL_SYNC_ALL_USER: `${environment.services.dcmsCoreAdmin}/v1/hcm-sync/employee`,
}

export const FUNCTION_TYPE = {
  IMPORT_CONFIG_BUILDER: 'IMPORT_CONFIG_BUILDER',
  SYNC_ALL_ORG: 'SY<PERSON>_ALL_ORG',
  SY<PERSON>_ALL_TITLE: 'SY<PERSON>_ALL_TITLE',
  SY<PERSON>_ALL_USER: 'SYNC_ALL_USER',
  DCMS_DATA_MANAGEMENT: 'DCMS_DATA_MANAGEMENT'
}

export const STATIC_DATA_PAGE = [
  {
    'functionType': FUNCTION_TYPE.IMPORT_CONFIG_BUILDER,
    'title': 'API import config builder',
    'button': {
      'class': 'mbb-icon ic-upload_file',
      'text': 'import'
    },
    'urlAPI': {
      'downloadTemplate': PATH.URL_DOWNLOAD_TEMPLATE_CONFIG_BUILDER,
      'urlImport': PATH.URL_UPLOAD_CONFIG_BUILDER,
    }
  },
  {
    'functionType': FUNCTION_TYPE.SYNC_ALL_ORG,
    'title': 'API đồng bộ tất cả đơn vị từ HCM (Sync All Org)',
    'button': {
      'class': 'mbb-icon ic-refresh',
      'text': 'sync'
    },
    'urlAPI': {
      'urlSync': PATH.URL_SYNC_ALL_ORG,
    }
  },
  {
    'functionType': FUNCTION_TYPE.SYNC_ALL_TITLE,
    'title': 'API đồng bộ tất cả chức danh từ HCM (Sync All Title)',
    'button': {
      'class': 'mbb-icon ic-refresh',
      'text': 'sync'
    },
    'urlAPI': {
      'urlSync': PATH.URL_SYNC_ALL_TITLE,
    }
  },
  {
    'functionType': FUNCTION_TYPE.SYNC_ALL_USER,
    'title': 'API đồng bộ tất cả user từ HCM (Sync All User)',
    'button': {
      'class': 'mbb-icon ic-refresh',
      'text': 'sync'
    },
    'urlAPI': {
      'urlSync': PATH.URL_SYNC_ALL_USER,
    }
  }
]
