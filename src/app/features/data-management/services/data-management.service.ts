import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'

@Injectable({
  providedIn: 'root'
})
export class DataManagementService {
  constructor(private httpClient: HttpClientService, private appService: AppService) {}

  syncData(url, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${url}`,
      params: params
    }
    return this.httpClient.get(options);
  }

}

