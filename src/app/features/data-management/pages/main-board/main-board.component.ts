import { Component, Injector, isDevM<PERSON>, ViewChild } from '@angular/core'
import { AppPaginationComponent, ComponentAbstract, MessageSeverity, Verbs } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { NgForOf, NgIf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'
import { DataManagementService } from '@features/data-management/services/data-management.service'
import {
  <PERSON><PERSON><PERSON>, <PERSON><PERSON>ellDef,
  Mat<PERSON><PERSON>umnDef,
  <PERSON><PERSON><PERSON>er<PERSON>ell, MatHeaderCellDef,
  MatHeaderRow, MatHeaderRowDef, MatRow, MatRowDef,
  MatTable
} from '@angular/material/table'
import { FUNCTION_TYPE, STATIC_DATA_PAGE } from '@features/data-management/constants'
import { finalize, takeUntil } from 'rxjs'
import {
  AppImportExcelDialogComponent
} from '@shared/components/data-input/dialog-upload-excel/app-import-excel-dialog.component'
import { NoDataComponent } from '@shared/components/section/no-data/no-data.component'

@Component({
  selector: 'app-board-data-management',
  templateUrl: './main-board.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent,
    AppPaginationComponent,
    NoDataComponent,
    MatTable,
    MatHeaderCell,
    MatCell,
    MatColumnDef,
    MatHeaderRow,
    MatRow,
    MatHeaderCellDef,
    MatCellDef,
    MatHeaderRowDef,
    MatRowDef,
    NgIf
  ],
  styleUrls: ['./main-board.component.scss']
})
export class MainBoardComponent extends ComponentAbstract {

  displayedColumns = ['no', 'functionType', 'action']
  @ViewChild('dataTableBuilderComponent') dataTableBuilderComponent: AppTableBuilderComponent

  constructor(
    protected override injector: Injector,
    private _dataManagementService: DataManagementService,
  ) {
    super(injector)
    this.initBuilderUIConfig('management-data-list').then((r) => {
       this.elementService.checkViewChildExists('dataTableBuilderComponent', this).then((r) => {
         setTimeout(() => {
           this.getListCommonCategory()
         }, 100)
       })
    });

  }

  componentInit(): void {
  }

  onTableActionClick($event: any) {
    // console.log($event.row.functionType)
    let data = $event.row
    switch (data.functionType) {
          case FUNCTION_TYPE.IMPORT_CONFIG_BUILDER: {
            this.openDialogImport(data.urlAPI.downloadTemplate, data.urlAPI.urlImport)
            break
          }
          case FUNCTION_TYPE.SYNC_ALL_ORG:
          case FUNCTION_TYPE.SYNC_ALL_TITLE:
          case FUNCTION_TYPE.SYNC_ALL_USER:
            this.openDialogSyncAll(data)
            break
          default:
            break
        }
  }

  onButtonClick($event: any) {
    console.log($event)
  }

  getListCommonCategory() {
    try {
      this.indicator.showActivityIndicator(true);
      this.appService
        .getListCommonCategory([FUNCTION_TYPE.DCMS_DATA_MANAGEMENT])
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe((res: any) => {
          if (res) {
            const data =  res.content?.find((x) => x.code === FUNCTION_TYPE.DCMS_DATA_MANAGEMENT)?.commons
            if(data) {
              const listKeyActive = data.map(item => item.code);
              let dataTable = STATIC_DATA_PAGE.filter(item => listKeyActive.includes(item.functionType))?.map((item : any, index) => {
                return { ...item, no: index + 1 }
              })
              this.dataTableBuilderComponent._appTableBodyComponent.loadDataStaticTable(false, dataTable)
            }
          }
        })
    } catch (error) {
      this.indicator.hideActivityIndicator(true)
    }
  }


  openDialogSyncAll(data) {
    this.dialogService.confirm(
      {
        title: 'Đồng bộ dữ liệu',
        innerHTML: `Thao tác này sẽ xử lý call ${data.title}`,
        textButtonLeft: 'Hủy',
        textButtonRight: 'Đồng ý'
      },
      (result) => {
        if (result) {
          //call api sync
          this.indicator.showActivityIndicator(true);
          this._dataManagementService.syncData(data.urlAPI.urlSync).subscribe(
            (res) => {
              this.indicator.hideActivityIndicator(true);
              this.toastr.showToastr(
                '',
                'Đồng bộ thành công',
                MessageSeverity.success
              )
            },
            (error) => {
              this.indicator.hideActivityIndicator(true);
            }
          )
        } else {
          //close
        }
      }
    )
  }

  openDialogImport(urlDownload, urlImport) {
    this.dialogService.componentDialog(
      AppImportExcelDialogComponent,
      {
        width: '40%',
        maxWidth: '500px',
        height: '55%',
        data: {
          urlDownload,
          urlImport
        }
      },
      (data) => {
      }
    )
  }
}
