import { Routes } from '@angular/router'
export const routes: Routes = [
  {
    path: 'audio',
    title: '<PERSON>h sách bản ghi audio',
    loadComponent: async () => (await import('./pages/list/audio-list.component')).AudioListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  },
  {
    path: 'audio/create',
    title: 'Tạo bản ghi audio',
    loadComponent: async () => (await import('@features/audio-management/pages/create/audio-create.component')).AudioCreateComponent,
    // canActivate: [RoleGuard],
  },
  {
    path: 'audio/edit/:id',
    title: 'Sửa bản ghi audio',
    loadComponent: async () => (await import('@features/audio-management/pages/edit/audio-edit.component')).AudioEditComponent,
    // canActivate: [RoleGuard],
  }
]
