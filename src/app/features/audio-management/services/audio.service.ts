import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'
import { PATH_API_AUDIO } from '../constants'

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * List
   * @param body
   */
  search(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API_AUDIO.COMMON,
      body
    }
    return this.httpClient.post(options)
  }

  /**
   * tạo
   * @param body
   */
  create(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API_AUDIO.COMMON,
      body
    }
    return this.httpClient.post(options)
  }

  /**
   * Import file audio
   * @param file audio
   */
  importAudio(formData: FormData) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API_AUDIO.UPLOAD}`
    }
    return this.httpClient.uploadFormData(options, formData)
  }

  /**
   * chi tiết
   * @param code mã audio
   */
  getDetail(code: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API_AUDIO.COMMON}/${code}`
    }
    return this.httpClient.get(options)
  }

  /**
   * update
   * @param body
   */
  update(body: any, id: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API_AUDIO.COMMON}/${id}`,
      body
    }
    return this.httpClient.put(options)
  }
}
