import { Component, Injector, ViewChild } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, SharedSMModule, Status } from '@shared'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { environment } from '@env/environment'
import { DragDropUploadFileAudioComponent } from '@shared/components/data-input/drag-drop-upload-file-audio/drag-drop-upload-file-audio.component'
import { finalize, firstValueFrom, takeUntil } from 'rxjs'
import { debounceTime } from 'rxjs/operators'
import { ROUTES_NAME_AUDIO } from '../../../../app.routes'
import { blobToJson } from '../../../../shared/utils'
import { AudioService } from '@features/audio-management/services/audio.service'

@Component({
  selector: 'app-audio-create',
  templateUrl: './audio-create.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon, SharedSMModule, DragDropUploadFileAudioComponent],
  styleUrls: ['./audio-create.component.scss']
})
export class AudioCreateComponent extends ComponentAbstract {
  @ViewChild('uploadFileComponent') uploadFileComponent: DragDropUploadFileAudioComponent
  urlImport = `${environment.services.pbxCms}/audio/upload`
  id = 'management-audio-upload-create'

  constructor(
    protected override injector: Injector,
    private _service: AudioService
  ) {
    super(injector)
    this.initBuilderUIConfig(this.id, true).then((r) => {
      this.componentInit()
    })
  }

  componentInit(): void {
    // this.form = this.itemControl.toFormGroup([])
    this._page.form.valueChanges.pipe(debounceTime(300)).subscribe((value) => {
      console.log('typing form', value)
    })
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
  }

  async onSave() {
    const newData = { ...this._page.form.getRawValue() }
    console.log('formDat', this._page.form.getRawValue())
    this._page.validateForm()
    if (!this.uploadFileComponent.fileUpload?.length && this._page.form.valid) {
      this.showDialogErrorI18n('', 'validations.required-upload-file-audio')
    } else if (this._page.form.valid) {
      const result = await firstValueFrom(this.uploadFileComponent.uploadFileAudio(newData))
      blobToJson(result.body)
        .then((res) => {
          if (res.code === Status.SUCCESS) {
            const req = {
              ...this._page.form.getRawValue(),
              format: res.data.body.format,
              duration: res.data.body.duration,
              size: res.data.body.size,
              path: res.data.body.path
            }
            this.onCreateData(req)
          } else {
            this.showDialogErrorI18n(res?.error?.message, 'Thêm mới audio <br/>thất bại')
          }
        })
        .catch((error) => {
          console.error(error)
        })
    }
  }

  onCreateData(body: any) {
    this._service
      .create(body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this._page.form.reset()
            this.goTo(ROUTES_NAME_AUDIO.LIST)
            this.showDialogSuccessI18n('', 'Thêm mới audio <br/>thành công')
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Thêm mới audio <br/>thất bại')
        }
      )
  }

  onCancel() {
    this.dialogService.confirm(
      {
        title: this.translateService.instant('dialog.confirm'),
        message: this.translateService.instant('dialog.confirm-cancel-import')
      },
      (result) => {
        if (result) {
          this.goTo(ROUTES_NAME_AUDIO.LIST)
        }
      }
    )
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onCancel()
        break
      case BUTTON_SAVE.typeBtn:
        this.onSave().then((r) => {})
        break
    }
  }
}
