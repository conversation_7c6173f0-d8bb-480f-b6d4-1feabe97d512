import { Component, Injector, Input, ViewChild } from '@angular/core'
import { BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, Status } from '@shared'
import { finalize, firstValueFrom, takeUntil } from 'rxjs'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { MatIcon } from '@angular/material/icon'
import { blobToJson } from '../../../../shared/utils'
import { AudioService } from '@features/audio-management/services/audio.service'
import { environment } from '@env/environment'
import { DragDropUploadFileAudioComponent } from '@shared/components/data-input/drag-drop-upload-file-audio/drag-drop-upload-file-audio.component'
import { ROUTES_NAME_AUDIO } from '../../../../app.routes'

@Component({
  selector: 'app-audio-edit',
  templateUrl: './audio-edit.component.html',
  standalone: true,
  imports: [PageBuilderComponent, FooterComponent, MatIcon, DragDropUploadFileAudioComponent],
  styleUrls: ['./audio-edit.component.scss']
})
export class AudioEditComponent extends ComponentAbstract {
  @ViewChild('uploadFileComponent') uploadFileComponent: DragDropUploadFileAudioComponent
  urlImport = `${environment.services.pbxCms}/audio/upload`
  members = []
  @Input() public idEdit: string
  id = 'audio-management-edit'
  dataEdit: {
    name: any
    category: any
    duration: any
    format: any
    id: any
    path: any
    size: any
    status: any
    createAt: any
    createBy: any
    updateAt: any
    updateBy: any
    folderType: any
  }

  constructor(
    protected override injector: Injector,
    private _service: AudioService
  ) {
    super(injector)
    this.idEdit = this.route.snapshot.paramMap.get('id')
    this.initBuilderUIConfig(this.id, true).then((r) => {
      if (this.idEdit) {
        this.form = this.itemControl.toFormGroup([])
        setTimeout(() => {
          this.getDetailById()
        }, 500)
      }
    })
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_UPDATE)
  }

  getDetailById() {
    this.indicator.showActivityIndicator(true)
    this._service
      .getDetail(this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          console.log('response', res)

          if (res?.code === Status.SUCCESS && res?.data) {
            this.dataEdit = res.data
            const data = {
              id: {
                value: this.dataEdit?.id
              },
              name: {
                value: this.dataEdit?.name
              },
              category: {
                value: this.dataEdit.category
              },
              folderType: {
                value: this.dataEdit.folderType
              }
            }
            console.log('this.dataEdit', this.dataEdit)
            this.patchValuesFormBuilder(data)
          } else {
            this.goTo404()
          }
        },
        () => {}
      )
  }

  async onSave() {
    this._page.validateForm()
    const updateData = { ...this._page.form.getRawValue() }

    if (!this.uploadFileComponent.fileUpload?.length && this._page.form.valid) {
      console.log('dataEdit before', this.dataEdit)
      this.dataEdit.name = updateData.name
      this.dataEdit.folderType = updateData.folderType
      this.dataEdit.category = updateData.category
      this.onUpdateData(this.dataEdit)
    } else if (this._page.form.valid) {
      const result: any = await firstValueFrom(this.uploadFileComponent.uploadFileAudio(updateData))
      blobToJson(result.body)
        .then((res) => {
          if (res.code === Status.SUCCESS) {
            this.dataEdit.name = updateData.name
            this.dataEdit.folderType = updateData.folderType
            this.dataEdit.category = updateData.category
            this.dataEdit.format = res.data.body.format
            this.dataEdit.duration = res.data.body.duration
            this.dataEdit.size = res.data.body.size
            this.dataEdit.path = res.data.body.path
            console.log('this.dataEdit co file import', this.dataEdit)
            this.onUpdateData(this.dataEdit)
          } else {
            this.showDialogErrorI18n(res?.error?.message, 'update file audio <br/>thất bại')
          }
        })
        .catch((error) => {
          console.error(error)
        })
    }
  }

  onUpdateData(body: any) {
    this._service
      .update(body, this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.goTo(ROUTES_NAME_AUDIO.LIST)
            this.showDialogSuccessI18n('', 'Cập nhật audio <br/>thành công')
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Cập nhật audio <br/>thất bại')
        }
      )
  }

  handlerButtonTableClick($event) {
    if ($event.event.type === 'DELETE') {
      const members = this.members.filter((x) => x.employeeId !== $event.event.row.employeeId)
      const codes = members.map((x) => x.employeId)
      const dataEdit = {
        subscriberIds: {
          value: codes,
          options: members
        }
      }
      this.patchValuesFormBuilder(dataEdit)
      if (!codes?.length) {
        this._page.table.loadDataStaticTable(false, [])
      }
    }
  }

  handlerFormData($event: any) {
    if ($event.event.item.key === 'subscriberIds') {
      this.members = $event.event.data.map((x, index) => {
        return { ...x.data, no: index + 1 }
      })

      this._page.table.loadDataStaticTable(false, this.members)
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.location.back()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave().then((r) => {})
        break
    }
  }

  fileChanged($event) {
    return $event
  }
}
