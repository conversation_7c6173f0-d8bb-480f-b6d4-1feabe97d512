import { Component, Injector, ViewChild } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { NgForOf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'

@Component({
  selector: 'app-audio-list',
  templateUrl: './audio-list.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent
  ],
  styleUrls: ['./audio-list.component.scss']
})
export class AudioListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent

  constructor(protected override injector: Injector) {
    super(injector)
    this.initBuilderUIConfig('management-audio-list').then((r) => {})
  }

  componentInit(): void {}
}
