import { Routes } from '@angular/router'
import { RESOURCE_CODE, Scopes } from '@shared'

export const routes: Routes = [
  {
    path: '',
    title: 'Nội dung chốt sale',
    loadComponent: async () => (await import('./pages/list/sale-close-content-list.component')).SaleCloseContentListComponent,
    data: {
      breadcrumb: '',
      showBreadcrumb: false
    }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa nội dung chốt sale',
    loadComponent: async () => (await import('./pages/edit/sale-close-content-edit.component')).SaleCloseContentsEditComponent,
    data: {
      breadcrumb: 'Chỉnh sửa nội dung chốt sale',
    }
  },
  {
    path: 'create',
    title: 'Thêm mới nội dung chốt sale',
    loadComponent: async () => (await import('./pages/create/sale-close-content-create.component')).SaleCloseContentsCreateComponent,
    data: {
      breadcrumb: 'Thêm mới nội dung chốt sale',

    }
  }
]
