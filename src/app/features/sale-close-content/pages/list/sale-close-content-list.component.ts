import { Component, ElementRef, Injector, isDevMode, ViewContainerRef } from '@angular/core'
import { ComponentAbstract, RESOURCE_CODE } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { MatExpansionPanel } from '@angular/material/expansion'
import { MatIcon } from '@angular/material/icon'
import { NgForOf } from '@angular/common'
import { ZoneComponent } from '@mb/ngx-ui-builder'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { DateTimeControlComponent } from '@shared/components/data-input/date-time-control/date-time-control.component'

@Component({
  selector: 'app-sale-close-content-list',
  templateUrl: './sale-close-content-list.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    AppTableBuilderComponent,
    CheckboxControlComponent,
    DateControlComponent,
    DateTimeControlComponent,
    FlexModule,
    MatExpansionPanel,
    MatIcon,
    NgForOf,
    ZoneComponent
  ],
  styleUrls: ['./sale-close-content-list.component.scss']
})
export class SaleCloseContentListComponent extends ComponentAbstract {
  prefix = 'management-sale-close-contents'
  constructor(protected override injector: Injector) {
    super(injector)
    this.initBuilderUIConfig(this.prefix, true).then((r) => {})
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    console.log($event)
  }

  onButtonClick($event: any) {
    console.log($event)
  }
}
