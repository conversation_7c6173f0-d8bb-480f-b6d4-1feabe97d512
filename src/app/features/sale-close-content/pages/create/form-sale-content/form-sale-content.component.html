<form [formGroup]="form">
  <table
    #table
    cdkDropList
    (cdkDropListDropped)="drop($event)"
    cdkDropListData="dataSource"
    [cdkDropListDisabled]="dragDisabled"
    formArrayName="answers"
    class="app-table table-info w-full"
    element.htmlForm
    mat-table
    [dataSource]="dataTable"
    [@.disabled]="true">
    <ng-container matColumnDef="no">
      <th mat-header-cell *matHeaderCellDef>STT</th>
      <td mat-cell *matCellDef="let element" style="width: 100px" (mousedown)="onDisabled(false)">
        <div class="d-flex flex-center-row">
          <mat-icon matTooltipPosition='above' matTooltip="Kéo thả để thay đổi vị trí" class="mbb-icon ic-drag dragCursor drag-icon"></mat-icon>
          <div>{{ element.no }}</div>
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="answer">
      <th mat-header-cell *matHeaderCellDef>
        <strong>Câu trả lời</strong>
      </th>
      <td mat-cell *matCellDef="let element" style="overflow: visible" class="pd-0">
        <div class="d-flex align-items-center h-100">
          <app-textarea-control
            (mousedown)="onDisabled(true)"
            formGroupClass="mrb-0"
            [item]="getListControls(arrAnswerControls[element.index], $answer.key)"
            [form]="getAnswers(form).controls[element.index]"
            class="flex-100"></app-textarea-control>
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="decision">
      <th mat-header-cell *matHeaderCellDef style="width: 100px">Mang tính quyết định?</th>
      <td mat-cell *matCellDef="let element" class="pd-0 d-flex justify-content-center align-items-center" (mousedown)="onDisabled(false)">
        <!--        <app-checkbox-control [item]="getSystemControl(arraySystemControls[element.index], $cbDecision.key)" [form]="getArraySystemSelectedControl(form).controls[element.index]"></app-checkbox-control>-->
        <mat-checkbox [formControl]="getAnswers(form).controls[element.index].get($cbDecision.key)"></mat-checkbox>
      </td>
    </ng-container>
    <ng-container matColumnDef="action">
      <th mat-header-cell *matHeaderCellDef="let element" style="width: 100px">Hành động</th>
      <td mat-cell *matCellDef="let element" class="text-center" style="width: 100px" (mousedown)="onDisabled(false)">
        <button mat-icon-button [hidden]="element.index === 0 && dataSource?.data?.length === 1"
                (click)="onDelete(element)">
          <mat-icon class="mbb-icon ic-delete" matTooltipPosition='above' matTooltip="Xóa"></mat-icon>
        </button>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"
        cdkDrag
        [cdkDragData]="row">
      <table *cdkDragPreview class="app-table table-info drag-preview">
        <tbody>
        <tr>
          <td style="width: 100px">
            <div class="d-flex flex-center-row">
              <mat-icon matTooltipPosition='above' matTooltip="Kéo thả để thay đổi vị trí" class="mbb-icon ic-drag dragCursor drag-icon"></mat-icon>
              <div>{{ row.no }}</div>
            </div>
          </td>
          <td style="overflow: visible" class="pd-0">
            <app-textarea-control (mousedown)="dragDisabled = true;" formGroupClass="mrb-0" [item]="getListControls(arrAnswerControls[row.index], $answer.key)" [form]="getAnswers(form).controls[row.index]" class="flex-100"></app-textarea-control>
          </td>
          <td class="pd-0 d-flex justify-content-center align-items-center" style="width: 100px">
            <mat-checkbox [formControl]="getAnswers(form).controls[row.index].get('decision')"></mat-checkbox>
          </td>
        </tr>
        </tbody>
      </table>
    </tr>
  </table>
  <button type="button" class="btn btn-white btn-border fc-dark-blue mrt-4 pds-4" (click)="onAddItem()">
    <mat-icon class="mbb-icon ic-plus fc-dark-blue"></mat-icon>
    <span class="fc-dark-blue">Thêm mới</span>
  </button>
</form>
