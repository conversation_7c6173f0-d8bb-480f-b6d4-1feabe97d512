.app-table tr td {
  height: 99.5px;
}
.dragCursor {
  cursor: grabbing!important;
}

.cursor {
  cursor: inherit;
}

.cdk-drag-preview {
  background: #fff;
  border: solid 2px red;
}

.drag-preview {
  width: 90%;
  height: 99.5px;
  max-height: 99.5px;
  background: #fff;
  border-radius: 4px;
  border: solid 2px #141ED2;
  tr td {
    height: 99.5px;
  }
  ::ng-deep textarea {
    height: 40px!important;
  }
}

.cdk-drag-placeholder {
  background: #F6F8FF;
  border-radius: 4px;
  border: solid 2px #141ED2;
}

.cdk-drag-animating {
  transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .mat-row:not(.cdk-drag-placeholder) {
  transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
}
.drag-icon {
  color:#74A5DB;
  font-size: 18px;
}
