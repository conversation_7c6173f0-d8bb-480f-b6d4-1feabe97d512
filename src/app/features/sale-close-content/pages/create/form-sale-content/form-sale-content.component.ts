import { Component, EventEmitter, Injector, Output, ViewChild } from '@angular/core'
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { MatTable, MatTableModule } from '@angular/material/table'
import { ComponentAbstract } from '@shared'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { MatIcon } from '@angular/material/icon'
import { cbDecision, txtAnswer } from '@features/sale-close-content/models/form-sale-content.model'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { debounceTime } from 'rxjs/operators'
import { MatCheckbox } from '@angular/material/checkbox'
import { NgIf } from '@angular/common'
import { CdkDrag, CdkDragDrop, CdkDragPreview, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop'
import { MatTooltip } from '@angular/material/tooltip'

@Component({
  selector: 'app-form-sale-content',
  templateUrl: './form-sale-content.component.html',
  styleUrls: ['./form-sale-content.component.scss'],
  imports: [
    TextareaControlComponent,
    MatIcon,
    ReactiveFormsModule,
    MatTableModule,
    CheckboxControlComponent,
    MatCheckbox,
    NgIf,
    CdkDrag,
    CdkDropList,
    CdkDragPreview,
    MatTooltip
  ],
  standalone: true
})
export class FormSaleContentComponent extends ComponentAbstract {
  @Output() onChanged = new EventEmitter<any>()
  @ViewChild('table', { static: true }) table: MatTable<any>
  $answer = txtAnswer()
  $cbDecision = cbDecision()

  dataTable = []
  displayedColumns: string[] = ['no', 'answer', 'decision', 'action']
  arrAnswerControls = []

  constructor(protected override injector: Injector) {
    super(injector)
  }

  componentInit() {
    this.form = this.itemControl.toFormGroup([])
    this.itemControl.addFormArrayGroup('answers', this.form)
    this.form.valueChanges.pipe(debounceTime(300)).subscribe((value) => {
      this.emitData()
    })
  }

  dragDisabled = true

  drop(event: CdkDragDrop<any>) {
    // Return the drag container to disabled.
    this.dragDisabled = true

    const previousIndex = this.dataTable.findIndex((d) => d === event.item.data)

    moveItemInArray(this.dataTable, previousIndex, event.currentIndex)
    this.emitData()
    this.table.renderRows()
  }

  getAnswers(formGroup: FormGroup): any {
    let control = <FormArray>formGroup.controls.answers

    return control
  }

  getListControls(arr: any, controlKey: string) {
    let control = arr?.find((x) => x.key == controlKey)
    return control
  }

  onAddItem(answer = '', decision = false) {
    let control = this.getAnswers(this.form)
    this.$answer.value = answer
    const controls = [this.$answer, this.$cbDecision]
    const formGroup = this.itemControl.toFormGroup(controls)
    formGroup.get(this.$cbDecision.key).patchValue(decision)
    control.push(formGroup)
    this.arrAnswerControls.push(controls)

    let lastIndex = this.dataTable.length
    this.dataTable = [
      ...this.dataTable,
      {
        no: lastIndex + 1,
        answer: '',
        decision: '',
        index: lastIndex
      }
    ]
  }

  get answersFormArray() {
    return this.form.get('answers') as FormArray
  }

  onDelete(element) {
    this.onRemoveControl(element.index)
    this.dataTable = this.dataTable
      .filter((x) => x.no !== element.no)
      .map((x, index) => {
        return { ...x, index: index }
      })
  }

  onRemoveControl(i) {
    let control = this.getAnswers(this.form)
    control.removeAt(i)
    this.arrAnswerControls.splice(i, 1)
  }

  /**
   * call view ref
   * DO NOT REMOVE
   */
  validateForm() {
    this.validateAllFields(this.form)
  }

  onDisabled(b: boolean) {
    this.dragDisabled = b
    console.log(b)
  }

  emitData() {
    const answers = this.dataTable.map((item) => {
      const fc = this.answersFormArray.controls[item.index].value
      return {
        answerContent: fc.answerContent,
        isDecisive: fc.decision
      }
    })
    console.dir(this.dataTable)
    console.dir(answers)
    console.dir(this.answersFormArray.value)
    this.onChanged.emit(answers)
  }
}
