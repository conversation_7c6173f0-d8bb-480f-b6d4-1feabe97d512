import { Cdk<PERSON>rag, CdkDragPlaceholder, CdkDropList, CdkDropListGroup } from '@angular/cdk/drag-drop'
import { NgForOf, NgIf } from '@angular/common'
import { Component, Injector, Input, ViewChild } from '@angular/core'
import { FlexModule } from '@angular/flex-layout'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIcon } from '@angular/material/icon'
import { FormPreviewComponent } from '@features/form/shared/form-preview/form-preview.component'
import { TemplateNameDirective, ZoneComponent } from '@mb/ngx-ui-builder'
import { BUTTON_SAVE, BUTTON_TYPE_CANCEL, ComponentAbstract, EVENT_FORM_CONTROL, Status } from '@shared'
import { AppTableTreeComponent, FormControlPreviewRendererComponent } from '@shared/components/data-display'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { finalize, takeUntil } from 'rxjs'

import { SaleCloseContentService } from '@features/sale-close-content/services/sale-close-content.service'
import { ROUTES_NAME_SALE_CLOSE_CONTENT } from 'src/app/app.routes'
import { FormSaleContentComponent } from '@features/sale-close-content/pages/create/form-sale-content/form-sale-content.component'
import { SharedService } from '@features/sale-close-content/services/shared-service.service'
import { CampaignBusinessLogicService } from '@features/campaign-voice-blaster/services/campaign-business-logic.service'
import { TypeSelectedEnum } from '@features/campaign-voice-blaster/pages/assign/assign.enum'
import { AppPanelComponent } from '@shared/components/element/app-panel/app-panel.component'
import { AppTableAgentComponent } from '@features/campaign-voice-blaster/pages/assign/app-table-agent/app-table-agent.component'

@Component({
  selector: 'sale-close-content-create',
  templateUrl: './sale-close-content-create.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    FooterComponent,
    MatIcon,
    CheckboxControlComponent,
    FlexModule,
    NgForOf,
    NgIf,
    ReactiveFormsModule,
    SelectControlComponent,
    TextControlComponent,
    FormControlRendererComponent,
    FormsModule,
    TemplateNameDirective,
    ZoneComponent,
    AppTableTreeComponent,
    FormPreviewComponent,
    CdkDrag,
    CdkDropList,
    FormControlPreviewRendererComponent,
    CdkDropListGroup,
    CdkDragPlaceholder,
    FormSaleContentComponent,
    AppPanelComponent,
    AppTableAgentComponent
  ],
  styleUrls: ['./sale-close-content-create.component.scss']
})
export class SaleCloseContentsCreateComponent extends ComponentAbstract {
  @ViewChild('saleContent') saleContent: FormSaleContentComponent
  prefix = 'management-sale-close-content-create'
  id: string
  answers = []
  @Input()
  isEmbed = false
  errorCreate = ''
  constructor(
    protected override injector: Injector,
    private _service: SaleCloseContentService,
    private _campaignBusinessLogicService: CampaignBusinessLogicService,
    private sharedService: SharedService
  ) {
    super(injector)
    this.initBuilderUIConfig(this.prefix, true).then((r) => {
      this.saleContent.onAddItem()
    })
  }
  override ngAfterViewInit() {
    super.ngAfterViewInit()
  }

  override componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_SAVE)
  }

  onSave() {
    this.errorCreate = this.sharedService.onSave(
      this.form,
      this.saleContent,
      this.answers,
      this.onCreateData.bind(this),
      this.validateAllFields.bind(this)
    )
  }

  onCreateData(body: any) {
    this.indicator.showActivityIndicator()
    this._service
      .create(body)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.form.reset()
            this.saleContent.form.reset()
            if (this.isEmbed === true) {
              this.showDialogSuccessI18n('', 'Thêm mới nội dung chốt sale<br/> thành công')
              this._campaignBusinessLogicService.saleContentCreate.next(res?.data)
              this.onBackListEmbed()
            } else {
              this.goTo(ROUTES_NAME_SALE_CLOSE_CONTENT.LIST)
              this.showDialogSuccessI18n('', 'Thêm mới nội dung chốt sale<br/> thành công')
            }
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Thêm mới nội dung chốt sale<br/>  ')
        }
      )
  }
  formChanged($event: any) {
    console.log('formChanged', $event)
    if ($event?.data == EVENT_FORM_CONTROL.CREATE_CONTROL) {
      this.form.setControl($event?.item.key, $event.form.get($event?.item.key))
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onBack()
        break
      case BUTTON_SAVE.typeBtn:
        this.onSave()
        break
    }
  }

  clickMessageError($event: any) {}

  onChangeContent($event: any) {
    this.answers = $event
  }
  onBack() {
    if (this.isEmbed) {
      this.onBackListEmbed()
    } else {
      this.location.back()
    }
  }

  onBackListEmbed() {
    this._campaignBusinessLogicService.buttonClick.next({ type: 'sale-content', value: 'list' })
  }

  protected readonly TypeSelectedEnum = TypeSelectedEnum
}
