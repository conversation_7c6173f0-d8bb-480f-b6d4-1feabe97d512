<div *ngIf='isEmbed' class="d-flex flex-center-row header-page">
  <button type='button' class='btn btn-back-page' (click)="onBackListEmbed()">
    <mat-icon class='mbb-icon ic-arrow-right' style="transform: rotate(180deg);"></mat-icon>
  </button>
  <h2 class='title-route'>Thêm mới nội dung chốt sale</h2>
</div>
<div class="ui-builder">
  <app-panel heading="Thông tin chung" [showExpandBtn]="false">
    <div content>
      <form class="flex-column">
        <uib-zone [prefix]="prefix" id="form-info">
          <ng-template uib-template="formControl" let-config>
            <app-form-control-renderer
              [config]="config"
              (clickMessageError)="clickMessageError($event)"
              (formChanged)="formChanged($event)"></app-form-control-renderer>
          </ng-template>
        </uib-zone>
      </form>
    </div>
  </app-panel>
  <app-panel panelClass="mrt-4" heading="Danh sách kết quả chốt sale" [showExpandBtn]="false">
    <ng-container heading>
      <div class="fc-error" *ngIf="errorCreate">{{ errorCreate }}</div>
    </ng-container>
    <div content>
      <app-form-sale-content #saleContent (onChanged)="onChangeContent($event)"></app-form-sale-content>
    </div>
  </app-panel>
</div>
<app-form-footer [isFixedBottom]='!isEmbed' [listButton]="listButton" (eventClick)="onClickBtn($event)"></app-form-footer>
