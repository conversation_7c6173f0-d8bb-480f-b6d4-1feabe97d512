import { Cdk<PERSON>rag, CdkDragPlaceholder, CdkDropList, CdkDropListGroup } from '@angular/cdk/drag-drop'
import { NgForOf, NgIf } from '@angular/common'
import { Component, Injector, Input, ViewChild } from '@angular/core'
import { FlexModule } from '@angular/flex-layout'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIcon } from '@angular/material/icon'
import { FormPreviewComponent } from '@features/form/shared/form-preview/form-preview.component'
import { SaleCloseContentService } from '@features/sale-close-content/services/sale-close-content.service'
import { TemplateNameDirective, ZoneComponent } from '@mb/ngx-ui-builder'
import { BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, EVENT_FORM_CONTROL, getSelectOptions, Status } from '@shared'
import { AppTableTreeComponent, FormControlPreviewRendererComponent } from '@shared/components/data-display'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { PageBuilderComponent } from '@shared/components/page-builder/page-builder.component'
import { FooterComponent } from '@shared/components/section/footer/footer.component'
import { finalize, takeUntil } from 'rxjs'
import { ROUTES_NAME_SALE_CLOSE_CONTENT } from 'src/app/app.routes'
import { FormSaleContentComponent } from '@features/sale-close-content/pages/create/form-sale-content/form-sale-content.component'
import { Answer } from '@features/sale-close-content/models'
import { SharedService } from '@features/sale-close-content/services/shared-service.service'
import { AppPanelComponent } from '@shared/components/element/app-panel/app-panel.component'

@Component({
  selector: 'app-sale-close-content-edit',
  templateUrl: './sale-close-content-edit.component.html',
  standalone: true,
  imports: [
    PageBuilderComponent,
    FooterComponent,
    MatIcon,
    CheckboxControlComponent,
    FlexModule,
    NgForOf,
    NgIf,
    ReactiveFormsModule,
    SelectControlComponent,
    TextControlComponent,
    FormControlRendererComponent,
    FormsModule,
    TemplateNameDirective,
    ZoneComponent,
    AppTableTreeComponent,
    FormPreviewComponent,
    CdkDrag,
    CdkDropList,
    FormControlPreviewRendererComponent,
    CdkDropListGroup,
    CdkDragPlaceholder,
    FormSaleContentComponent,
    AppPanelComponent
  ],
  styleUrls: ['./sale-close-content-edit.component.scss']
})
export class SaleCloseContentsEditComponent extends ComponentAbstract {
  @ViewChild('saleContent') saleContent: FormSaleContentComponent
  @Input() public idEdit: string
  prefix = 'management-sale-close-content-edit'
  answers = []
  errorCreate = ''
  constructor(
    protected override injector: Injector,
    private _service: SaleCloseContentService,
    private sharedService: SharedService
  ) {
    super(injector)
    this.idEdit = this.route.snapshot.paramMap.get('id')
    this.initBuilderUIConfig(this.prefix, true).then((r) => {
      if (this.idEdit) {
        this.form = this.itemControl.toFormGroup([])
        setTimeout(() => {
          this.getDetailById()
        }, 500)
      }
    })
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_UPDATE)
  }

  getDetailById() {
    this.indicator.showActivityIndicator(true)
    this._service
      .getDetail(this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS && res?.data) {
            const { data } = res
            const answerType = getSelectOptions(data?.answerType, '1', '2')

            const dataEdit = {
              questionContent: {
                value: data?.questionContent
              },
              answerType: {
                value: data?.answerType?.toString(),
                options: answerType
              },
              isDecisive: {
                value: data?.isDecisive ? '1' : '0'
              },
              status: {
                value: data?.status
              }
            }
            this.patchValuesFormBuilder(dataEdit)

            data?.answers.forEach((a) => {
              this.saleContent.onAddItem(a.answerContent, a.isDecisive)
            })
          } else {
            this.goTo404()
          }
        },
        () => {}
      )
  }

  onSave() {
    this.errorCreate = this.sharedService.onSave(
      this.form,
      this.saleContent,
      this.answers,
      this.onUpdateData.bind(this),
      this.validateAllFields.bind(this)
    )
  }

  onUpdateData(body: any) {
    this.indicator.showActivityIndicator()
    this._service
      .update(body, this.idEdit)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe(
        (res: any) => {
          if (res?.code === Status.SUCCESS) {
            this.goTo(ROUTES_NAME_SALE_CLOSE_CONTENT.LIST)
            this.showDialogSuccessI18n('', 'Chỉnh sửa nội dung chốt sale <br/>thành công')
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, 'Chỉnh sửa nội dung chốt sale <br/>thất bại')
        }
      )
  }

  onBuilderChanged($event: any) {
    if ($event.type == 'formData') {
    }
  }
  formChanged($event: any) {
    if ($event?.data == EVENT_FORM_CONTROL.CREATE_CONTROL) {
      this.form.setControl($event?.item.key, $event.form.get($event?.item.key))
    }
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.location.back()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave()
        break
    }
  }

  clickMessageError($event: any) {}

  onChangeContent($event: any) {
    this.answers = $event
  }

  validateAnswers(answers: Answer[]): boolean {
    return answers.some((answer) => answer.isDecisive === true)
  }
}
