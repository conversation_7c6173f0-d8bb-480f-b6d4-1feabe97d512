<div class="ui-builder">
  <app-panel heading="Thông tin chung" [showExpandBtn]="false">
    <div content>
      <form class="flex-column">
        <uib-zone [prefix]="prefix" id="form-info">
          <ng-template uib-template="formControl" let-config>
            <app-form-control-renderer
              [config]="config"
              (clickMessageError)="clickMessageError($event)"
              (formChanged)="formChanged($event)"></app-form-control-renderer>
          </ng-template>
        </uib-zone>
      </form>
    </div>
  </app-panel>
  <app-panel panelClass="mrt-4" heading="Danh sách kết quả chốt sale" [showExpandBtn]="false">
    <ng-container heading>
      <div class="fc-error" *ngIf="errorCreate">{{ errorCreate }}</div>
    </ng-container>
    <div content>
      <app-form-sale-content #saleContent (onChanged)="onChangeContent($event)"></app-form-sale-content>
    </div>
  </app-panel>
<!--  <div class="panel mrt-4">-->
<!--    <div class="panel-heading bdt d-flex flex-center-row justify-content-between">-->
<!--      <div>-->
<!--        <label class="form-label">Danh sách kết quả chốt sale</label>-->
<!--        <div class="fc-error" *ngIf="errorCreate">{{ errorCreate }}</div>-->
<!--      </div>-->
<!--    </div>-->
<!--    <div class="panel-body pd-4">-->
<!--      <app-form-sale-content #saleContent (onChanged)="onChangeContent($event)"></app-form-sale-content>-->
<!--      &lt;!&ndash;      <uib-zone id="fields">&ndash;&gt;-->
<!--      &lt;!&ndash;        <ng-container *uib-template="'table'; let config">&ndash;&gt;-->
<!--      &lt;!&ndash;           <app-table-tree #appTableComponent [configComponent]="config" (onTableActionClick)="onTableActionHandler($event)"></app-table-tree>&ndash;&gt;-->
<!--      &lt;!&ndash;        </ng-container>&ndash;&gt;-->
<!--      &lt;!&ndash;      </uib-zone>&ndash;&gt;-->
<!--    </div>-->
<!--  </div>-->
</div>
<app-form-footer [listButton]="listButton" (eventClick)="onClickBtn($event)"></app-form-footer>
