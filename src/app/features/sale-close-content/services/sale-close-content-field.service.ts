import { Injectable } from '@angular/core'
import { BehaviorSubject } from 'rxjs'
import { FieldModel } from '@features/sale-close-content/models/field.model'
// import { FieldDataTypeEnum, FieldDisplayTypeEnum } from '@features/sale-close-content/constants'

@Injectable({
  providedIn: 'root'
})
export class SaleCloseContentFieldService {
  private SaleCloseContentFieldsSubject = new BehaviorSubject<FieldModel[]>([])
  SaleCloseContentFields$ = this.SaleCloseContentFieldsSubject.asObservable()

  // Phương thức để cập nhật SaleCloseContentFields
  updateSaleCloseContentFields(fields: FieldModel[]) {
    this.SaleCloseContentFieldsSubject.next(fields)
  }

  // Phương thức để thêm một field mới
  addSaleCloseContentField(field: FieldModel) {
    const currentFields = this.SaleCloseContentFieldsSubject.getValue()
    this.SaleCloseContentFieldsSubject.next([...currentFields, field])
  }

  // Phương thức để xóa một field
  removeSaleCloseContentField(index: number) {
    const currentFields = this.SaleCloseContentFieldsSubject.getValue()
    currentFields.splice(index, 1)
    this.SaleCloseContentFieldsSubject.next([...currentFields])
  }

  /**
   *
   * @param fields
   */
  excludeStaticFields(fields: FieldModel[]) {
    return fields.filter((field) => !field.fieldStatic)
  }

//   getFieldDisplayOptions(fieldType: FieldDataTypeEnum): { key: FieldDisplayTypeEnum; value: string }[] {
//     const optionsMap: Record<FieldDataTypeEnum, { key: FieldDisplayTypeEnum; value: string }[]> = {
//       [FieldDataTypeEnum.TEXT]: [
//         { key: FieldDisplayTypeEnum.TEXT_BOX, value: 'Textbox' },
//         { key: FieldDisplayTypeEnum.TEXTAREA, value: 'Textarea' }
//       ],
//       [FieldDataTypeEnum.NUMBER]: [{ key: FieldDisplayTypeEnum.TEXT_BOX, value: 'Textbox' }],
//       [FieldDataTypeEnum.INTEGER]: [{ key: FieldDisplayTypeEnum.TEXT_BOX, value: 'Textbox' }],
//       [FieldDataTypeEnum.DATETIME]: [
//         { key: FieldDisplayTypeEnum.DATE, value: 'Date' },
//         { key: FieldDisplayTypeEnum.TIME, value: 'Time' },
//         { key: FieldDisplayTypeEnum.DATETIME, value: 'Datetime' }
//       ],
//       [FieldDataTypeEnum.BOOLEAN]: [
//         { key: FieldDisplayTypeEnum.CHECKBOX, value: 'Checkbox' },
//         { key: FieldDisplayTypeEnum.SWITCH, value: 'Switch' }
//       ],
//       [FieldDataTypeEnum.SINGLECHOICE]: [{ key: FieldDisplayTypeEnum.DROPDOWN_LIST, value: 'Dropdownlist' }],
//       [FieldDataTypeEnum.MULTICHOICE]: [{ key: FieldDisplayTypeEnum.DROPDOWN_LIST, value: 'Dropdownlist' }],
//       [FieldDataTypeEnum.FILE]: [{ key: FieldDisplayTypeEnum.FILE, value: 'File' }],
//       [FieldDataTypeEnum.VALIDATE]: [{ key: FieldDisplayTypeEnum.TEXT_BOX, value: 'Textbox' }],
//       [FieldDataTypeEnum.SALE_CONTENT]: [{ key: FieldDisplayTypeEnum.LIST, value: 'List' }],
//       [FieldDataTypeEnum.ORG]: [{ key: FieldDisplayTypeEnum.LIST, value: 'List' }]
//     }

//     return optionsMap[fieldType] || []
//   }
}
