import { Injectable } from '@angular/core'
import { FormGroup } from '@angular/forms'
import { Answer } from '@features/sale-close-content/models'
import { FormSaleContentComponent } from '@features/sale-close-content/pages/create/form-sale-content/form-sale-content.component'

@Injectable({
  providedIn: 'root'
})
export class SharedService {
  constructor() {}

  /**
   *
   * @param form
   * @param saleContent
   * @param answers
   * @param onCreateData
   * @param validateAnswers
   * @param validateAllFields
   */
  onSave(
    form: FormGroup,
    saleContent: FormSaleContentComponent,
    answers: any[],
    onCreateData: (data: any) => void,
    validateAllFields: (form: FormGroup) => void
  ) {
    saleContent.validateForm()
    const formData = form.getRawValue()
    const body = {
      ...formData,
      isDecisive: String(formData.isDecisive).indexOf('1') !== -1,
      status: formData.status ? 1 : 0,
      answers: answers
    }
    let errorCreate = ''

    if (form.valid && saleContent.form.valid && saleContent.dataTable.length > 0) {
      if (body.isDecisive) {
        if (this.validateAnswers(answers)) {
          onCreateData(body)
        } else {
          errorCreate = 'Phải có ít nhất 1 câu trả lời mang tính quyết định'
        }
      } else {
        onCreateData(body)
      }
    } else {
      errorCreate = !saleContent.dataTable.length ? '1 câu hỏi có ít nhất 1 câu trả lời' : ''
      validateAllFields(form)
    }

    return errorCreate
  }

  validateAnswers(answers: Answer[]): boolean {
    return answers.some((answer) => answer.isDecisive === true)
  }
}
