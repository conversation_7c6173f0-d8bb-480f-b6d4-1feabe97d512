import { CheckboxItem, TextAreaItem } from '@shared'

export const txtAnswer = () =>
  new TextAreaItem({
    key: 'answerContent',
    label: '',
    placeholder: 'Nhập câu trả lời',
    value: '',
    minRow: '1',
    required: true,
    maxLength: 200,
    countMaxLength: true
  })

export const cbDecision = () =>
  new CheckboxItem({
    key: 'decision',
    label: '',
    layout: 'row wrap',
    checkBoxKey: 'key',
    hideValueCheckBox: true,
    options: [
      {
        key: '1',
        value: '1'
      }
    ],
    value: undefined,
    required: true
  })
