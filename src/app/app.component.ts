import { Component, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Router, RouterOutlet } from '@angular/router'
import { environment } from '@env/environment'
import { Store } from '@ngrx/store'
import { checkLoadMFE } from '@shared'
import { AppState, userInfoSelector } from '@store/reducers/app.reducer'
import { getUserInfoActionSuccess } from '@store/app.actions'
import Utils from './shared/utils/utils'
import { ToastComponent } from '@mb/ngx-ui-builder'
import { ActivityIndicatorComponent } from '@shared'
import { DomSanitizer } from '@angular/platform-browser'
import { MatIconRegistry } from '@angular/material/icon'
import { SVGIcons } from '../assets/images/svg-path/svg-path-object'
import { CommonModule } from '@angular/common';
import { DialPopupComponent } from '@features/web-call/dial-popup.component';
import { DialPopupService } from '@shared/components/navigation/header/header.component';

declare const require: any

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [RouterOutlet, ActivityIndicatorComponent, ToastComponent, CommonModule, DialPopupComponent]
})
export class AppComponent implements OnInit, OnDestroy {
  @HostListener('window:popstate')
  onPopState(event: any) {
    const url = location.href.replace(environment.base_url, '')
    this.router.navigateByUrl(url)
  }
  loginToken = ''
  ngVersion = require('../../package.json').dependencies['@angular/core']
  constructor(
    private router: Router,
    private store: Store<AppState>,
    private santinizer: DomSanitizer,
    private iconRegistry: MatIconRegistry,
    private dialPopupService: DialPopupService
  ) {
    this.addStyle()
  }

  ngOnInit(): void {
    Utils.logger(checkLoadMFE(), 'checkLoadMFE')
    if (checkLoadMFE()) {
      const url = location.href?.replace(location.origin, '')
      this.router.navigateByUrl(url)
    } else {
      const url = location.href?.replace(environment.base_url, '')
      this.router.navigateByUrl(url)
    }
    window.addEventListener('message', this.listener)
    this.store?.select(userInfoSelector).subscribe((res) => {
      this.loginToken = res?.token
    })
    // get user info login
    this.store.dispatch(new getUserInfoActionSuccess())
    //get SVGIcon form path
    for (const [iconName, iconSVG] of Object.entries(SVGIcons)) {
      this.iconRegistry.addSvgIconLiteral(iconName, this.santinizer.bypassSecurityTrustHtml(iconSVG))
    }
    // Subscribe to dial popup visibility changes
    this.dialPopupService.showDialPopup$.subscribe(show => {
      this.showDialPopup = show;
    });
  }

  listener = (event) => {
    const urlNavigation = location.href?.replace(location.origin, '')
    if (event && event.data.key == environment.key && event.data.url) {
      this.router.navigateByUrl(event.data.url)
    }
    window.removeEventListener('message', this.listener)
  }

  ngOnDestroy(): void {
    this.removeStyle()
  }

  addStyle() {
    var cssId = 'dcm-css'
    if (!document.getElementById(cssId)) {
      var head = document.getElementsByTagName('head')[0]
      var link = document.createElement('link')
      link.id = cssId
      link.rel = 'stylesheet'
      link.type = 'text/css'
      link.href = `${environment.base_url}/dcm-style.css`
      link.media = 'all'
      head.appendChild(link)
    }
  }
  removeStyle() {
    var cssId = 'dcm-css' // you could encode the css path itself to generate id..
    document.getElementById(cssId)?.remove()
  }

  // Dial popup visibility control
  showDialPopup = false;
}
