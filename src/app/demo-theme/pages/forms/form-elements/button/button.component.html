<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Buttons</mat-card-title>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <div class="row">
      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Basic</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button mat-button>Basic</button>
              <button mat-button color="primary">Primary</button>
              <button mat-button color="accent">Accent</button>
              <button mat-button color="warn">Warn</button>
              <button mat-button disabled>Disabled</button>
              <a mat-button href="https://www.google.com/" target="_blank"
                >Link</a
              >
            </div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Raised</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button mat-raised-button>Basic</button>
              <button mat-raised-button color="primary">Primary</button>
              <button mat-raised-button color="accent">Accent</button>
              <button mat-raised-button color="warn">Warn</button>
              <button mat-raised-button disabled>Disabled</button>
              <a
                mat-raised-button
                href="https://www.google.com/"
                target="_blank"
                >Link</a
              >
            </div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Stroke</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button mat-stroked-button>Basic</button>
              <button mat-stroked-button color="primary">Primary</button>
              <button mat-stroked-button color="accent">Accent</button>
              <button mat-stroked-button color="warn">Warn</button>
              <button mat-stroked-button disabled>Disabled</button>
              <a
                mat-stroked-button
                href="https://www.google.com/"
                target="_blank"
                >Link</a
              >
            </div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Flat</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button mat-flat-button>Basic</button>
              <button mat-flat-button color="primary">Primary</button>
              <button mat-flat-button color="accent">Accent</button>
              <button mat-flat-button color="warn">Warn</button>
              <button mat-flat-button disabled>Disabled</button>
              <a mat-flat-button href="https://www.google.com/" target="_blank"
                >Link</a
              >
            </div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Icon</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div class="d-flex">
              <button
                mat-icon-button
                class="d-flex"
                aria-label="Example icon button with a vertical three dot icon"
              >
                <i-tabler name="home-2" class="icon-20"></i-tabler>
              </button>
              <button
                mat-icon-button
                color="primary"
                class="d-flex"
                aria-label="Example icon button with a home icon"
              >
                <i-tabler name="adjustments-alt" class="icon-20"></i-tabler>
              </button>
              <button
                mat-icon-button
                color="accent"
                class="d-flex"
                aria-label="Example icon button with a menu icon"
              >
                <i-tabler name="apps" class="icon-20"></i-tabler>
              </button>
              <button
                mat-icon-button
                color="warn"
                class="d-flex"
                aria-label="Example icon button with a heart icon"
              >
                <i-tabler name="heart-filled" class="icon-20"></i-tabler>
              </button>
              <button
                mat-icon-button
                disabled
                class="d-flex"
                aria-label="Example icon button with a open in new tab icon"
              >
                <i-tabler name="message-2" class="icon-20"></i-tabler>
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Fab</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button
                mat-fab
                color="primary"
                aria-label="Example icon button with a delete icon"
              >
                <i-tabler name="trash"></i-tabler>
              </button>

              <button
                mat-fab
                color="accent"
                aria-label="Example icon button with a bookmark icon"
              >
                <i-tabler name="bookmark"></i-tabler>
              </button>

              <button
                mat-fab
                color="warn"
                aria-label="Example icon button with a home icon"
              >
                <i-tabler name="home-2"></i-tabler>
              </button>

              <button
                mat-fab
                disabled
                aria-label="Example icon button with a heart icon"
              >
                <i-tabler name="heart-filled"></i-tabler>
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Mini Fab</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button
                mat-mini-fab
                color="primary"
                aria-label="Example icon button with a delete icon"
              >
                <i-tabler class="icon-20" name="trash"></i-tabler>
              </button>

              <button
                mat-mini-fab
                color="accent"
                aria-label="Example icon button with a bookmark icon"
              >
                <i-tabler class="icon-20" name="bookmark"></i-tabler>
              </button>

              <button
                mat-mini-fab
                color="warn"
                aria-label="Example icon button with a home icon"
              >
                <i-tabler class="icon-20" name="home-2"></i-tabler>
              </button>

              <button
                mat-mini-fab
                disabled
                aria-label="Example icon button with a heart icon"
              >
                <i-tabler class="icon-20" name="heart-filled"></i-tabler>
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="col-lg-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Extended Fab</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <div
              class="d-flex flex-sm-row flex-column align-items-center justify-content-center gap-8"
            >
              <button
                mat-fab
                extended
                color="primary"
                aria-label="Example icon button with a delete icon"
              >
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-20 m-r-4" name="trash"></i-tabler
                  ><span> Delete</span>
                </div>
              </button>

              <button
                mat-fab
                extended
                color="accent"
                aria-label="Example icon button with a bookmark icon"
              >
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-20 m-r-4" name="bookmark"></i-tabler>
                  Bookmark
                </div>
              </button>

              <button
                mat-fab
                extended
                color="warn"
                aria-label="Example icon button with a home icon"
              >
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-20 m-r-4" name="home-2"></i-tabler> Home
                </div>
              </button>

              <button
                mat-fab
                extended
                disabled
                aria-label="Example icon button with a heart icon"
              >
                <div class="d-flex align-items-center">
                  <i-tabler
                    class="icon-20 m-r-4"
                    name="heart-filled"
                  ></i-tabler>
                  Heart
                </div>
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-card-content>
</mat-card>

<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Button Toggle</mat-card-title>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <div class="row">
      <div class="col-sm-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Basic</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <mat-button-toggle-group name="fontStyle" aria-label="Font Style">
              <mat-button-toggle value="bold">Bold</mat-button-toggle>
              <mat-button-toggle value="italic">Italic</mat-button-toggle>
              <mat-button-toggle value="underline">Underline</mat-button-toggle>
            </mat-button-toggle-group>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-sm-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Multiple Select</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <mat-button-toggle-group
              name="fontStyle"
              aria-label="Font Style"
              multiple
            >
              <mat-button-toggle value="flour">Flour</mat-button-toggle>
              <mat-button-toggle value="eggs">Eggs</mat-button-toggle>
              <mat-button-toggle value="sugar">Sugar</mat-button-toggle>
            </mat-button-toggle-group>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-sm-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Reactive Form</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <mat-button-toggle-group
              [formControl]="fontStyleControl"
              aria-label="Font Style"
            >
              <mat-button-toggle value="bold">Bold</mat-button-toggle>
              <mat-button-toggle value="italic">Italic</mat-button-toggle>
              <mat-button-toggle value="underline">Underline</mat-button-toggle>
            </mat-button-toggle-group>
            <p class="mat-subtitle-2 f-w-600 m-t-12">
              Chosen value is : {{ fontStyleControl.value }}
            </p>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="col-sm-6">
        <mat-card class="b-1 shadow-none">
          <mat-card-header>
            <mat-card-title>Exclusive selection</mat-card-title>
          </mat-card-header>
          <mat-card-content class="b-t-1">
            <mat-button-toggle-group #group="matButtonToggleGroup">
              <mat-button-toggle value="left" aria-label="Text align left">
                <mat-icon>format_align_left</mat-icon>
              </mat-button-toggle>
              <mat-button-toggle value="center" aria-label="Text align center">
                <mat-icon>format_align_center</mat-icon>
              </mat-button-toggle>
              <mat-button-toggle value="right" aria-label="Text align right">
                <mat-icon>format_align_right</mat-icon>
              </mat-button-toggle>
              <mat-button-toggle
                value="justify"
                disabled
                aria-label="Text align justify"
              >
                <mat-icon>format_align_justify</mat-icon>
              </mat-button-toggle>
            </mat-button-toggle-group>
            <div class="mat-subtitle-2 f-w-600 m-t-12">
              Selected value: {{ group.value }}
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-card-content>
</mat-card>
