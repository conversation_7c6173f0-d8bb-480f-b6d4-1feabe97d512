import { Component } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TablerIconsModule } from 'angular-tabler-icons'
import { MaterialModule } from 'src/app/demo-theme/shared/material.module'
import { <PERSON><PERSON><PERSON><PERSON>Toggle, MatButtonToggleGroup } from '@angular/material/button-toggle'

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule, MaterialModule, TablerIconsModule, MatButtonToggleGroup, MatButtonToggle],
  templateUrl: './button.component.html'
})
export class AppButtonComponent {
  constructor() {}

  //   reactive form
  fontStyleControl = new FormControl('')
  fontStyle?: string
}
