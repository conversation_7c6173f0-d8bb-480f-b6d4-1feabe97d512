import { Component } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MaterialModule } from 'src/app/demo-theme/shared/material.module'

@Component({
  selector: 'app-radio',
  standalone: true,
  imports: [MaterialModule, FormsModule, ReactiveFormsModule],
  templateUrl: './radio.component.html',
})
export class AppRadioComponent {
  constructor() {}

  //   ngModel
  favoriteSeason: string;
  seasons: string[] = ['Winter', 'Spring', 'Summer', 'Autumn'];
}
