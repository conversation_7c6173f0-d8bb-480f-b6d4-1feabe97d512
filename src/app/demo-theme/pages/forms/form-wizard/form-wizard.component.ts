import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms'
import { MaterialModule } from 'src/app/demo-theme/shared/material.module';
import { Mat<PERSON>tep, Mat<PERSON>tep<PERSON><PERSON>l, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t, MatStepperPrevious } from '@angular/material/stepper'

@Component({
  selector: 'app-form-wizard',
  standalone: true,
  imports: [MaterialModule, FormsModule, ReactiveFormsModule, MatStep, MatStepperPrevious, MatStepperNext, MatStepLabel, MatStepper],
  templateUrl: './form-wizard.component.html'
})
export class AppFormWizardComponent {
  firstFormGroup = this._formBuilder.group({
    firstCtrl: ['', Validators.required]
  })
  secondFormGroup = this._formBuilder.group({
    secondCtrl: ['', Validators.required]
  })

  constructor(private _formBuilder: FormBuilder) {}
}
