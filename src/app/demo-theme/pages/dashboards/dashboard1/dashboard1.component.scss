.dashboard-container {
  padding: 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;
}

.col-lg-8, .col-lg-4, .col-lg-6, .col-md-6 {
  padding: 10px;
}

.col-lg-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-lg-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.h-100 {
  height: 100%;
}

.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #4caf50;
}

.text-danger {
  color: #f44336;
}

// Welcome card
.welcome-card {
  background: linear-gradient(135deg, #635bff 0%, #8b75ff 100%);
  color: white;
  
  .mat-mdc-card-content {
    padding: 24px;
  }
  
  .welcome-text {
    flex: 1;
    
    h2 {
      font-size: 28px;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    p {
      font-size: 16px;
      margin-bottom: 24px;
      opacity: 0.8;
    }
    
    button {
      background-color: white !important;
      color: #635bff !important;
    }
  }
  
  .welcome-image {
    flex: 1;
    text-align: right;
    
    img {
      max-width: 100%;
      height: 180px;
    }
  }
}

// Stats cards
.stats-number {
  flex: 1;
  
  h2 {
    font-size: 24px;
    margin-bottom: 4px;
    font-weight: 500;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.stats-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(99, 91, 255, 0.1);
  margin-left: 16px;
  
  .mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

// Revenue chart
.revenue-chart {
  margin-bottom: 24px;
}

.chart-placeholder {
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 16px;
}

.small-chart {
  height: 120px;
}

.revenue-stats {
  .revenue-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    
    .revenue-label {
      color: #6c757d;
    }
    
    .revenue-value {
      font-weight: 500;
    }
  }
}

// Performance stats
.performance-stats {
  .performance-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .performance-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
      
      p {
        margin: 0;
        font-weight: 500;
      }
    }
  }
}
