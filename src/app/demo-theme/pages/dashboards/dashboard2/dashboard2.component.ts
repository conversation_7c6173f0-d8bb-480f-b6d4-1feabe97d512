import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { IconsModule } from 'src/app/shared/icons.module';

// Components
import { AppWeeklyScheduleComponent } from 'src/app/components/dashboard2/weekly-schedules/weekly-schedules.component';
import { AppProfileCardComponent } from 'src/app/components/dashboard2/profile-card/profile-card.component';
import { AppSubscriptionsComponent } from 'src/app/components/dashboard2/subscriptions/subscriptions.component';
import { AppRevenueForecastComponent } from 'src/app/components/dashboard2/revenue-forecast/revenue-forecast.component';
import { AppAnnualProfitComponent } from 'src/app/components/dashboard2/annual-profit/annual-profit.component';
import { AppNewCustomersComponent } from 'src/app/components/dashboard2/new-customers/new-customers.component';
import { AppRevenueProductComponent } from 'src/app/components/dashboard1/revenue-product/revenue-product.component';
import { AppBlogCardComponent } from 'src/app/components/dashboard2/blog-card/blog-card.component';
import { AppUsersComponent } from 'src/app/components/dashboard2/users/users.component';
import { AppSalesLocationsComponent } from 'src/app/components/dashboard2/sales-locations/sales-locations.component';
import { AppDailyActivitiesComponent } from 'src/app/components/dashboard2/daily-activities/daily-activities.component';
import { AppTotalIncomeComponent } from 'src/app/components/dashboard2/total-income/total-income.component';
import { AppWeeklyStatsComponent } from 'src/app/components/dashboard2/weekly-stats/weekly-stats.component';

@Component({
  selector: 'app-dashboard2',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    // Material modules
    MatCardModule,
    MatGridListModule,
    MatToolbarModule,
    MatMenuModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatListModule,
    // Components
    AppWeeklyScheduleComponent,
    AppProfileCardComponent,
    AppSubscriptionsComponent,
    AppRevenueForecastComponent,
    AppAnnualProfitComponent,
    AppNewCustomersComponent,
    AppRevenueProductComponent,
    AppBlogCardComponent,
    AppUsersComponent,
    AppSalesLocationsComponent,
    AppDailyActivitiesComponent,
    AppTotalIncomeComponent,
    AppWeeklyStatsComponent
  ],
  templateUrl: './dashboard2.component.html'
})
export class Dashboard2Component {
  constructor() {}
}
