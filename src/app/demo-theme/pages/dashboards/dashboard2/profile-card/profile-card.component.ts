import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-profile-card',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Profile Overview</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="profile-content">
          <div class="profile-info">
            <h3><PERSON></h3>
            <p>Full Stack Developer</p>
            <div class="profile-stats">
              <div class="stat-item">
                <mat-icon>people</mat-icon>
                <span>125</span>
                <span>Followers</span>
              </div>
              <div class="stat-item">
                <mat-icon>groups</mat-icon>
                <span>56</span>
                <span>Following</span>
              </div>
              <div class="stat-item">
                <mat-icon>emoji_events</mat-icon>
                <span>24</span>
                <span>Projects</span>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `, 
  styles: [`
    .profile-content {
      display: flex;
      align-items: center;
      gap: 24px;
    }
    
    .profile-info {
      flex: 1;
    }
    
    .profile-info h3 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .profile-info p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
    
    .profile-stats {
      display: flex;
      gap: 24px;
      margin-top: 24px;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-item mat-icon {
      font-size: 24px;
      color: #16cdc7;
      margin-bottom: 8px;
    }
    
    .stat-item span {
      display: block;
    }
    
    .stat-item span:first-child {
      font-size: 24px;
      font-weight: 500;
    }
    
    .stat-item span:last-child {
      font-size: 14px;
      color: #6c757d;
    }
  `]
})
export class AppProfileCardComponent {
  constructor() { }
}
