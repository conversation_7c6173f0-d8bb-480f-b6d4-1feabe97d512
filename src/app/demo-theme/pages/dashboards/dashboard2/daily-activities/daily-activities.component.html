<mat-card class="daily-activities-card">
  <mat-card-header>
    <mat-card-title>Daily Activities</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <mat-list>
      <mat-list-item *ngFor="let activity of activities">
        <mat-icon matListIcon>{{ activity.icon }}</mat-icon>
        <div matLine>{{ activity.title }}</div>
        <div matLine>{{ activity.description }}</div>
        <div class="time-stamp" matLine>{{ activity.time }}</div>
      </mat-list-item>
    </mat-list>
  </mat-card-content>
</mat-card>
