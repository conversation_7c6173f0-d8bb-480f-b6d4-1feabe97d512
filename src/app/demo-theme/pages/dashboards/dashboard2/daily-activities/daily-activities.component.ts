import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';

interface Activity {
  icon: string;
  title: string;
  description: string;
  time: string;
}

@Component({
  selector: 'app-daily-activities',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatListModule, MatIconModule],
  templateUrl: './daily-activities.component.html',
})
export class AppDailyActivitiesComponent {
  activities: Activity[] = [
    {
      icon: 'task_alt',
      title: 'Task Completed',
      description: 'Project milestone reached',
      time: '2 hours ago'
    },
    {
      icon: 'people',
      title: 'New Team Member',
      description: '<PERSON> joined the team',
      time: '4 hours ago'
    },
    {
      icon: 'chat',
      title: 'New Message',
      description: 'Received message from client',
      time: '6 hours ago'
    }
  ];

  constructor() { }
}
