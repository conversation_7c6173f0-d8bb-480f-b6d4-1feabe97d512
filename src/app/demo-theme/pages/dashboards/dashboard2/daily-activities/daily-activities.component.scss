@import '../../../style/variables';

.daily-activities-card {
  margin-bottom: 20px;
  
  .mat-mdc-card-header {
    padding: 24px;
    
    .mat-mdc-card-title {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 8px;
    }
  }
  
  .mat-mdc-card-content {
    padding: 24px;
    
    .mat-mdc-list {
      padding: 0;
      
      .mat-mdc-list-item {
        padding: 16px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        
        &:last-child {
          border-bottom: none;
        }
        
        .mat-mdc-list-item-content {
          display: flex;
          align-items: flex-start;
          
          .mat-mdc-list-item-text {
            margin-left: 16px;
            
            .mat-mdc-line {
              margin: 4px 0;
              
              &:last-child {
                color: #6c757d;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
