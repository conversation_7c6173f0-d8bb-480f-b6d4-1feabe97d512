import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MaterialModule } from '../../../../shared/material.module';

@Component({
  selector: 'app-blog-card',
  standalone: true,
  imports: [CommonModule, MatCardModule],
  templateUrl: './blog-card.component.html',
})
export class AppBlogCardComponent {
  title = 'Blog Post Title';
  subtitle = 'Posted on April 3, 2025';
  content = 'This is a sample blog post content. It can contain multiple lines of text and other HTML elements.';

  constructor() { }
}
