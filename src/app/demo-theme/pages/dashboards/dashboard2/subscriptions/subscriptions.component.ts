import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-subscriptions',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Subscriptions</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="subscriptions-content">
          <div class="subscription-item">
            <mat-icon>business</mat-icon>
            <div class="subscription-info">
              <h3>Business Plan</h3>
              <p>Active until April 30, 2025</p>
              <p class="price">$99.99/month</p>
            </div>
          </div>
          <div class="subscription-item">
            <mat-icon>person</mat-icon>
            <div class="subscription-info">
              <h3>Personal Plan</h3>
              <p>Expired on March 31, 2025</p>
              <p class="price">$29.99/month</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .subscriptions-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .subscription-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 8px;
      background: #f8f9fa;
    }
    
    .subscription-item mat-icon {
      font-size: 24px;
      color: #16cdc7;
    }
    
    .subscription-info h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
    }
    
    .subscription-info p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
    }
    
    .price {
      margin-top: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #16cdc7;
    }
  `]
})
export class AppSubscriptionsComponent {
  constructor() { }
}
