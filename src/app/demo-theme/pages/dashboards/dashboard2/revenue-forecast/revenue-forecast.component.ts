import { Component, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { NgApexchartsModule } from 'ng-apexcharts';

@Component({
  selector: 'app-revenue-forecast',
  standalone: true,
  imports: [CommonModule, MatCardModule, NgApexchartsModule],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Revenue Forecast</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div style="min-height: 300px;">
          <apx-chart [series]="revenueForecastChart.series" [chart]="revenueForecastChart.chart" [stroke]="revenueForecastChart.stroke" [fill]="revenueForecastChart.fill" [xaxis]="revenueForecastChart.xaxis"></apx-chart>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class AppRevenueForecastComponent {
  @ViewChild('chart') chart: any;
  public revenueForecastChart: any;

  constructor() {
    this.revenueForecastChart = {
      series: [
        {
          name: 'Revenue',
          data: [44, 55, 41, 67, 22, 43, 21, 49, 52, 51, 35, 41]
        }
      ],
      chart: {
        type: 'line',
        height: 300,
        fontFamily: "'Plus Jakarta Sans', sans-serif;",
        foreColor: '#adb0bb',
        toolbar: {
          show: false,
        },
        sparkline: {
          enabled: false,
        }
      },
      stroke: {
        width: 3,
        curve: 'smooth'
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          gradientToColors: ['#16cdc7'],
          shadeIntensity: 1,
          type: 'horizontal',
          opacityFrom: 0.7,
          opacityTo: 0.9,
          stops: [0, 100]
        }
      },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          style: {
            colors: '#adb0bb',
            fontSize: '12px'
          }
        }
      }
    };
  }
}
