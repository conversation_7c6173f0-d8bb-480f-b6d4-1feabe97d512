import { Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { MaterialModule } from '../../../../../shared/material.module'
import { IconsModule } from '../../../../../shared/icons.module'
import { CardComponent } from './card/card.component'

interface TopCard {
  id: number
  icon: string
  color: string
  title: string
  subtitle: string
}

@Component({
  selector: 'app-top-cards',
  standalone: true,
  imports: [CommonModule, MaterialModule, IconsModule, CardComponent],
  template: `
    <mat-card class="cardWithShadow">
      <mat-card-content>
        <div class="table-xs-responsive">
          <div class="row flex-nowrap">
            @for (topcard of topcards; track topcard.title) {
              <div class="col">
                <app-card [data]="topcard"></app-card>
              </div>
            }
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `
})
export class TopCardsComponent {
  topcards: TopCard[] = [
    {
      id: 1,
      color: 'primary',
      icon: 'currency-dollar',
      title: 'Tổng đơn hàng',
      subtitle: '16,689'
    },
    {
      id: 2,
      color: 'warning',
      icon: 'arrow-back-up',
      title: 'Hàng trả về',
      subtitle: '148'
    },
    {
      id: 3,
      color: 'accent',
      icon: 'backpack',
      title: 'Ngân sách năm',
      subtitle: '156K'
    },
    {
      id: 4,
      color: 'error',
      icon: 'x',
      title: 'Đơn hủy',
      subtitle: '64'
    },
    {
      id: 5,
      color: 'success',
      icon: 'chart-bar',
      title: 'Tổng thu nhập',
      subtitle: '36,715'
    }
  ]
}
