import { Component, Input } from '@angular/core'
import { CommonModule } from '@angular/common'
import { MaterialModule } from '../../../../../../shared/material.module'
import { IconsModule } from '../../../../../../shared/icons.module'

interface CardData {
  id: number
  icon: string
  color: string
  title: string
  subtitle: string
}

@Component({
  selector: 'app-card',
  standalone: true,
  imports: [CommonModule, MaterialModule, IconsModule],
  template: `
    <mat-card class="shadow-none text-center {{ data.color }}-gt shadow-none m-b-0">
      <mat-card-content class="p-32">
        <span class="icon-48 m-auto m-b-16 d-flex align-items-center justify-content-center rounded bg-{{ data.color }}">
          <i-tabler [name]="data.icon" class="text-white icon-27"></i-tabler>
        </span>
        <span class="mat-subtitle-2 f-s-14 f-w-400 text-dark text-nowrap">
          {{ data.title }}
        </span>
        <h6 class="m-t-4 mat-subtitle-1 f-s-22 f-w-600 m-b-16">
          {{ data.subtitle }}
        </h6>
        <button mat-raised-button class="f-w-500 f-s-12 btn-shadow text-nowrap border border-gray-200">Xem chi tiết</button>
      </mat-card-content>
    </mat-card>
  `
})
export class CardComponent {
  @Input() data!: CardData
}
