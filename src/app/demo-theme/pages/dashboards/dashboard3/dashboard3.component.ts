import { Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { MaterialModule } from '../../../../shared/material.module'
import { IconsModule } from '../../../../shared/icons.module'
import { TopCardsComponent } from './top-cards/top-cards.component'

interface TopCard {
  id: number
  icon: string
  color: string
  title: string
  subtitle: string
}

@Component({
  selector: 'app-dashboard3',
  standalone: true,
  imports: [CommonModule, MaterialModule, IconsModule, TopCardsComponent],
  template: `
    <div class="p-4">
      <h1 class="text-2xl font-medium mb-4">Dashboard 3</h1>

      <app-top-cards></app-top-cards>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
        <!-- Revenue Forecast -->
        <mat-card class="mat-elevation-z1">
          <mat-card-header>
            <mat-card-title>Revenue Forecast</mat-card-title>
          </mat-card-header>
          <mat-card-content class="p-4">
            <div class="flex justify-between mb-6">
              <div>
                <h3 class="text-3xl font-medium">$36,358</h3>
                <p class="text-gray-500">Total Revenue</p>
              </div>
              <div>
                <div class="flex items-center">
                  <div class="h-3 w-3 rounded-full bg-blue-500 mr-2"></div>
                  <span class="text-sm">This Year</span>
                </div>
                <div class="flex items-center mt-1">
                  <div class="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                  <span class="text-sm">Last Year</span>
                </div>
              </div>
            </div>
            <div class="h-64 flex items-end justify-between">
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-32 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-24 rounded-t-md w-3 inline-block"></div>
              </div>
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-48 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-40 rounded-t-md w-3 inline-block"></div>
              </div>
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-36 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-28 rounded-t-md w-3 inline-block"></div>
              </div>
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-52 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-44 rounded-t-md w-3 inline-block"></div>
              </div>
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-40 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-32 rounded-t-md w-3 inline-block"></div>
              </div>
              <div class="w-8 mx-1">
                <div class="bg-blue-500 h-56 rounded-t-md w-3 inline-block"></div>
                <div class="bg-green-500 h-48 rounded-t-md w-3 inline-block"></div>
              </div>
            </div>
            <div class="flex justify-between mt-2 text-sm text-gray-500">
              <span>Jan</span>
              <span>Feb</span>
              <span>Mar</span>
              <span>Apr</span>
              <span>May</span>
              <span>Jun</span>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Calendar -->
        <mat-card class="mat-elevation-z1">
          <mat-card-header>
            <mat-card-title>Calendar</mat-card-title>
          </mat-card-header>
          <mat-card-content class="p-4">
            <div class="flex justify-between items-center mb-4">
              <button mat-button>
                <i-tabler name="chevron-left" class="icon-24"></i-tabler>
              </button>
              <h3 class="text-lg font-medium">April 2025</h3>
              <button mat-button>
                <i-tabler name="chevron-right" class="icon-24"></i-tabler>
              </button>
            </div>

            <div class="grid grid-cols-7 gap-1 text-center">
              <div class="p-2 text-gray-500">Su</div>
              <div class="p-2 text-gray-500">Mo</div>
              <div class="p-2 text-gray-500">Tu</div>
              <div class="p-2 text-gray-500">We</div>
              <div class="p-2 text-gray-500">Th</div>
              <div class="p-2 text-gray-500">Fr</div>
              <div class="p-2 text-gray-500">Sa</div>

              <div class="p-2 text-gray-400">30</div>
              <div class="p-2 text-gray-400">31</div>
              <div class="p-2">1</div>
              <div class="p-2">2</div>
              <div class="p-2 bg-blue-500 text-white rounded-full">3</div>
              <div class="p-2">4</div>
              <div class="p-2">5</div>

              <div class="p-2">6</div>
              <div class="p-2">7</div>
              <div class="p-2">8</div>
              <div class="p-2">9</div>
              <div class="p-2">10</div>
              <div class="p-2 bg-green-500 text-white rounded-full">11</div>
              <div class="p-2">12</div>

              <div class="p-2">13</div>
              <div class="p-2">14</div>
              <div class="p-2">15</div>
              <div class="p-2">16</div>
              <div class="p-2">17</div>
              <div class="p-2">18</div>
              <div class="p-2">19</div>

              <div class="p-2">20</div>
              <div class="p-2 bg-purple-500 text-white rounded-full">21</div>
              <div class="p-2">22</div>
              <div class="p-2">23</div>
              <div class="p-2">24</div>
              <div class="p-2">25</div>
              <div class="p-2">26</div>

              <div class="p-2">27</div>
              <div class="p-2">28</div>
              <div class="p-2">29</div>
              <div class="p-2">30</div>
              <div class="p-2 text-gray-400">1</div>
              <div class="p-2 text-gray-400">2</div>
              <div class="p-2 text-gray-400">3</div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `
})
export class Dashboard3Component {
  constructor() {}
}
