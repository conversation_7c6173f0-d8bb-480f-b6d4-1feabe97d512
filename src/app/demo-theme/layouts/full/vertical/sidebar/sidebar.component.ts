import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output
} from '@angular/core';
import { BrandingComponent } from './branding.component';
import { IconsModule } from '../../../../../shared/icons.module';
import { MaterialModule } from '../../../../../shared/material.module';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [BrandingComponent, IconsModule, MaterialModule, CommonModule],
  templateUrl: './sidebar.component.html'
})
export class SidebarComponent implements OnInit {
  @Input() showToggle = true;
  @Output() toggleMobileNav = new EventEmitter<void>();
  @Output() toggleCollapsed = new EventEmitter<void>();

  constructor() { }

  ngOnInit(): void { }
}
