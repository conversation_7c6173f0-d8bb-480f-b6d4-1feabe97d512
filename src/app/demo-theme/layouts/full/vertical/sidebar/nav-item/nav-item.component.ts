import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NavItem } from './nav-item';
import { Router, RouterModule } from '@angular/router';
import { NavService } from '../../../../../services/nav.service';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MaterialModule } from '../../../../../../shared/material.module';
import { IconsModule } from '../../../../../../shared/icons.module';

@Component({
  selector: 'app-nav-item',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatListModule,
    MaterialModule,
    IconsModule
  ],
  templateUrl: './nav-item.component.html',
  styles: [`
    .sidebar-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      min-width: 24px;
    }
    
    .iconify {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
    
    .menu-list-item {
      display: flex;
      align-items: center;
      padding: 11px 16px;
      cursor: pointer;
      position: relative;
    }
    
    .menu-list-item.expanded {
      background-color: rgba(0, 0, 0, 0.04);
    }
    
    .sub-menu {
      padding-left: 16px;
    }
    
    .sidebar-nav-title {
      padding: 16px 16px 8px;
    }
    
    .sidebar-nav-cap {
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      color: rgba(0, 0, 0, 0.54);
    }
    
    .arrow-icon {
      display: flex;
      align-items: center;
    }
  `]
})
export class AppNavItemComponent implements OnChanges {
  @Input() item: NavItem | any;
  @Input() depth: number = 0;
  @Output() notify: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(public navService: NavService, public router: Router) {}

  ngOnChanges() {
    this.navService.currentUrl.subscribe((url: string) => {
      if (this.item.route && url) {
        // console.log(`${this.item.route} - ${url}`);
        this.expanded = url.indexOf(`${this.item.route}`) === 0;
        this.ariaExpanded = this.expanded;
      }
    });
  }

  expanded: boolean = false;
  disabled: boolean = false;
  ariaExpanded: boolean = false;

  handleClick() {
    this.notify.emit();
  }
}
