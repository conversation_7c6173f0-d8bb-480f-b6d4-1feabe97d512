<!-- Nav without child -->
<a *ngIf="!item.children && !item.navCap && !item.divider"
  class="sidebar-link hover:bg-primary-light rounded-md overflow-hidden"
  [routerLink]="item.route"
  routerLinkActive="bg-primary-light"
  [routerLinkActiveOptions]="{exact: true}"
  matRipple
  mat-list-item
  (click)="handleClick()"
>
  <span class="sidebar-icon">
    <span class="iconify" [attr.data-icon]="item.iconName"></span>
  </span>
  <span class="hide-menu">{{ item.displayName }}</span>
  <div class="sidebar-chip" *ngIf="item.chip">
    <span class="{{ item.chipClass }} rounded-full text-center py-1 px-2 text-xs">
      {{ item.chipContent }}
    </span>
  </div>
</a>

<!-- Nav Divider -->
<div class="sidebar-nav-title" *ngIf="item.divider">
  <span class="sidebar-nav-cap" *ngIf="item.navCap">{{ item.navCap }}</span>
</div>

<!-- Nav Caption without divider -->
<div class="sidebar-nav-title" *ngIf="item.navCap && !item.divider">
  <span class="sidebar-nav-cap">{{ item.navCap }}</span>
</div>

<!-- Nav with child -->
<a 
  *ngIf="item.children"
  mat-list-item
  (click)="expanded = !expanded"
  class="menu-list-item"
  [ngClass]="{'expanded': expanded}"
>
  <span class="sidebar-icon">
    <span class="iconify" [attr.data-icon]="item.iconName"></span>
  </span>
  <span class="hide-menu">{{ item.displayName }}</span>
  <span class="arrow-icon" style="margin-left: auto;">
    <mat-icon>{{ expanded ? 'expand_less' : 'expand_more' }}</mat-icon>
  </span>
  <div class="sidebar-chip" *ngIf="item.chip">
    <span class="{{ item.chipClass }} rounded-full text-center py-1 px-2 text-xs">
      {{ item.chipContent }}
    </span>
  </div>
</a>

<!-- Children items -->
<div class="sub-menu" *ngIf="expanded && item.children">
  <a
    mat-list-item
    class="sidebar-link hover:bg-primary-light rounded-md"
    *ngFor="let subitem of item.children"
    [routerLink]="subitem.route"
    routerLinkActive="bg-primary-light"
    [routerLinkActiveOptions]="{exact: true}"
    (click)="handleClick()"
  >
    <span class="sidebar-icon" *ngIf="subitem.subItemIcon">
      <span class="iconify" [attr.data-icon]="subitem.iconName"></span>
    </span>
    <span class="hide-menu">{{ subitem.displayName }}</span>
  </a>
</div>
