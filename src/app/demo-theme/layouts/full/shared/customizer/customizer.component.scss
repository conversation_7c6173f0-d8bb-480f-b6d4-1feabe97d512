.customizerNav {
  width: 300px;
  height: 100%;
  position: relative;
  overflow: auto;
}

.customizer-button-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  &.gap-16 {
    gap: 16px;
  }
  .mat-button-toggle {
    flex: 1;
    border-radius: 8px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  &.theme-colors {
    .mat-button-toggle {
      flex: 0 0 calc(33.33% - 8px);
      border-radius: 8px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      .theme-circle {
        width: 24px;
        height: 24px;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .theme-icon {
          display: none;
          color: #fff;
        }
      }
      &.mat-button-toggle-checked {
        .theme-icon {
          display: block;
        }
      }
    }
  }
}

.customizerBtn {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 1;
}
