import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { Router, NavigationEnd, ActivatedRoute, Data } from '@angular/router';
import { filter, map, mergeMap } from 'rxjs/operators';
import { IconsModule } from '@shared'

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [RouterModule, IconsModule],
  templateUrl: './breadcrumb.component.html',
})
export class AppBreadcrumbComponent {
  pageInfo: Data | any = Object.create(null);
  myurl: any = this.router.url.slice(1).split('/');

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private titleService: Title
  ) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .pipe(map(() => this.activatedRoute))
      .pipe(
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        })
      )
      .pipe(filter((route) => route.outlet === 'primary'))
      .pipe(mergeMap((route) => route.data))
      .subscribe((event) => {
        this.titleService.setTitle(event['title'] + ' - Angular 18');
        this.pageInfo = event;
      });
  }
}
