<span [dir]="options.dir!">
  <mat-sidenav-container
    class="mainWrapper"
    autosize
    autoFocus
    [ngClass]="{
      'sidebarNav-mini':
        options.sidenavCollapsed && options.navPos !== 'top' && !resView,
      'sidebarNav-horizontal': options.horizontal,
      cardBorder: options.cardBorder,
      orange_theme: options.activeTheme == 'orange_theme',
      blue_theme: options.activeTheme == 'blue_theme',
      aqua_theme: options.activeTheme == 'aqua_theme',
      purple_theme: options.activeTheme == 'purple_theme',
      green_theme: options.activeTheme == 'green_theme',
      cyan_theme: options.activeTheme == 'cyan_theme'
    }"
  >
    <!-- ============================================================== -->
    <!-- Vertical Sidebar -->
    <!-- ============================================================== -->
    <mat-sidenav
      *ngIf="!options.horizontal"
      #leftsidenav
      [mode]="isOver ? 'over' : 'side'"
      [opened]="
        options.navPos === 'side' &&
        options.sidenavOpened &&
        !isOver &&
        !resView
      "
      (openedChange)="onSidenavOpenedChange($event)"
      (closedStart)="onSidenavClosedStart()"
      class="sidebarNav"
    >
      <div class="flex-layout">
        <app-sidebar
          (toggleMobileNav)="leftsidenav.toggle()"
          [showToggle]="isOver"
        ></app-sidebar>
        <ng-scrollbar class="position-relative" style="height: 100%">
          <mat-nav-list class="sidebar-list">
            <ng-container *ngFor="let item of navItems">
              <app-nav-item [item]="item" (notify)="leftsidenav.toggle()">
              </app-nav-item>
            </ng-container>
          </mat-nav-list>
        </ng-scrollbar>
      </div>
    </mat-sidenav>

    <!-- ============================================================== -->
    <!-- Main Content -->
    <!-- ============================================================== -->
    <mat-sidenav-content class="contentWrapper" #content>
      <!-- ============================================================== -->
      <!-- VerticalHeader -->
      <!-- ============================================================== -->
      <app-header
        *ngIf="!options.horizontal"
        [showToggle]="!isOver"
        (toggleCollapsed)="toggleCollapsed()"
        (toggleMobileNav)="leftsidenav.toggle()"
      ></app-header>
      <div class="body-wrapper">
        <main
          class="pageWrapper"
          [ngClass]="{
            maxWidth: options.boxed
          }"
        >
          <!-- ============================================================== -->
          <!-- Outlet -->
          <!-- ============================================================== -->
          <router-outlet></router-outlet>
          <div class="customizerBtn">
            <button mat-fab color="warn" (click)="customizerRight.toggle()">
              <i-tabler name="settings" class="icon-20"></i-tabler>
            </button>
          </div>
        </main>
      </div>
    </mat-sidenav-content>

    <mat-sidenav #customizerRight mode="over" position="end">
      <div
        class="p-x-16 p-y-20 d-flex align-items-center justify-content-between"
      >
        <h3 class="mat-subtitle-1 f-s-21 f-w-600">Settings</h3>
        <button class="d-lg-none" mat-button (click)="customizerRight.toggle()">
          Close
        </button>
      </div>
      <mat-divider></mat-divider>

      <app-customizer (optionsChange)="receiveOptions($event)"></app-customizer>
    </mat-sidenav>
  </mat-sidenav-container>

  <!-- ------------------------------------------------------------------
    Mobile Apps Sidebar
    ------------------------------------------------------------------ -->
  <mat-drawer #filterNavRight mode="over" position="end" class="filter-sidebar">
    <div>
      <div class="d-flex justify-content-between align-items-center">
        <div class="branding">
          <a href="/">
            <img
              src="./assets/images/logos/logo.svg"
              class="align-middle m-2"
              alt="logo"
            />
          </a>
        </div>
        <button
          mat-icon-button
          (click)="filterNavRight.toggle()"
          class="d-flex justify-content-center"
        >
          <i-tabler name="x" class="icon-18 d-flex"></i-tabler>
        </button>
      </div>
    </div>
  </mat-drawer>
</span>
