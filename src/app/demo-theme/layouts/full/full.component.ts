import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ViewEncapsulation, HostListener } from '@angular/core'
import { Subscription } from 'rxjs'
import { MatSidenav } from '@angular/material/sidenav'
import { CoreService } from '../../services/core.service'
import { AppSettings } from '../../config'
import { navItems } from './vertical/sidebar/sidebar-data'
import { CommonModule } from '@angular/common'
import { RouterModule } from '@angular/router'
import { MaterialModule } from '../../../shared/material.module'
import { IconsModule } from '../../../shared/icons.module'
import { SidebarComponent } from './vertical/sidebar/sidebar.component'
import { HeaderComponent } from './vertical/header/header.component'
import { NgScrollbarModule } from 'ngx-scrollbar'
import { CustomizerComponent } from './shared/customizer/customizer.component'
import { AppNavItemComponent } from './vertical/sidebar/nav-item/nav-item.component'

@Component({
  selector: 'app-full-layout',
  templateUrl: './full.component.html',
  styleUrls: ['./full.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    IconsModule,
    SidebarComponent,
    HeaderComponent,
    NgScrollbarModule,
    CustomizerComponent,
    AppNavItemComponent
  ]
})
export class FullComponent implements OnInit, OnDestroy {
  navItems = navItems

  @ViewChild('leftsidenav')
  public leftsidenav: MatSidenav

  options: AppSettings
  private layoutChangesSubscription = Subscription.EMPTY
  private htmlElement!: HTMLHtmlElement
  public isOver = false
  public isTablet = false
  public isCollapsedWidthFixed = false
  public isContentWidthFixed = true
  public resView = false

  constructor(private settings: CoreService) {
    this.htmlElement = document.querySelector('html')!
    this.options = this.settings.getOptions()
    // Đảm bảo các tùy chọn cần thiết được khởi tạo
    if (!this.options.sidenavOpened) {
      this.options.sidenavOpened = true
    }
    if (!this.options.navPos) {
      this.options.navPos = 'side'
    }
    this.settings.setOptions(this.options)
  }

  ngOnInit(): void {
    this.layoutChangesSubscription = this.settings.notify.subscribe((options) => {
      this.options = options as AppSettings
      this.toggleDarkTheme(options as AppSettings)
    })

    this.isOver = window.innerWidth <= 1024
    this.isTablet = window.innerWidth <= 1024 && window.innerWidth > 767
    this.resView = window.innerWidth <= 1024
  }

  ngOnDestroy(): void {
    this.layoutChangesSubscription.unsubscribe()
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.handleResizeUpdate()
  }

  handleResizeUpdate() {
    this.isOver = window.innerWidth <= 1024
    this.isTablet = window.innerWidth <= 1024 && window.innerWidth > 767
    this.resView = window.innerWidth <= 1024

    if (window.innerWidth <= 1024) {
      this.isCollapsedWidthFixed = false
      this.isContentWidthFixed = false
    } else {
      this.isCollapsedWidthFixed = true
      this.isContentWidthFixed = true
    }
  }

  toggleSidenav(): void {
    if (this.leftsidenav) {
      this.leftsidenav.toggle()
    }
  }

  toggleCollapsed() {
    this.options.sidenavCollapsed = !this.options.sidenavCollapsed
    this.settings.setOptions(this.options)
  }

  receiveOptions(options: AppSettings): void {
    this.options = options
    this.settings.setOptions(options)
  }

  onSidenavClosedStart() {
    this.isCollapsedWidthFixed = false
    this.isContentWidthFixed = false
  }

  onSidenavOpenedChange(isOpened: boolean) {
    this.isCollapsedWidthFixed = !this.isOver
    this.options.sidenavOpened = isOpened
    this.settings.setOptions(this.options)
  }

  toggleDarkTheme(options: AppSettings) {
    if (options.theme === 'dark') {
      this.htmlElement.classList.add('dark-theme')
      this.htmlElement.classList.remove('light-theme')
    } else {
      this.htmlElement.classList.remove('dark-theme')
      this.htmlElement.classList.add('light-theme')
    }
  }
}
