import { Injectable } from '@angular/core';

interface Options {
  sidebarOpened: boolean;
  navbarFixed: boolean;
  dir: string;
  theme: string;
  cardBorder: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private options: Options = {
    sidebarOpened: true,
    navbarFixed: true,
    dir: 'ltr',
    theme: 'light',
    cardBorder: false
  };

  getOptions(): Options {
    return this.options;
  }

  setOptions(options: Options): void {
    this.options = options;
  }
}
