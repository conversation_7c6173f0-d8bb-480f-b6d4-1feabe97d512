import { Routes } from '@angular/router'
import { Dashboard1Component } from './pages/dashboards/dashboard1/dashboard1.component'
import { FullComponent } from './layouts/full/full.component'

export const DEMO_THEME_ROUTES: Routes = [
  {
    path: '',
    component: FullComponent,
    children: [
      {
        path: '',
        redirectTo: 'dashboards',
        pathMatch: 'full'
      },
      {
        path: 'dashboards',
        loadChildren: () => import('./pages/dashboards/dashboards.routes').then((m) => m.DashboardsRoutes)
      },
      {
        path: 'ui-components',
        loadChildren: () => import('./pages/ui-components/ui-components.routes').then((m) => m.UiComponentsRoutes)
      },
      {
        path: 'forms',
        loadChildren: () => import('./pages/forms/forms.routes').then((m) => m.FormsRoutes)
      },
      {
        path: 'charts',
        loadChildren: () => import('./pages/charts/charts.routes').then((m) => m.ChartsRoutes)
      },
      {
        path: 'apps',
        loadChildren: () => import('./pages/apps/apps.routes').then((m) => m.AppsRoutes)
      },
      {
        path: 'tables',
        loadChildren: () => import('./pages/tables/tables.routes').then((m) => m.TablesRoutes)
      },
      {
        path: 'authentication',
        loadChildren: () => import('./pages/authentication/authentication.routes').then((m) => m.AuthenticationRoutes)
      }
    ]
  }
]
