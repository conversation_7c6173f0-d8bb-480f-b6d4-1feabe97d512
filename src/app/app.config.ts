import { APP_INITIALIZER, ApplicationConfig, importProvidersFrom, isDevMode, LOCALE_ID, Provider } from '@angular/core'
import { provideRouter, Router } from '@angular/router'
import { appRoutes } from './app.routes'
import { APP_BASE_HREF, PlatformLocation, registerLocaleData } from '@angular/common'
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { CustomHttpInterceptor } from '@core/interceptors/keycloak.interceptor'
import { initializerKeycloak } from '@core/config/keycloak-initializer'
import { KeycloakAngularModule, KeycloakBearerInterceptor, KeycloakService } from 'keycloak-angular'
import { ConvertTreeService, HttpClientService, PipeModule } from '@shared'
import { SessionService } from './shared/services'
import { AppService } from '@services/app.service'
import { materialProviders } from '@core/config/material-config'
import { TranslateLoader, TranslateModule } from '@ngx-translate/core'
import { provideToastr } from 'ngx-toastr'
import { BrowserModule } from '@angular/platform-browser'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ThemeModule } from './theme/theme.module'
import { provideAnimations } from '@angular/platform-browser/animations'
import { FlexLayoutModule } from '@angular/flex-layout'
// Tạm thởi comment các import gây lỗi
// import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker'
// import { NgxMatMomentModule } from '@angular-material-components/moment-adapter'
import { UIBuilderModule } from '@mb/ngx-ui-builder'
import { AngularSvgIconModule } from 'angular-svg-icon'
import { StoreModule } from '@ngrx/store'
import { metaReducers, reducers } from '@store/reducers'
import { EffectsModule } from '@ngrx/effects'
import { AppEffects } from '@store/app.effects'
import { StoreDevtoolsModule } from '@ngrx/store-devtools'
import { MonacoEditorModule } from 'ngx-monaco-editor-v2'
import { TranslateHttpLoaderFactory, TranslateLangServiceFactory } from '@core/config/i18n.config'
import { MatDialogRef } from '@angular/material/dialog'
import { TranslateLangService } from '@core/services/translate-lang.service'
import { DBConfig, NgxIndexedDBModule } from 'ngx-indexed-db'
import { NgApexchartsModule } from 'ng-apexcharts'
import { MAT_TOOLTIP_DEFAULT_OPTIONS, MatTooltipDefaultOptions } from '@angular/material/tooltip'
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material'
registerLocaleData('vi-VN')
// Provider for Keycloak Bearer Interceptor
const KeycloakBearerInterceptorProvider: Provider = {
  provide: HTTP_INTERCEPTORS,
  useClass: KeycloakBearerInterceptor,
  multi: true
}

// Provider for Keycloak Initialization
const KeycloakInitializerProvider: Provider = {
  provide: APP_INITIALIZER,
  useFactory: initializerKeycloak,
  deps: [KeycloakService, HttpClientService, Router, ConvertTreeService, SessionService, AppService],
  multi: true
}

const toastrConfig = {
  timeOut: 5000,
  positionClass: 'toast-bottom-right',
  preventDuplicates: true
}

const matTooltipConfig : MatTooltipDefaultOptions = {
  showDelay: 500,
  hideDelay: 0,
  touchendHideDelay: 0
}

const dbConfig: DBConfig = {
  name: 'DbmsDB',
  version: 1,
  objectStoresMeta: [
    {
      store: 'componentConfigs',
      storeConfig: { keyPath: 'id', autoIncrement: false },
      storeSchema: [
        { name: 'createdBy', keypath: 'createdBy', options: { unique: false } },
        { name: 'createdAt', keypath: 'createdAt', options: { unique: false } },
        { name: 'updatedBy', keypath: 'updatedBy', options: { unique: false } },
        { name: 'updatedAt', keypath: 'updatedAt', options: { unique: false } },
        { name: 'component', keypath: 'component', options: { unique: false } },
        { name: 'dataVersion', keypath: 'dataVersion', options: { unique: false } },
        { name: 'data', keypath: 'data', options: { unique: false } }
      ]
    },
    {
      store: 'routerConfigs',
      storeConfig: { keyPath: 'id', autoIncrement: true }, // Không sử dụng autoIncrement
      storeSchema: [
        { name: 'routerName', keypath: 'routerName', options: { unique: true } },
        { name: 'values', keypath: 'values', options: { unique: false } }
      ]
    }
  ]
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes),
    provideHttpClient(withInterceptorsFromDi()), // Provides HttpClient with interceptors
    KeycloakInitializerProvider, // Initializes Keycloak
    KeycloakBearerInterceptorProvider, // Provides Keycloak Bearer Interceptor
    KeycloakService, // Service for Keycloak
    importProvidersFrom(
      BrowserModule,
      FormsModule,
      ReactiveFormsModule,
      ThemeModule,
      HttpClientModule,
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: TranslateHttpLoaderFactory,
          deps: [HttpClient]
        }
      }),
      KeycloakAngularModule,
      FlexLayoutModule,
      UIBuilderModule,
      AngularSvgIconModule.forRoot({}),
      NgxIndexedDBModule.forRoot(dbConfig),
      PipeModule.forRoot(),
      StoreModule.forRoot(reducers, { metaReducers }),
      EffectsModule.forRoot([AppEffects]),
      StoreDevtoolsModule.instrument({
        maxAge: 25,
        logOnly: !isDevMode(),
        autoPause: true,
        trace: true,
        traceLimit: 75 // maximum stack trace frames to be stored (in case trace option was provided as true)
      }),
      MonacoEditorModule.forRoot(),
      NgApexchartsModule,
      NgxDaterangepickerMd.forRoot(),
    ),
    provideAnimations(),
    provideToastr(toastrConfig),
    {
      provide: APP_BASE_HREF,
      useFactory: (s: PlatformLocation) => s.getBaseHrefFromDOM(),
      deps: [PlatformLocation]
    },
    {
      provide: APP_INITIALIZER,
      useFactory: TranslateLangServiceFactory,
      deps: [TranslateLangService],
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: CustomHttpInterceptor,
      multi: true
    },
    {
      provide: MatDialogRef,
      useValue: {}
    },
    {
      provide: MAT_TOOLTIP_DEFAULT_OPTIONS,
      useValue: matTooltipConfig
    },
    {
      provide: LOCALE_ID,
      useValue: 'vi'
    },
    materialProviders
  ]
}
