import { Routes } from '@angular/router'
import { environment } from '@env/environment'
import { LayoutComponent } from '@shared/components/layout/layout.component'
import { AccessDeniedComponent } from '@pages/access-denied/access-denied.component'
import { NotFoundComponent } from '@pages/not-found/not-found.component'
import { RESOURCE_CODE, Scopes } from '@shared'
import { RoleGuard } from '@core/guards/role.guard'

export const ROUTES_NAME_PRODUCT = {
  LIST: environment.base_path + '/category/product',
  CREATE: environment.base_path + '/category/product/create',
  EDIT: environment.base_path + '/category/product/edit'
}

export const ROUTES_NAME_BUSINESS = {
  LIST: environment.base_path + '/category/business',
  CREATE: environment.base_path + '/category/product/create',
  EDIT: environment.base_path + '/category/product/edit'
}

export const ROUTES_NAME_SERVICE = {
  LIST: environment.base_path + '/category/user',
  CREATE: environment.base_path + '/category/user/create',
  EDIT: environment.base_path + '/category/user/edit'
}

export const ROUTES_NAME_FORM = {
  LIST: environment.base_path + '/category/form',
  CREATE: environment.base_path + '/category/form/create',
  EDIT: environment.base_path + '/category/form/edit'
}

export const ROUTES_NAME_GROUP = {
  LIST: environment.base_path + '/system-management/group',
  CREATE: environment.base_path + '/system-management/group/create',
  EDIT: environment.base_path + '/system-management/group/edit'
}

export const ROUTES_NAME_ORG = {
  LIST: environment.base_path + '/system-management/org',
  EDIT: environment.base_path + '/system-management/org/edit'
}

export const ROUTES_NAME_DASHBOARD = {
  DASHBOARD: `${environment.base_path}/dashboard`
}

export const ROUTES_NAME_SALE_CLOSE_CONTENT = {
  LIST: `${environment.base_path}/campaign/sale-close-content`,
  EDIT: `${environment.base_path}/campaign/sale-close-content/edit`,
  CREATE: `${environment.base_path}/campaign/sale-close-content/create`
}
export const ROUTES_NAME_CAMPAIGN = {
  LIST: environment.base_path + '/voice-blaster/campaign',
  EDIT: environment.base_path + '/voice-blaster/campaign/edit',
  COPY: environment.base_path + '/campaign/campaign/copy',
  ASSIGN: environment.base_path + '/campaign/campaign/assign',
  JOBPROFILE: environment.base_path + '/campaign/job-profile',
  CREATE_INTERACT: environment.base_path + '/campaign/interact/create',
  PROFILE_IN_CAMPAIGN: environment.base_path + '/campaign/profile-assigned/profile-in-campaign'
}

export const ROUTES_NAME_CUSTOMER = {
  LIST: environment.base_path + '/customer-management/customer',
  VIEW: environment.base_path + '/customer-management/customer' // /{:customerCode}
}

export const ROUTES_NAME_AUDIO = {
  LIST: environment.base_path + '/audio-management/audio',
  VIEW: environment.base_path + '/audio-management/audio' // /{:customerCode}
}

export const ROUTES_NAME_BLACKLIST = {
  LIST: environment.base_path + '/customer-management/blacklist',
  EDIT: environment.base_path + '/customer-management/blacklist' // /{:id}
}
export const ROUTES_NAME_INTERACT = {
  LIST: environment.base_path + '/request/interact',
  EDIT: environment.base_path + '/request/interact/edit', // /{:id}
  DETAIL: environment.base_path + '/request/interact/detail' // /{:id}
}

export const ROUTES_NAME_JOB_SCHEDULE_JOB = {
  LIST: environment.base_path + '/system-management/job-schedule',
  CREATE: environment.base_path + '/system-management/job-schedule/create',
  EDIT: environment.base_path + '/system-management/job-schedule/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_USER = {
  LIST: environment.base_path + '/system-management/user',
  CREATE: environment.base_path + '/system-management/user/create',
  EDIT: environment.base_path + '/system-management/user/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_CALL_HISTORY = {
  LIST: environment.base_path + '/system-management/call-history',
  CREATE: environment.base_path + '/system-management/user/create',
  EDIT: environment.base_path + '/system-management/user/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_DISPATCHER = {
  LIST: environment.base_path + '/system-management/dispatcher',
  CREATE: environment.base_path + '/system-management/dispatcher/create',
  EDIT: environment.base_path + '/system-management/dispatcher/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_VOICE_BRAND_NAME = {
  LIST: environment.base_path + '/system-management/voice-brand-name',
  CREATE: environment.base_path + '/system-management/voice-brand-name/create',
  EDIT: environment.base_path + '/system-management/voice-brand-name/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_ADDRESS = {
  LIST: environment.base_path + '/system-management/address',
  CREATE: environment.base_path + '/system-management/address/create',
  EDIT: environment.base_path + '/system-management/address/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_IVR_MENU = {
  LIST: environment.base_path + '/system-management/ivr-menu',
  CREATE: environment.base_path + '/system-management/ivr-menu/create',
  EDIT: environment.base_path + '/system-management/ivr-menu/edit'
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_DIAL_PLAN = {
  LIST: environment.base_path + '/system-management/dialplan',
  CREATE: environment.base_path + '/system-management/dialplan/create',
  EDIT: environment.base_path + '/system-management/dialplan/edit'
}

export const ROUTES_NAME_CALL_FEE = {
  LIST: environment.base_path + '/call-fee',
  CREATE: environment.base_path + '/call-fee/create',
  EDIT: environment.base_path + '/call-fee/edit'
}

// @ts-ignore
export const appRoutes: Routes = [
  {
    path: '',
    redirectTo: ROUTES_NAME_DASHBOARD.DASHBOARD,
    pathMatch: 'full'
  },
  {
    path: environment.base_path,
    redirectTo: ROUTES_NAME_DASHBOARD.DASHBOARD,
    pathMatch: 'full'
  },
  {
    path: 'demo-theme',
    data: { breadcrumb: 'Demo Theme' },
    loadChildren: async () => (await import('./demo-theme/demo-theme.routes')).DEMO_THEME_ROUTES
  },
  {
    path: environment.base_path,
    component: LayoutComponent,
    children: [
      {
        path: 'dashboard',
        data: { breadcrumb: 'Dashboard' },
        // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
        loadChildren: async () => (await import('@features/dashboard/dashboard.routes')).routes
      },
      {
        path: 'system-management',
        data: { breadcrumb: 'Quản trị hệ thống' },
        children: [
          {
            path: 'user',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/user/user.routes')).routes,
            // canActivate: [RoleGuard],
            data: {
              breadcrumb: 'User',
              resources: [RESOURCE_CODE.USER_MANAGEMENT_USERS],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'group',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/group/group.routes')).routes,
            data: {
              breadcrumb: 'Quản lý nhóm người dùng',
              resources: [RESOURCE_CODE.USER_MANAGEMENT_GROUPS],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'call-history',
            // canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/call-history/call-history.routes')).routes,
            data: {
              breadcrumb: 'Lịch sử cuộc gọi',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_CALL_HISTORY],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'v2/call-history',
            // canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/call-history-v2/call-history-v2.routes')).routes,
            data: {
              breadcrumb: 'Lịch sử cuộc gọi',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_CALL_HISTORY],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'call-recording',
            // canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/recording-file/recording-file.routes')).routes,
            data: {
              breadcrumb: 'Quản lý ghi âm cuộc gọi',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_DIALPLAN],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'dispatcher',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/dispatcher/dispatcher.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý Dispatcher',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_DISPATCHER],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'telco',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/voice-brand-name/voice-brand-name.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý nhà mạng',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_TELCO_PROVIDER],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'address',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/address/address.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý đầu số',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_NUMBER_MANAGEMENT],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'phone-number-history',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/phone-number-history/phone-number-history.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Lịch sử đầu số',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_NUMBER_HISTORY_MANAGEMENT],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'phone-number-overview',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/address-overview/address-overview.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Kho đầu số ',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_PHONE_NUMBER_OVERVIEW],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'ivr-menu',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/ivr-menu/ivr.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý IVR Menu',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_IVR_MENU],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'dialplan',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/dialplan/dialplan.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý Dialplan',
              resources: [RESOURCE_CODE.SYSTEM_MANAGEMENT_DIALPLAN],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'job-schedule',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/job-shedule/job-schedule.routes')).routes,
            data: { breadcrumb: 'Quản lý tiến trình', resources: [RESOURCE_CODE.JOB_SCHEDULE_MANAGEMENT], scopes: [Scopes.VIEW] }
          },
          // {
          //   path: 'data-management',
          //   // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
          //   loadChildren: async () => (await import('@features/data-management/data-management.routes')).routes,
          //   data: { breadcrumb: 'Quản lý import dữ liệu' }
          // },
          {
            path: 'sip-profile',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/sip-profile/sip-profile.routes')).routes,
            data: { breadcrumb: 'Quản lý sip profile', resources: [RESOURCE_CODE.SIP_PROFILE], scopes: [Scopes.VIEW] }
          },
          {
            path: 'config-builder',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/builder-config/builder-config.routes')).routes,
            data: { breadcrumb: 'Quản lý cấu hình' }
          }
        ]
      },
      {
        path: 'data-call',
        data: { breadcrumb: 'Quản lý nguồn gọi' },
        children: [
          {
            path: 'source',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/customer_source/customer-source.routes')).routes,
            data: {
              breadcrumb: 'Nguồn'
              // resources: [RESOURCE_CODE.CUSTOMER], scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'customer',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/customer/customer.routes')).routes,
            data: {
              breadcrumb: 'Khách hàng'
              // resources: [RESOURCE_CODE.CUSTOMER], scopes: [Scopes.VIEW]
            }
          }
          // {
          //   path: 'blacklist',
          //   canActivate: [RoleGuard],
          //   loadChildren: async () => (await import('@features/blacklist/blacklist.routes')).routes,
          //   data: { breadcrumb: 'Hạn chế liên hệ',
          //     // resources: [RESOURCE_CODE.BLACKLIST], scopes: [Scopes.VIEW]
          //   }
          // }
        ]
      },
      {
        path: 'campaign',
        data: { breadcrumb: 'Quản lý Campaign' },
        children: [
          {
            path: 'common-fields',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/common-fields/common-fields.routes')).routes,
            data: {
              breadcrumb: 'Trường dữ liệu cố định',
              resources: [RESOURCE_CODE.CAMPAIGN_COMMON_FIELDS],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'sale-close-content',
            canActivate: [RoleGuard],

            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/sale-close-content/sale-close-content.routes')).routes,
            data: { breadcrumb: 'Nội dung chốt sale', resources: [RESOURCE_CODE.CAMPAIGN_SALE_CLOSE_CONTENT], scopes: [Scopes.VIEW] }
          },
          {
            path: 'campaign',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/campaign/campaign.routes')).routes,
            data: {
              breadcrumb: 'Campaign',
              resources: [RESOURCE_CODE.CAMPAIGN_CAMPAIGN],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'profile-assigned',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/profile-assigned/profile-assigned.routes')).routes,
            data: { breadcrumb: 'Hồ sơ được phân giao', resources: [RESOURCE_CODE.CAMPAIGN_PROFILE_ASSIGNED], scopes: [Scopes.VIEW] }
          },
          {
            path: 'all-profile',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/all-profile/all-profile.routes')).routes,
            data: { breadcrumb: 'Hồ sơ tổng', resources: [RESOURCE_CODE.CAMPAIGN_ALL_PROFILE], scopes: [Scopes.VIEW] }
          },
          {
            path: 'job-profile',
            canActivate: [RoleGuard],
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/job-profile/job-profile.routes')).routes,
            data: { breadcrumb: 'Hồ sơ tác nghiệp', resources: [RESOURCE_CODE.CAMPAIGN_JOB_PROFILE], scopes: [Scopes.VIEW] }
          }
        ]
      },
      {
        path: 'voice-blaster',
        // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
        data: { breadcrumb: 'Voice blaster' },
        children: [
          {
            path: 'campaign',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/campaign-voice-blaster/campaign.routes')).routes,
            data: {
              breadcrumb: 'Quản lý Campaign',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          },
          {
            path: 'voice-script',
            // canMatch: [checkLoadMFE() ? AuthGuard : AuthKeycloakGuard],
            loadChildren: async () => (await import('@features/voice-scripts/voice-script.routes')).routes,
            canActivate: [RoleGuard],
            data: {
              breadcrumb: 'Quản lý kịch bản cuộc gọi',
              resources: [RESOURCE_CODE.MANAGEMENT_VOICE_SCRIPT],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      },
      {
        path: 'audio-management',
        data: { breadcrumb: 'quản lý bản ghi audio' },
        children: [
          {
            path: '',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/audio-management/audio-management.routes')).routes,
            data: {
              breadcrumb: '',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      },
      {
        path: 'auto-call',
        data: { breadcrumb: 'Test tính năng auto call' },
        children: [
          {
            path: '',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/test-auto-call/auto-call.routes')).routes,
            data: {
              breadcrumb: '',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      },
      {
        path: 'call-fee',
        data: { breadcrumb: 'Cấu hình cước' },
        children: [
          {
            path: '',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/call-fee/call-fee.routes')).routes,
            data: {
              breadcrumb: '',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      },
      {
        path: 'billing',
        data: { breadcrumb: 'Báo cáo cước' },
        children: [
          {
            path: '',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/call-fee-report/call-fee-report.routes')).routes,
            data: {
              breadcrumb: '',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      },
      {
        path: 'ws',
        data: { breadcrumb: 'Test tính năng auto call' },
        children: [
          {
            path: '',
            canActivate: [RoleGuard],
            loadChildren: async () => (await import('@features/websocket-sip/dashboard.routes')).routes,
            data: {
              breadcrumb: '',
              resources: [],
              scopes: [Scopes.VIEW]
            }
          }
        ]
      }
    ]
  },
  {
    path: `${environment.base_path}/access-denied`,
    component: AccessDeniedComponent
  },
  {
    path: '**',
    component: NotFoundComponent
  }
]
