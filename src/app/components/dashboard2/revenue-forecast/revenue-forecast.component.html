<mat-card class="cardWithShadow">
  <mat-card-content>
    <div class="hstack align-items-center flex-column flex-lg-row">
      <div class="w-100">
        <mat-card-title>Revenue Forecast</mat-card-title>
        <mat-card-subtitle>Overview of Profit</mat-card-subtitle>
      </div>
      <div class="m-l-auto hstack">
        <div class="d-flex">
          <i-tabler
            name="circle-filled"
            class="text-primary icon-10"
          ></i-tabler>
          <div class="m-l-8">
            <span class="mat-subtitle-2">2023</span>
          </div>
        </div>
        <div class="d-flex">
          <i-tabler name="circle-filled" class="text-error icon-10"></i-tabler>
          <div class="m-l-8">
            <span class="mat-subtitle-2">2024</span>
          </div>
        </div>
      </div>
    </div>

    <!-- chart -->
    <div class="m-t-16 rounded-bars" style="height: 305px">
      <apx-chart
        [series]="revenueForecastChart.series"
        [chart]="revenueForecastChart.chart"
        [colors]="revenueForecastChart.colors"
        [plotOptions]="revenueForecastChart.plotOptions"
        [dataLabels]="revenueForecastChart.dataLabels"
        [legend]="revenueForecastChart.legend"
        [grid]="revenueForecastChart.grid"
        [yaxis]="revenueForecastChart.yaxis"
        [xaxis]="revenueForecastChart.xaxis"
        [tooltip]="revenueForecastChart.tooltip"
      ></apx-chart>
    </div>

    <div class="row">
      @for(performanceList of performanceLists; track performanceList.title) {
      <div class="col-sm-4">
        <div class="hstack align-items-center m-t-20">
          <div
            class="rounded icon-48 bg-{{
              performanceList.color
            }} d-flex align-items-center flex-shrink-0 justify-content-center"
          >
            <span
              class="iconify f-s-24 text-{{ performanceList.textcolor }}"
              [attr.data-icon]="performanceList.icon"
            ></span>
          </div>
          <div>
            <span class="mat-body-1">{{ performanceList.subtext }}</span>
            <h6 class="mat-subtitle-1 f-s-18 f-w-600 text-nowrap">
              {{ performanceList.title }}
            </h6>
          </div>
        </div>
      </div>
      }
    </div>
  </mat-card-content>
</mat-card>
