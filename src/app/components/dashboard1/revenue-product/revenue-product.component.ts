import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

interface month {
  value: string;
  viewValue: string;
}

export interface productsData {
  id: number;
  imagePath: string;
  uname: string;
  position: string;
  hrate: number;
  skills: string;
  priority: string;
  progress: string;
}

const ELEMENT_DATA: productsData[] = [
  {
    id: 1,
    imagePath: 'assets/images/products/dash-prd-1.jpg',
    uname: 'Minecraf App',
    position: '<PERSON>',
    skills: '3.5',
    hrate: 73.2,
    priority: 'Low',
    progress: '60%'
  }
];

@Component({
  selector: 'app-revenue-product',
  templateUrl: './revenue-product.component.html',
  styleUrls: ['./revenue-product.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatMenuModule,
    MatButtonModule,
    MatIconModule
  ]
})
export class AppRevenueProductComponent {
  displayedColumns: string[] = ['imagePath', 'uname', 'position', 'hrate', 'skills', 'priority', 'progress'];
  dataSource = ELEMENT_DATA;
  months: month[] = [
    { value: 'jan', viewValue: 'January' },
    { value: 'feb', viewValue: 'February' },
    { value: 'mar', viewValue: 'March' },
    { value: 'apr', viewValue: 'April' },
    { value: 'may', viewValue: 'May' },
    { value: 'jun', viewValue: 'June' },
    { value: 'jul', viewValue: 'July' },
    { value: 'aug', viewValue: 'August' },
    { value: 'sep', viewValue: 'September' },
    { value: 'oct', viewValue: 'October' },
    { value: 'nov', viewValue: 'November' },
    { value: 'dec', viewValue: 'December' }
  ];
  selectedMonth: month | null = null;

  selectMonth(month: month): void {
    this.selectedMonth = month;
  }
}
