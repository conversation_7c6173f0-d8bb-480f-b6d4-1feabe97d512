<div class="card">
  <div class="card-header">
    <h5 class="card-title mb-0">Revenue by Product</h5>
  </div>
  <div class="card-body">
    <div class="d-flex justify-content-between mb-3">
      <div class="dropdown">
        <button mat-button [matMenuTriggerFor]="menu">
          {{selectedMonth?.viewValue || 'Select Month'}}
          <mat-icon>arrow_drop_down</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item *ngFor="let month of months" (click)="selectMonth(month)">
            {{month.viewValue}}
          </button>
        </mat-menu>
      </div>
    </div>
    
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th *ngFor="let column of displayedColumns">{{column}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataSource">
            <td>
              <div class="d-flex align-items-center">
                <img [src]="item.imagePath" alt="" class="rounded-circle me-2" width="30" height="30">
                <div>
                  <h6 class="mb-0">{{item.uname}}</h6>
                  <small class="text-muted">{{item.position}}</small>
                </div>
              </div>
            </td>
            <td>{{item.progress}}</td>
            <td>{{item.priority}}</td>
            <td>{{item.hrate}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
