ubject: [PATCH] fix: limit choose enddate
---
Index: src/app/shared/components/data-input/date-range-control/date-range-control.component.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/app/shared/components/data-input/date-range-control/date-range-control.component.ts b/src/app/shared/components/data-input/date-range-control/date-range-control.component.ts
--- a/src/app/shared/components/data-input/date-range-control/date-range-control.component.ts	(revision b68fbb345217270ccb897b68608a38abe3c62d9e)
+++ b/src/app/shared/components/data-input/date-range-control/date-range-control.component.ts	(date 1726038249254)
@@ -59,9 +59,10 @@
     closeOnAutoApply: false,
     showCustomRangeLabel: false,
   }
-
-  minDate: dayjs.Dayjs = dayjs().subtract(48, 'month') //TODO
-  maxDate: dayjs.Dayjs = dayjs().add(48, 'month') // TODO
+  //TODO: neu phat sinh nghiep vu khac khong phai 90 thi dua ra config
+  // 90 ngay - 2 vi ko tinh start và end
+  minDate: dayjs.Dayjs = dayjs().subtract(90 - 2, 'day').startOf('day')
+  maxDate: dayjs.Dayjs = dayjs()
   locale: LocaleConfig = {
     format: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
     displayFormat: 'DD/MM/YYYY',
@@ -92,17 +93,21 @@
     //   const { start , end } = this.f[this.item.key].value
     //   if (start && end)
     //   {
+    //     const startDayJs = dayjs(start).startOf('day')
     //     const durationInDays = end.diff(start, 'day');
+    //     console.log('durationInDays', durationInDays)
     //     //TODO: đưa ra cấu hình để sử dụng với item.maxDate
-    //     if (durationInDays >= 90) {
-    //       // Cập nhật lại giá trị của end trong form control
-    //       const endDayjs = start.add(90 - 3, 'day').endOf('day');
-    //       this.f[this.item.key].patchValue({
-    //         end: endDayjs
-    //       }, { emitEvent: true });
-    //       console.log('*****', this.f[this.item.key].value)
-    //       // this.errorDateRange = durationInDays > 90
-    //     }
+    //     // if (durationInDays >= 90) {
+    //     //   // Cập nhật lại giá trị của end trong form control
+    //     //   const endDayjs = startDayJs.add(90 - 2, 'day').endOf('day');
+    //     //   console.log('startDayJs', startDayJs)
+    //     //   console.log('endDayjs', endDayjs)
+    //     //   this.f[this.item.key].patchValue({
+    //     //     end: endDayjs
+    //     //   }, { emitEvent: true });
+    //     //   console.log('*****', this.f[this.item.key].value)
+    //     //   // this.errorDateRange = durationInDays > 90
+    //     // }
     //   }
     // })
   }
