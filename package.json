{"name": "mb-ng-started", "version": "0.0.1", "scripts": {"ng": "ng", "start": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng serve --port 4200", "dev": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng serve --port 4200 --live-reload=false --host 0.0.0.0", "build": "ng build --configuration uat --aot --output-hashing=all", "build-dev": "ng build --configuration dev --aot --output-hashing=all", "build-prod": "ng build --configuration production --aot --output-hashing=all", "build:lib": "ng build lib", "postbuild:lib": "copyfiles README.md dist/lib/ && cd projects/dynamic-builder-ui && npm run build", "hmr": "ng serve --port 4200 --hmr", "serve:sw": "npx angular-http-server --path dist -p 4200 --baseHref pbx", "serve-dev:sw": "npm run build-dev && npx angular-http-server --path dist -p 4200 --baseHref pbx", "test": "ng test --source-map=false", "lint": "tsc --noEmit && eslint . --ext js,ts,json", "lint-fix": "tsc --noEmit && eslint . --ext ts,html --fix", "lint-fix-html": "tsc --noEmit && eslint . --ext html --fix", "e2e": "ng e2e", "prepare": "husky install", "compodoc": "compodoc -p tsconfig.doc.json", "compodoc:build": "compodoc -p tsconfig.doc.json", "compodoc:build-and-serve": "compodoc -p tsconfig.doc.json -s", "compodoc:serve": "compodoc -s", "format": "prettier src/**/*.{js,ts,css,scss,json,html} --write .", "format-ts": "prettier src/**/*.ts --write .", "generate-css-auto-complete": "sass --watch src/styles.scss src/styles.css"}, "private": true, "overrides": {"call-bind": "1.0.2"}, "dependencies": {"@angular-architects/module-federation": "^17.0.8", "@angular/animations": "^18.0.3", "@angular/cdk": "^18.0.3", "@angular/common": "^18.0.3", "@angular/compiler": "^18.0.3", "@angular/core": "^18.0.3", "@angular/elements": "^18.0.3", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^18.0.3", "@angular/localize": "^18.0.3", "@angular/material": "^18.0.3", "@angular/material-moment-adapter": "^18.0.3", "@angular/platform-browser": "^18.0.3", "@angular/platform-browser-dynamic": "^18.0.3", "@angular/router": "^18.0.3", "@datical/dat-resizable-column": "^0.0.1", "@ng-select/ng-select": "^12.0.7", "@ngrx/component": "^17.1.1", "@ngrx/component-store": "^17.1.1", "@ngrx/effects": "^17.1.1", "@ngrx/entity": "^17.1.1", "@ngrx/store": "^17.1.1", "@ngrx/store-devtools": "^17.1.1", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@sbzen/ng-cron": "^17.0.0", "angular-split": "^17.0.0", "angular-svg-icon": "^17.0.0", "angular-tabler-icons": "^2.7.0", "apexcharts": "^3.49.2", "bootstrap": "^5.3.3", "bpmn-js": "^17.2.1", "bpmn-js-properties-panel": "^5.14.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "echarts": "^5.6.0", "file-saver": "^2.0.5", "immer": "^10.0.4", "jquery": "^3.7.1", "jssip": "^3.10.1", "keycloak-angular": "^15.2.1", "keycloak-js": "^24.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.47.0", "ng-apexcharts": "1.10.0", "ng2-pdf-viewer": "^10.2.2", "ngrx-wieder": "^12.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-drag-drop": "^17.0.0", "ngx-echarts": "^19.0.0", "ngx-indexed-db": "^18.0.0", "ngx-mat-timepicker": "^17.1.0", "ngx-monaco-editor-v2": "^17.0.1", "ngx-scrollbar": "^14.0.0", "ngx-sse-client": "^18.0.0", "ngx-toastr": "^18.0.0", "rxjs": "~7.8.1", "sass-loader": "^14.1.1", "sip.js": "^0.21.2", "tslib": "^2.6.2", "uuid": "^9.0.1", "webpack-dev-server": "^5.0.4", "webrtc-adapter": "^8.2.3", "xlsx": "^0.18.5", "zone.js": "~0.14.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.2", "@angular-devkit/build-angular": "^18.0.3", "@angular-eslint/eslint-plugin": "18.0.0", "@angular-eslint/eslint-plugin-template": "18.0.0", "@angular-eslint/schematics": "18.0.0", "@angular-eslint/template-parser": "18.0.0", "@angular/cli": "^18.0.3", "@angular/compiler-cli": "^18.0.3", "@module-federation/fmr": "^0.0.7", "@ngrx/eslint-plugin": "^17.1.1", "@ngrx/schematics": "^17.1.1", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.4", "@types/lodash": "^4.17.0", "@types/node": "^20.12.3", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "7.5.0", "body-parser": "^1.20.2", "copyfiles": "^2.4.1", "cors": "^2.8.5", "eslint": "8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "5.1.3", "express": "^4.19.2", "html-prettify": "1.0.7", "htmlparser": "1.7.7", "husky": "^9.0.11", "jasmine-core": "~5.1.2", "json-format": "^1.0.1", "json-server": "1.0.0-alpha.23", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "3.2.5", "prettier-eslint": "^16.3.0", "pretty-quick": "^4.0.0", "protractor": "7.0.0", "sanitize-html": "2.13.0", "ts-node": "10.9.2", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "~5.4.3"}}